(()=>{var e={};e.id=778,e.ids=[778],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10452:(e,s,t)=>{"use strict";t.d(s,{default:()=>P});var r=t(60687),a=t(43210),i=t(85814),l=t.n(i),n=t(44493),d=t(29523),o=t(96834),c=t(89667),m=t(15079),u=t(91821),x=t(58376),p=t(63213),h=t(19080),f=t(88059),g=t(5336),y=t(11860),j=t(93613),v=t(99270),b=t(80462),w=t(71057),N=t(40228),A=t(13861),k=t(31158);let P=()=>{let{isAuthenticated:e}=(0,p.A)(),[s,t]=(0,a.useState)([]),[i,P]=(0,a.useState)([]),[q,C]=(0,a.useState)(!0),[R,M]=(0,a.useState)(null),[_,S]=(0,a.useState)(""),[L,H]=(0,a.useState)("all"),[O,G]=(0,a.useState)("newest");(0,a.useEffect)(()=>{(async()=>{if(e)try{C(!0);let e=await x.Qo.getOrders();e.success&&e.data?(t(e.data),P(e.data)):M("Failed to fetch orders")}catch(e){console.error("Error fetching orders:",e),M("Failed to load orders")}finally{C(!1)}})()},[e]),(0,a.useEffect)(()=>{let e=[...s];_&&(e=e?.filter(e=>e._id?.toLowerCase().includes(_.toLowerCase())||e.orderItems.some(e=>e.product.title?.toLowerCase().includes(_.toLowerCase())))),"all"!==L&&(e=e.filter(e=>e.status===L)),e.sort((e,s)=>{switch(O){case"newest":return new Date(s.createdAt).getTime()-new Date(e.createdAt).getTime();case"oldest":return new Date(e.createdAt).getTime()-new Date(s.createdAt).getTime();case"amount-high":return s.totalRevenue-e.totalRevenue;case"amount-low":return e.totalRevenue-s.totalRevenue;default:return 0}}),P(e)},[s,_,L,O]);let z=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),T=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"shipped":return"bg-purple-100 text-purple-800";case"delivered":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},U=e=>{switch(e){case"pending":default:return(0,r.jsx)(h.A,{className:"w-4 h-4"});case"shipped":return(0,r.jsx)(f.A,{className:"w-4 h-4"});case"delivered":return(0,r.jsx)(g.A,{className:"w-4 h-4"});case"cancelled":return(0,r.jsx)(y.A,{className:"w-4 h-4"})}};return e?q?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,r.jsx)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3 mb-4"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-8"}),(0,r.jsx)("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded"},e))})]})})}):R?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,r.jsx)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)(u.Fc,{variant:"destructive",children:[(0,r.jsx)(j.A,{className:"h-4 w-4"}),(0,r.jsx)(u.TN,{children:R})]})})}):(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,r.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Order History"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Track and manage your orders"})]}),(0,r.jsx)(n.Zp,{className:"mb-6",children:(0,r.jsx)(n.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,r.jsx)(c.p,{placeholder:"Search orders...",value:_,onChange:e=>S(e.target.value),className:"pl-10"})]}),(0,r.jsxs)(m.l6,{value:L,onValueChange:H,children:[(0,r.jsx)(m.bq,{children:(0,r.jsx)(m.yv,{placeholder:"Filter by status"})}),(0,r.jsxs)(m.gC,{children:[(0,r.jsx)(m.eb,{value:"all",children:"All Orders"}),(0,r.jsx)(m.eb,{value:"pending",children:"Pending"}),(0,r.jsx)(m.eb,{value:"shipped",children:"Shipped"}),(0,r.jsx)(m.eb,{value:"delivered",children:"Delivered"}),(0,r.jsx)(m.eb,{value:"cancelled",children:"Cancelled"})]})]}),(0,r.jsxs)(m.l6,{value:O,onValueChange:G,children:[(0,r.jsx)(m.bq,{children:(0,r.jsx)(m.yv,{placeholder:"Sort by"})}),(0,r.jsxs)(m.gC,{children:[(0,r.jsx)(m.eb,{value:"newest",children:"Newest First"}),(0,r.jsx)(m.eb,{value:"oldest",children:"Oldest First"}),(0,r.jsx)(m.eb,{value:"amount-high",children:"Amount: High to Low"}),(0,r.jsx)(m.eb,{value:"amount-low",children:"Amount: Low to High"})]})]}),(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,r.jsx)(b.A,{className:"w-4 h-4 mr-2"}),i.length," of ",s.length," orders"]})]})})}),0===i.length?(0,r.jsx)(n.Zp,{children:(0,r.jsxs)(n.Wu,{className:"flex flex-col items-center justify-center py-12 text-center",children:[(0,r.jsx)(w.A,{className:"w-16 h-16 text-gray-400 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:0===s.length?"No orders yet":"No orders found"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:0===s.length?"Start shopping to see your orders here":"Try adjusting your search or filter criteria"}),0===s.length&&(0,r.jsx)(d.$,{asChild:!0,children:(0,r.jsx)(l(),{href:"/shop",children:"Start Shopping"})})]})}):(0,r.jsx)("div",{className:"space-y-4",children:i.map(e=>(0,r.jsx)(n.Zp,{className:"hover:shadow-md transition-shadow",children:(0,r.jsxs)(n.Wu,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"font-semibold",children:["Order #",e._id.slice(-8)]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Placed on ",z(e.createdAt)]})]}),(0,r.jsxs)(o.E,{className:`${T(e.status)} flex items-center space-x-1`,children:[U(e.status),(0,r.jsxs)("span",{children:["Order Status: ",e.status]})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"font-semibold text-lg",children:["$",e.totalRevenue.toFixed(2)]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[e.orderItems.length," item",1!==e.orderItems.length?"s":""]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[e.orderItems.slice(0,3).map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-md flex items-center justify-center",children:(0,r.jsx)(h.A,{className:"w-6 h-6 text-gray-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium truncate max-w-32",children:e.product.title}),(0,r.jsxs)("p",{className:"text-xs text-gray-600",children:["Qty: ",e.quantity]})]})]},s)),e.orderItems.length>3&&(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:["+",e.orderItems.length-3," more items"]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,r.jsx)(N.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"text-xs",children:"Delivery date will be updated soon"})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(d.$,{variant:"outline",size:"sm",asChild:!0,children:(0,r.jsxs)(l(),{href:`/order-confirmation/${e._id}`,children:[(0,r.jsx)(A.A,{className:"w-4 h-4 mr-2"}),"View Details"]})}),(0,r.jsxs)(d.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(k.A,{className:"w-4 h-4 mr-2"}),"Receipt"]})]})]})]})},e._id))})]})}):null}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15079:(e,s,t)=>{"use strict";t.d(s,{bq:()=>u,eb:()=>f,gC:()=>h,l6:()=>c,yv:()=>m});var r=t(60687),a=t(43210),i=t(25911),l=t(78272),n=t(3589),d=t(13964),o=t(4780);let c=i.bL;i.YJ;let m=i.WT,u=a.forwardRef(({className:e,children:s,...t},a)=>(0,r.jsxs)(i.l9,{ref:a,className:(0,o.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[s,(0,r.jsx)(i.In,{asChild:!0,children:(0,r.jsx)(l.A,{className:"h-4 w-4 opacity-50"})})]}));u.displayName=i.l9.displayName;let x=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.PP,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,r.jsx)(n.A,{className:"h-4 w-4"})}));x.displayName=i.PP.displayName;let p=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.wn,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,r.jsx)(l.A,{className:"h-4 w-4"})}));p.displayName=i.wn.displayName;let h=a.forwardRef(({className:e,children:s,position:t="popper",...a},l)=>(0,r.jsx)(i.ZL,{children:(0,r.jsxs)(i.UC,{ref:l,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...a,children:[(0,r.jsx)(x,{}),(0,r.jsx)(i.LM,{className:(0,o.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,r.jsx)(p,{})]})}));h.displayName=i.UC.displayName,a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.JU,{ref:t,className:(0,o.cn)("px-2 py-1.5 text-sm font-semibold",e),...s})).displayName=i.JU.displayName;let f=a.forwardRef(({className:e,children:s,...t},a)=>(0,r.jsxs)(i.q7,{ref:a,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[(0,r.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(i.VF,{children:(0,r.jsx)(d.A,{className:"h-4 w-4"})})}),(0,r.jsx)(i.p4,{children:s})]}));f.displayName=i.q7.displayName,a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.wv,{ref:t,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=i.wv.displayName},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21678:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\components\\\\checkout\\\\OrderHistoryPage.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\checkout\\OrderHistoryPage.tsx","default")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},34346:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o,metadata:()=>d});var r=t(37413),a=t(61120),i=t(21678),l=t(55118),n=t(17215);let d={title:"Order History - Mega Mall | Track Your Orders",description:"View your order history, track current orders, and manage your purchases with Mega Mall.",keywords:"order history, track orders, order status, mega mall orders",openGraph:{title:"Order History - Mega Mall | Track Your Orders",description:"View your order history and track current orders.",type:"website",url:"/orders"},robots:{index:!1,follow:!1}};function o(){return(0,r.jsx)(n.default,{children:(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)(l.default,{}),children:(0,r.jsx)(i.default,{})})})}},35329:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var r=t(65239),a=t(48088),i=t(88170),l=t.n(i),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let o={children:["",{children:["orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,34346)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\orders\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\orders\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/orders/page",pathname:"/orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},40228:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},46547:(e,s,t)=>{Promise.resolve().then(t.bind(t,72080)),Promise.resolve().then(t.bind(t,10452)),Promise.resolve().then(t.bind(t,66981))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71057:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("shopping-bag",[["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}],["path",{d:"M3.103 6.034h17.794",key:"awc11p"}],["path",{d:"M3.4 5.467a2 2 0 0 0-.4 1.2V20a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6.667a2 2 0 0 0-.4-1.2l-2-2.667A2 2 0 0 0 17 2H7a2 2 0 0 0-1.6.8z",key:"o988cm"}]])},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80462:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,s,t)=>{"use strict";t.d(s,{p:()=>i});var r=t(60687);t(43210);var a=t(4780);function i({className:e,type:s,...t}){return(0,r.jsx)("input",{type:s,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},93403:(e,s,t)=>{Promise.resolve().then(t.bind(t,55118)),Promise.resolve().then(t.bind(t,21678)),Promise.resolve().then(t.bind(t,17215))},94735:e=>{"use strict";e.exports=require("events")},99270:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,162,658,893,367,781],()=>t(35329));module.exports=r})();