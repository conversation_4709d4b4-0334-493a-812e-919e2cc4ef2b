(()=>{var e={};e.id=122,e.ids=[122],e.modules={1270:(e,t,r)=>{Promise.resolve().then(r.bind(r,7956))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7956:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminProtectedRoute.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminProtectedRoute.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16189:(e,t,r)=>{"use strict";var s=r(65773);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22542:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(37413),i=r(7956);function n(){return(0,s.jsx)(i.default,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Settings"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Configure system settings, site preferences, and administrative options."})]}),(0,s.jsxs)("div",{className:"bg-card p-8 rounded-lg border border-border text-center",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Settings Panel Coming Soon"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"System configuration and settings management will be available in the next update."})]})]})})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},48126:(e,t,r)=>{Promise.resolve().then(r.bind(r,73482))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73482:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var s=r(60687);r(43210);var i=r(58873),n=r(91821),o=r(93613),a=r(99891);let d=(e,t)=>"admin"===t?"admin"===e.role||"superAdmin"===e.role:"superAdmin"===t&&"superAdmin"===e.role,u=({children:e,requiredRole:t,fallback:r})=>{let{admin:u,isAuthenticated:l,isLoading:c}=(0,i.b)();return c?r||(0,s.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})}):l&&u?t&&!d(u,t)?r||(0,s.jsxs)(n.Fc,{variant:"destructive",children:[(0,s.jsx)(a.A,{className:"h-4 w-4"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{children:"Insufficient Permissions"}),(0,s.jsxs)("p",{children:["You need ",t," privileges to access this page. Your current role is: ",u.role]})]})]}):(0,s.jsx)(s.Fragment,{children:e}):r||(0,s.jsxs)(n.Fc,{variant:"destructive",children:[(0,s.jsx)(o.A,{className:"h-4 w-4"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{children:"Access Denied"}),(0,s.jsx)("p",{children:"You must be logged in as an admin to access this page."})]})]})}},74075:e=>{"use strict";e.exports=require("zlib")},76965:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>l,routeModule:()=>p,tree:()=>u});var s=r(65239),i=r(48088),n=r(88170),o=r.n(n),a=r(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let u={children:["",{children:["admin",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,22542)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,99111)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\settings\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/settings/page",pathname:"/admin/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,162,658,367,10],()=>r(76965));module.exports=s})();