(()=>{var e={};e.id=895,e.ids=[895],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3589:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14952:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},14993:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var s=r(60687);r(43210);var a=r(44493);let i=()=>(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:Array.from({length:12}).map((e,t)=>(0,s.jsxs)(a.Zp,{className:"w-full h-full shadow-md border-0 bg-card overflow-hidden",children:[(0,s.jsx)("div",{className:"relative aspect-square bg-muted animate-pulse"}),(0,s.jsxs)(a.Wu,{className:"p-4 space-y-3",children:[(0,s.jsx)("div",{className:"h-3 bg-muted rounded animate-pulse w-1/3"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-full"}),(0,s.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-3/4"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"flex gap-1",children:Array.from({length:5}).map((e,t)=>(0,s.jsx)("div",{className:"w-4 h-4 bg-muted rounded animate-pulse"},t))}),(0,s.jsx)("div",{className:"h-3 bg-muted rounded animate-pulse w-12"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{className:"h-5 bg-muted rounded animate-pulse w-16"}),(0,s.jsx)("div",{className:"h-3 bg-muted rounded animate-pulse w-12"})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("div",{className:"h-3 bg-muted rounded animate-pulse w-full"}),(0,s.jsx)("div",{className:"h-3 bg-muted rounded animate-pulse w-2/3"})]})]})]},t))})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20572:(e,t,r)=>{Promise.resolve().then(r.bind(r,17215)),Promise.resolve().then(r.bind(r,77399)),Promise.resolve().then(r.bind(r,36002))},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30300:(e,t,r)=>{Promise.resolve().then(r.bind(r,66981)),Promise.resolve().then(r.bind(r,14993)),Promise.resolve().then(r.bind(r,99253))},33873:e=>{"use strict";e.exports=require("path")},34771:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>o,revalidate:()=>c});var s=r(37413),a=r(61120),i=r(36002),n=r(17215),l=r(77399);let o={title:"Shop - Mega Mall | Premium Products & Best Deals",description:"Discover our extensive collection of premium products at unbeatable prices. Shop electronics, fashion, home goods, and more with fast shipping and excellent customer service.",keywords:"shop, products, electronics, fashion, home goods, deals, online shopping, mega mall",openGraph:{title:"Shop - Mega Mall | Premium Products & Best Deals",description:"Discover our extensive collection of premium products at unbeatable prices.",type:"website",url:"/shop",images:[{url:"/og-shop.jpg",width:1200,height:630,alt:"Mega Mall Shop - Premium Products"}]},twitter:{card:"summary_large_image",title:"Shop - Mega Mall | Premium Products & Best Deals",description:"Discover our extensive collection of premium products at unbeatable prices.",images:["/og-shop.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}},c=3600;function d(){return(0,s.jsx)(n.default,{children:(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)(u,{}),children:(0,s.jsx)(i.default,{})})})}function u(){return(0,s.jsx)("div",{className:"min-h-screen bg-background",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsxs)("div",{className:"space-y-6 mb-8",children:[(0,s.jsxs)("div",{className:"text-center space-y-2",children:[(0,s.jsx)("div",{className:"h-10 bg-muted rounded animate-pulse w-64 mx-auto"}),(0,s.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-96 mx-auto"})]}),(0,s.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 items-center justify-between",children:[(0,s.jsx)("div",{className:"h-10 bg-muted rounded animate-pulse w-80"}),(0,s.jsx)("div",{className:"h-10 bg-muted rounded animate-pulse w-48"})]})]}),(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,s.jsx)("aside",{className:"lg:w-64 flex-shrink-0",children:(0,s.jsx)("div",{className:"h-96 bg-muted rounded-xl animate-pulse"})}),(0,s.jsx)("main",{className:"flex-1",children:(0,s.jsx)(l.default,{})})]})]})})}},36002:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\components\\\\shop\\\\ShopPage.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\shop\\ShopPage.tsx","default")},47033:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},54300:(e,t,r)=>{"use strict";r.d(t,{J:()=>o});var s=r(60687),a=r(43210),i=r(14163),n=a.forwardRef((e,t)=>(0,s.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=r(4780);function o({className:e,...t}){return(0,s.jsx)(n,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67969:(e,t,r)=>{"use strict";function s(e,[t,r]){return Math.min(r,Math.max(t,e))}r.d(t,{q:()=>s})},74075:e=>{"use strict";e.exports=require("zlib")},77399:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\components\\\\shop\\\\LoadingGrid.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\shop\\LoadingGrid.tsx","default")},78272:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79551:e=>{"use strict";e.exports=require("url")},80462:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},81630:e=>{"use strict";e.exports=require("http")},83721:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(43210);function a(e){let t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},83997:e=>{"use strict";e.exports=require("tty")},88920:(e,t,r)=>{"use strict";r.d(t,{N:()=>v});var s=r(60687),a=r(43210),i=r(12157),n=r(72789),l=r(15124),o=r(21279),c=r(18171),d=r(32582);class u extends a.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=(0,c.s)(e)&&e.offsetWidth||0,s=this.props.sizeRef.current;s.height=t.offsetHeight||0,s.width=t.offsetWidth||0,s.top=t.offsetTop,s.left=t.offsetLeft,s.right=r-s.width-s.left}return null}componentDidUpdate(){}render(){return this.props.children}}function m({children:e,isPresent:t,anchorX:r,root:i}){let n=(0,a.useId)(),l=(0,a.useRef)(null),o=(0,a.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:c}=(0,a.useContext)(d.Q);return(0,a.useInsertionEffect)(()=>{let{width:e,height:s,top:a,left:d,right:u}=o.current;if(t||!l.current||!e||!s)return;let m="left"===r?`left: ${d}`:`right: ${u}`;l.current.dataset.motionPopId=n;let p=document.createElement("style");c&&(p.nonce=c);let x=i??document.head;return x.appendChild(p),p.sheet&&p.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${s}px !important;
            ${m}px !important;
            top: ${a}px !important;
          }
        `),()=>{x.removeChild(p),x.contains(p)&&x.removeChild(p)}},[t]),(0,s.jsx)(u,{isPresent:t,childRef:l,sizeRef:o,children:a.cloneElement(e,{ref:l})})}let p=({children:e,initial:t,isPresent:r,onExitComplete:i,custom:l,presenceAffectsLayout:c,mode:d,anchorX:u,root:p})=>{let h=(0,n.M)(x),f=(0,a.useId)(),g=!0,v=(0,a.useMemo)(()=>(g=!1,{id:f,initial:t,isPresent:r,custom:l,onExitComplete:e=>{for(let t of(h.set(e,!0),h.values()))if(!t)return;i&&i()},register:e=>(h.set(e,!1),()=>h.delete(e))}),[r,h,i]);return c&&g&&(v={...v}),(0,a.useMemo)(()=>{h.forEach((e,t)=>h.set(t,!1))},[r]),a.useEffect(()=>{r||h.size||!i||i()},[r]),"popLayout"===d&&(e=(0,s.jsx)(m,{isPresent:r,anchorX:u,root:p,children:e})),(0,s.jsx)(o.t.Provider,{value:v,children:e})};function x(){return new Map}var h=r(86044);let f=e=>e.key||"";function g(e){let t=[];return a.Children.forEach(e,e=>{(0,a.isValidElement)(e)&&t.push(e)}),t}let v=({children:e,custom:t,initial:r=!0,onExitComplete:o,presenceAffectsLayout:c=!0,mode:d="sync",propagate:u=!1,anchorX:m="left",root:x})=>{let[v,y]=(0,h.xQ)(u),j=(0,a.useMemo)(()=>g(e),[e]),b=u&&!v?[]:j.map(f),w=(0,a.useRef)(!0),N=(0,a.useRef)(j),P=(0,n.M)(()=>new Map),[A,k]=(0,a.useState)(j),[C,S]=(0,a.useState)(j);(0,l.E)(()=>{w.current=!1,N.current=j;for(let e=0;e<C.length;e++){let t=f(C[e]);b.includes(t)?P.delete(t):!0!==P.get(t)&&P.set(t,!1)}},[C,b.length,b.join("-")]);let R=[];if(j!==A){let e=[...j];for(let t=0;t<C.length;t++){let r=C[t],s=f(r);b.includes(s)||(e.splice(t,0,r),R.push(r))}return"wait"===d&&R.length&&(e=R),S(g(e)),k(j),null}let{forceRender:M}=(0,a.useContext)(i.L);return(0,s.jsx)(s.Fragment,{children:C.map(e=>{let a=f(e),i=(!u||!!v)&&(j===C||b.includes(a));return(0,s.jsx)(p,{isPresent:i,initial:(!w.current||!!r)&&void 0,custom:t,presenceAffectsLayout:c,mode:d,root:x,onExitComplete:i?void 0:()=>{if(!P.has(a))return;P.set(a,!0);let e=!0;P.forEach(t=>{t||(e=!1)}),e&&(M?.(),S(N.current),u&&y?.(),o&&o())},anchorX:m,children:e},a)})})}},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(60687);r(43210);var a=r(4780);function i({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},90745:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c={children:["",{children:["shop",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,34771)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\shop\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\shop\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/shop/page",pathname:"/shop",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},93613:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},93661:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},94735:e=>{"use strict";e.exports=require("events")},99253:(e,t,r)=>{"use strict";r.d(t,{default:()=>ev});var s=r(60687),a=r(43210),i=r(54351),n=r(93853),l=r(9732),o=r(29523),c=r(47033),d=r(93661),u=r(14952);let m=({currentPage:e,totalPages:t,onPageChange:r})=>{if(t<=1)return null;let a=(()=>{let r=[],s=[];for(let s=Math.max(2,e-2);s<=Math.min(t-1,e+2);s++)r.push(s);return e-2>2?s.push(1,"..."):s.push(1),s.push(...r),e+2<t-1?s.push("...",t):t>1&&s.push(t),s})();return(0,s.jsxs)("nav",{className:"flex items-center justify-center space-x-1","aria-label":"Pagination",children:[(0,s.jsxs)(o.$,{variant:"outline",size:"sm",onClick:()=>r(e-1),disabled:1===e,className:"flex items-center gap-1",children:[(0,s.jsx)(c.A,{className:"w-4 h-4"}),"Previous"]}),(0,s.jsx)("div",{className:"flex items-center space-x-1",children:a.map((t,a)=>{if("..."===t)return(0,s.jsx)("div",{className:"flex items-center justify-center w-8 h-8",children:(0,s.jsx)(d.A,{className:"w-4 h-4 text-muted-foreground"})},`dots-${a}`);let i=t===e;return(0,s.jsx)(o.$,{variant:i?"default":"outline",size:"sm",onClick:()=>r(t),className:`w-8 h-8 p-0 ${i?"bg-primary text-primary-foreground":"hover:bg-muted"}`,"aria-current":i?"page":void 0,children:t},t)})}),(0,s.jsxs)(o.$,{variant:"outline",size:"sm",onClick:()=>r(e+1),disabled:e===t,className:"flex items-center gap-1",children:["Next",(0,s.jsx)(u.A,{className:"w-4 h-4"})]})]})};var p=r(26001);let x=(0,a.memo)(({products:e,currentPage:t,totalPages:r,onPageChange:a,isLoading:i=!1})=>0!==e.length||i?(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsx)(p.P.div,{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",initial:{opacity:0},animate:{opacity:1},transition:{duration:.3},children:e.map((e,t)=>(0,s.jsx)(p.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.05*t},children:(0,s.jsx)(l.K,{product:e})},e._id))}),r>1&&(0,s.jsx)("div",{className:"flex justify-center mt-12",children:(0,s.jsx)(m,{currentPage:t,totalPages:r,onPageChange:a})})]}):(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center py-16 text-center",children:[(0,s.jsx)("div",{className:"w-24 h-24 mb-6 text-muted-foreground",children:(0,s.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",className:"w-full h-full",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H6a1 1 0 00-1 1v1m16 0H4"})})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-2",children:"No products found"}),(0,s.jsx)("p",{className:"text-muted-foreground max-w-md",children:"We couldn't find any products matching your criteria. Try adjusting your filters or search terms."})]}));var h=r(44493),f=r(89667),g=r(54300),v=r(96834),y=r(67969),j=r(70569),b=r(98599),w=r(11273),N=r(65551),P=r(43),A=r(83721),k=r(18853),C=r(14163),S=r(9510),R=["PageUp","PageDown"],M=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],E={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},D="Slider",[_,L,q]=(0,S.N)(D),[z,H]=(0,w.A)(D,[q]),[$,B]=z(D),G=a.forwardRef((e,t)=>{let{name:r,min:i=0,max:n=100,step:l=1,orientation:o="horizontal",disabled:c=!1,minStepsBetweenThumbs:d=0,defaultValue:u=[i],value:m,onValueChange:p=()=>{},onValueCommit:x=()=>{},inverted:h=!1,form:f,...g}=e,v=a.useRef(new Set),b=a.useRef(0),w="horizontal"===o,[P=[],A]=(0,N.i)({prop:m,defaultProp:u,onChange:e=>{let t=[...v.current];t[b.current]?.focus(),p(e)}}),k=a.useRef(P);function C(e,t,{commit:r}={commit:!1}){let s=(String(l).split(".")[1]||"").length,a=function(e,t){let r=Math.pow(10,t);return Math.round(e*r)/r}(Math.round((e-i)/l)*l+i,s),o=(0,y.q)(a,[i,n]);A((e=[])=>{let s=function(e=[],t,r){let s=[...e];return s[r]=t,s.sort((e,t)=>e-t)}(e,o,t);if(!function(e,t){if(t>0)return Math.min(...e.slice(0,-1).map((t,r)=>e[r+1]-t))>=t;return!0}(s,d*l))return e;{b.current=s.indexOf(o);let t=String(s)!==String(e);return t&&r&&x(s),t?s:e}})}return(0,s.jsx)($,{scope:e.__scopeSlider,name:r,disabled:c,min:i,max:n,valueIndexToChangeRef:b,thumbs:v.current,values:P,orientation:o,form:f,children:(0,s.jsx)(_.Provider,{scope:e.__scopeSlider,children:(0,s.jsx)(_.Slot,{scope:e.__scopeSlider,children:(0,s.jsx)(w?Q:F,{"aria-disabled":c,"data-disabled":c?"":void 0,...g,ref:t,onPointerDown:(0,j.m)(g.onPointerDown,()=>{c||(k.current=P)}),min:i,max:n,inverted:h,onSlideStart:c?void 0:function(e){let t=function(e,t){if(1===e.length)return 0;let r=e.map(e=>Math.abs(e-t)),s=Math.min(...r);return r.indexOf(s)}(P,e);C(e,t)},onSlideMove:c?void 0:function(e){C(e,b.current)},onSlideEnd:c?void 0:function(){let e=k.current[b.current];P[b.current]!==e&&x(P)},onHomeKeyDown:()=>!c&&C(i,0,{commit:!0}),onEndKeyDown:()=>!c&&C(n,P.length-1,{commit:!0}),onStepKeyDown:({event:e,direction:t})=>{if(!c){let r=R.includes(e.key)||e.shiftKey&&M.includes(e.key),s=b.current;C(P[s]+l*(r?10:1)*t,s,{commit:!0})}}})})})})});G.displayName=D;var[I,O]=z(D,{startEdge:"left",endEdge:"right",size:"width",direction:1}),Q=a.forwardRef((e,t)=>{let{min:r,max:i,dir:n,inverted:l,onSlideStart:o,onSlideMove:c,onSlideEnd:d,onStepKeyDown:u,...m}=e,[p,x]=a.useState(null),h=(0,b.s)(t,e=>x(e)),f=a.useRef(void 0),g=(0,P.jH)(n),v="ltr"===g,y=v&&!l||!v&&l;function j(e){let t=f.current||p.getBoundingClientRect(),s=et([0,t.width],y?[r,i]:[i,r]);return f.current=t,s(e-t.left)}return(0,s.jsx)(I,{scope:e.__scopeSlider,startEdge:y?"left":"right",endEdge:y?"right":"left",direction:y?1:-1,size:"width",children:(0,s.jsx)(U,{dir:g,"data-orientation":"horizontal",...m,ref:h,style:{...m.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{let t=j(e.clientX);o?.(t)},onSlideMove:e=>{let t=j(e.clientX);c?.(t)},onSlideEnd:()=>{f.current=void 0,d?.()},onStepKeyDown:e=>{let t=E[y?"from-left":"from-right"].includes(e.key);u?.({event:e,direction:t?-1:1})}})})}),F=a.forwardRef((e,t)=>{let{min:r,max:i,inverted:n,onSlideStart:l,onSlideMove:o,onSlideEnd:c,onStepKeyDown:d,...u}=e,m=a.useRef(null),p=(0,b.s)(t,m),x=a.useRef(void 0),h=!n;function f(e){let t=x.current||m.current.getBoundingClientRect(),s=et([0,t.height],h?[i,r]:[r,i]);return x.current=t,s(e-t.top)}return(0,s.jsx)(I,{scope:e.__scopeSlider,startEdge:h?"bottom":"top",endEdge:h?"top":"bottom",size:"height",direction:h?1:-1,children:(0,s.jsx)(U,{"data-orientation":"vertical",...u,ref:p,style:{...u.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{let t=f(e.clientY);l?.(t)},onSlideMove:e=>{let t=f(e.clientY);o?.(t)},onSlideEnd:()=>{x.current=void 0,c?.()},onStepKeyDown:e=>{let t=E[h?"from-bottom":"from-top"].includes(e.key);d?.({event:e,direction:t?-1:1})}})})}),U=a.forwardRef((e,t)=>{let{__scopeSlider:r,onSlideStart:a,onSlideMove:i,onSlideEnd:n,onHomeKeyDown:l,onEndKeyDown:o,onStepKeyDown:c,...d}=e,u=B(D,r);return(0,s.jsx)(C.sG.span,{...d,ref:t,onKeyDown:(0,j.m)(e.onKeyDown,e=>{"Home"===e.key?(l(e),e.preventDefault()):"End"===e.key?(o(e),e.preventDefault()):R.concat(M).includes(e.key)&&(c(e),e.preventDefault())}),onPointerDown:(0,j.m)(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),u.thumbs.has(t)?t.focus():a(e)}),onPointerMove:(0,j.m)(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&i(e)}),onPointerUp:(0,j.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),n(e))})})}),K="SliderTrack",T=a.forwardRef((e,t)=>{let{__scopeSlider:r,...a}=e,i=B(K,r);return(0,s.jsx)(C.sG.span,{"data-disabled":i.disabled?"":void 0,"data-orientation":i.orientation,...a,ref:t})});T.displayName=K;var W="SliderRange",X=a.forwardRef((e,t)=>{let{__scopeSlider:r,...i}=e,n=B(W,r),l=O(W,r),o=a.useRef(null),c=(0,b.s)(t,o),d=n.values.length,u=n.values.map(e=>ee(e,n.min,n.max)),m=d>1?Math.min(...u):0,p=100-Math.max(...u);return(0,s.jsx)(C.sG.span,{"data-orientation":n.orientation,"data-disabled":n.disabled?"":void 0,...i,ref:c,style:{...e.style,[l.startEdge]:m+"%",[l.endEdge]:p+"%"}})});X.displayName=W;var J="SliderThumb",V=a.forwardRef((e,t)=>{let r=L(e.__scopeSlider),[i,n]=a.useState(null),l=(0,b.s)(t,e=>n(e)),o=a.useMemo(()=>i?r().findIndex(e=>e.ref.current===i):-1,[r,i]);return(0,s.jsx)(Z,{...e,ref:l,index:o})}),Z=a.forwardRef((e,t)=>{let{__scopeSlider:r,index:i,name:n,...l}=e,o=B(J,r),c=O(J,r),[d,u]=a.useState(null),m=(0,b.s)(t,e=>u(e)),p=!d||o.form||!!d.closest("form"),x=(0,k.X)(d),h=o.values[i],f=void 0===h?0:ee(h,o.min,o.max),g=function(e,t){return t>2?`Value ${e+1} of ${t}`:2===t?["Minimum","Maximum"][e]:void 0}(i,o.values.length),v=x?.[c.size],y=v?function(e,t,r){let s=e/2,a=et([0,50],[0,s]);return(s-a(t)*r)*r}(v,f,c.direction):0;return a.useEffect(()=>{if(d)return o.thumbs.add(d),()=>{o.thumbs.delete(d)}},[d,o.thumbs]),(0,s.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[c.startEdge]:`calc(${f}% + ${y}px)`},children:[(0,s.jsx)(_.ItemSlot,{scope:e.__scopeSlider,children:(0,s.jsx)(C.sG.span,{role:"slider","aria-label":e["aria-label"]||g,"aria-valuemin":o.min,"aria-valuenow":h,"aria-valuemax":o.max,"aria-orientation":o.orientation,"data-orientation":o.orientation,"data-disabled":o.disabled?"":void 0,tabIndex:o.disabled?void 0:0,...l,ref:m,style:void 0===h?{display:"none"}:e.style,onFocus:(0,j.m)(e.onFocus,()=>{o.valueIndexToChangeRef.current=i})})}),p&&(0,s.jsx)(Y,{name:n??(o.name?o.name+(o.values.length>1?"[]":""):void 0),form:o.form,value:h},i)]})});V.displayName=J;var Y=a.forwardRef(({__scopeSlider:e,value:t,...r},i)=>{let n=a.useRef(null),l=(0,b.s)(n,i),o=(0,A.Z)(t);return a.useEffect(()=>{let e=n.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(o!==t&&r){let s=new Event("input",{bubbles:!0});r.call(e,t),e.dispatchEvent(s)}},[o,t]),(0,s.jsx)(C.sG.input,{style:{display:"none"},...r,ref:l,defaultValue:t})});function ee(e,t,r){return(0,y.q)(100/(r-t)*(e-t),[0,100])}function et(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let s=(t[1]-t[0])/(e[1]-e[0]);return t[0]+s*(r-e[0])}}Y.displayName="RadioBubbleInput";var er=r(4780);let es=a.forwardRef(({className:e,...t},r)=>(0,s.jsxs)(G,{ref:r,className:(0,er.cn)("relative flex w-full touch-none select-none items-center",e),...t,children:[(0,s.jsx)(T,{className:"relative h-1.5 w-full grow overflow-hidden rounded-full bg-primary/20",children:(0,s.jsx)(X,{className:"absolute h-full bg-primary"})}),(0,s.jsx)(V,{className:"block h-4 w-4 rounded-full border border-primary/50 bg-background shadow transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"}),(0,s.jsx)(V,{className:"block h-4 w-4 rounded-full border border-primary/50 bg-background shadow transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"})]}));es.displayName=G.displayName;var ea=r(80462),ei=r(3589),en=r(78272),el=r(11860),eo=r(88920);let ec=({filters:e,categories:t,priceRange:r,onFilterChange:i,onClearFilters:n})=>{let[l,c]=(0,a.useState)({categories:!0,price:!0,rating:!1}),[d,u]=(0,a.useState)([e.minPrice,e.maxPrice]),m=e=>{c(t=>({...t,[e]:!t[e]}))},x=e=>{u(e),i({minPrice:e[0],maxPrice:e[1]})},y=t=>{i({category:e.category===t?"":t})},j=()=>{let t=0;return e.category&&t++,(e.minPrice>r.min||e.maxPrice<r.max)&&t++,e.searchQuery&&t++,t},b=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0}).format(e);return(0,s.jsxs)(h.Zp,{className:"sticky top-4",children:[(0,s.jsx)(h.aR,{className:"pb-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(h.ZB,{className:"text-lg font-semibold flex items-center gap-2",children:[(0,s.jsx)(ea.A,{className:"w-5 h-5"}),"Filters",j()>0&&(0,s.jsx)(v.E,{variant:"secondary",className:"ml-2",children:j()})]}),j()>0&&(0,s.jsx)(o.$,{variant:"ghost",size:"sm",onClick:n,className:"text-muted-foreground hover:text-foreground",children:"Clear All"})]})}),(0,s.jsxs)(h.Wu,{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("button",{onClick:()=>m("categories"),className:"flex items-center justify-between w-full text-left",children:[(0,s.jsx)(g.J,{className:"text-sm font-medium",children:"Categories"}),l.categories?(0,s.jsx)(ei.A,{className:"w-4 h-4"}):(0,s.jsx)(en.A,{className:"w-4 h-4"})]}),(0,s.jsx)(eo.N,{children:l.categories&&(0,s.jsx)(p.P.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.2},className:"overflow-hidden",children:(0,s.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:t.map(t=>(0,s.jsx)("button",{onClick:()=>y(t),className:`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${e.category===t?"bg-primary text-primary-foreground":"hover:bg-muted"}`,children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"capitalize",children:t}),e.category===t&&(0,s.jsx)(el.A,{className:"w-3 h-3"})]})},t))})})})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("button",{onClick:()=>m("price"),className:"flex items-center justify-between w-full text-left",children:[(0,s.jsx)(g.J,{className:"text-sm font-medium",children:"Price Range"}),l.price?(0,s.jsx)(ei.A,{className:"w-4 h-4"}):(0,s.jsx)(en.A,{className:"w-4 h-4"})]}),(0,s.jsx)(eo.N,{children:l.price&&(0,s.jsxs)(p.P.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.2},className:"overflow-hidden space-y-4",children:[(0,s.jsx)("div",{className:"px-2",children:(0,s.jsx)(es,{value:d,onValueChange:x,max:r.max,min:r.min,step:1,className:"w-full"})}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-muted-foreground",children:[(0,s.jsx)("span",{children:b(d[0])}),(0,s.jsx)("span",{children:b(d[1])})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(g.J,{htmlFor:"min-price",className:"text-xs",children:"Min"}),(0,s.jsx)(f.p,{id:"min-price",type:"number",value:d[0],onChange:e=>{let t=[parseInt(e.target.value)||0,d[1]];u(t),x(t)},className:"h-8 text-xs"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(g.J,{htmlFor:"max-price",className:"text-xs",children:"Max"}),(0,s.jsx)(f.p,{id:"max-price",type:"number",value:d[1],onChange:e=>{let t=parseInt(e.target.value)||r.max,s=[d[0],t];u(s),x(s)},className:"h-8 text-xs"})]})]})]})})]}),j()>0&&(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(g.J,{className:"text-sm font-medium",children:"Active Filters"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2",children:[e.category&&(0,s.jsxs)(v.E,{variant:"secondary",className:"flex items-center gap-1 cursor-pointer",onClick:()=>i({category:""}),children:[e.category,(0,s.jsx)(el.A,{className:"w-3 h-3"})]}),(e.minPrice>r.min||e.maxPrice<r.max)&&(0,s.jsxs)(v.E,{variant:"secondary",className:"flex items-center gap-1 cursor-pointer",onClick:()=>i({minPrice:r.min,maxPrice:r.max}),children:[b(e.minPrice)," - ",b(e.maxPrice),(0,s.jsx)(el.A,{className:"w-3 h-3"})]}),e.searchQuery&&(0,s.jsxs)(v.E,{variant:"secondary",className:"flex items-center gap-1 cursor-pointer",onClick:()=>i({searchQuery:""}),children:["“",e.searchQuery,"”",(0,s.jsx)(el.A,{className:"w-3 h-3"})]})]})]})]})]})};var ed=r(21342),eu=r(99270);let em=(0,r(62688).A)("sliders-horizontal",[["line",{x1:"21",x2:"14",y1:"4",y2:"4",key:"obuewd"}],["line",{x1:"10",x2:"3",y1:"4",y2:"4",key:"1q6298"}],["line",{x1:"21",x2:"12",y1:"12",y2:"12",key:"1iu8h1"}],["line",{x1:"8",x2:"3",y1:"12",y2:"12",key:"ntss68"}],["line",{x1:"21",x2:"16",y1:"20",y2:"20",key:"14d8ph"}],["line",{x1:"12",x2:"3",y1:"20",y2:"20",key:"m0wm8r"}],["line",{x1:"14",x2:"14",y1:"2",y2:"6",key:"14e1ph"}],["line",{x1:"8",x2:"8",y1:"10",y2:"14",key:"1i6ji0"}],["line",{x1:"16",x2:"16",y1:"18",y2:"22",key:"1lctlv"}]]),ep=({totalProducts:e,filters:t,onFilterChange:r,onClearFilters:i})=>{let[n,l]=(0,a.useState)(t.searchQuery);(0,a.useEffect)(()=>{l(t.searchQuery)},[t.searchQuery]);let c=[{value:"createdAt-desc",label:"Newest First",sortBy:"createdAt",sortOrder:"desc"},{value:"createdAt-asc",label:"Oldest First",sortBy:"createdAt",sortOrder:"asc"},{value:"price-asc",label:"Price: Low to High",sortBy:"price",sortOrder:"asc"},{value:"price-desc",label:"Price: High to Low",sortBy:"price",sortOrder:"desc"},{value:"rating-desc",label:"Highest Rated",sortBy:"rating",sortOrder:"desc"},{value:"rating-asc",label:"Lowest Rated",sortBy:"rating",sortOrder:"asc"}],d=c.find(e=>e.sortBy===t.sortBy&&e.sortOrder===t.sortOrder),u=(e,t)=>{r({sortBy:e,sortOrder:t})},m=()=>{let e=0;return t.category&&e++,t.searchQuery&&e++,(t.minPrice>0||t.maxPrice<1e3)&&e++,e};return(0,s.jsxs)("div",{className:"space-y-6 mb-8",children:[(0,s.jsxs)("div",{className:"text-center space-y-2",children:[(0,s.jsx)("h1",{className:"text-3xl md:text-4xl font-bold text-foreground",children:"Shop Our Products"}),(0,s.jsx)("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Discover our extensive collection of premium products at unbeatable prices"})]}),(0,s.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 items-center justify-between",children:[(0,s.jsx)("form",{onSubmit:e=>{e.preventDefault(),r({searchQuery:n})},className:"relative flex-1 max-w-md",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(eu.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4"}),(0,s.jsx)(f.p,{type:"text",placeholder:"Search products...",value:n,onChange:e=>l(e.target.value),className:"pl-10 pr-10"}),n&&(0,s.jsx)("button",{type:"button",onClick:()=>{l(""),r({searchQuery:""})},className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground",children:(0,s.jsx)(el.A,{className:"w-4 h-4"})})]})}),(0,s.jsx)("div",{className:"flex items-center gap-4",children:(0,s.jsxs)(ed.rI,{children:[(0,s.jsx)(ed.ty,{asChild:!0,children:(0,s.jsxs)(o.$,{variant:"outline",className:"min-w-[180px] justify-between",children:[(0,s.jsxs)("span",{className:"flex items-center gap-2",children:[(0,s.jsx)(em,{className:"w-4 h-4"}),d?.label||"Sort by"]}),(0,s.jsx)(en.A,{className:"w-4 h-4"})]})}),(0,s.jsx)(ed.SQ,{align:"end",className:"w-[200px]",children:c.map(e=>(0,s.jsx)(ed._2,{onClick:()=>u(e.sortBy,e.sortOrder),className:`cursor-pointer ${d?.value===e.value?"bg-accent":""}`,children:e.label},e.value))})]})})]}),(0,s.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Showing ",e," product",1!==e?"s":""]}),m()>0&&(0,s.jsxs)(v.E,{variant:"secondary",children:[m()," filter",1!==m()?"s":""," applied"]})]}),m()>0&&(0,s.jsx)(o.$,{variant:"ghost",size:"sm",onClick:i,className:"text-muted-foreground hover:text-foreground",children:"Clear all filters"})]}),m()>0&&(0,s.jsxs)("div",{className:"flex flex-wrap gap-2",children:[t.category&&(0,s.jsxs)(v.E,{variant:"secondary",className:"flex items-center gap-1 cursor-pointer hover:bg-secondary/80",onClick:()=>r({category:""}),children:["Category: ",t.category,(0,s.jsx)(el.A,{className:"w-3 h-3"})]}),t.searchQuery&&(0,s.jsxs)(v.E,{variant:"secondary",className:"flex items-center gap-1 cursor-pointer hover:bg-secondary/80",onClick:()=>r({searchQuery:""}),children:["Search: “",t.searchQuery,"”",(0,s.jsx)(el.A,{className:"w-3 h-3"})]}),(t.minPrice>0||t.maxPrice<1e3)&&(0,s.jsxs)(v.E,{variant:"secondary",className:"flex items-center gap-1 cursor-pointer hover:bg-secondary/80",onClick:()=>r({minPrice:0,maxPrice:1e3}),children:["Price: $",t.minPrice," - $",t.maxPrice,(0,s.jsx)(el.A,{className:"w-3 h-3"})]})]})]})};var ex=r(14993),eh=r(93613),ef=r(78122);let eg=({message:e,onRetry:t})=>(0,s.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center p-4",children:(0,s.jsx)(h.Zp,{className:"w-full max-w-md",children:(0,s.jsxs)(h.Wu,{className:"flex flex-col items-center justify-center py-12 text-center space-y-6",children:[(0,s.jsx)("div",{className:"w-16 h-16 text-destructive",children:(0,s.jsx)(eh.A,{className:"w-full h-full"})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-foreground",children:"Oops! Something went wrong"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:e})]}),(0,s.jsxs)(o.$,{onClick:t,className:"flex items-center gap-2",size:"lg",children:[(0,s.jsx)(ef.A,{className:"w-4 h-4"}),"Try Again"]})]})})}),ev=()=>{let[e,t]=(0,a.useState)([]),[r,l]=(0,a.useState)([]),[o,c]=(0,a.useState)(!0),[d,u]=(0,a.useState)(null),[m,p]=(0,a.useState)([]),[h,f]=(0,a.useState)({min:0,max:1e3}),[g,v]=(0,a.useState)({category:"",minPrice:0,maxPrice:1e3,sortBy:"createdAt",sortOrder:"desc",searchQuery:""}),[y,j]=(0,a.useState)(1),[b,w]=(0,a.useState)(1),[N,P]=(0,a.useState)(0);(0,a.useEffect)(()=>{A()},[]),(0,a.useEffect)(()=>{k()},[e,g,k]);let A=async()=>{try{c(!0),u(null);let e=await i.j.getAllProducts();if(e.success&&e.data){t(e.data.products);let r=i.j.getCategories(e.data.products),s=i.j.getPriceRange(e.data.products);p(r),f(s),v(e=>({...e,maxPrice:s.max}))}else u("Failed to load products")}catch(e){console.error("Error fetching products:",e),u("Failed to load products. Please try again."),n.oR.error("Failed to load products")}finally{c(!1)}},k=(0,a.useCallback)(()=>{let t=[...e];if(g.searchQuery){let e=g.searchQuery.toLowerCase();t=t.filter(t=>t.title.toLowerCase().includes(e)||t.description.toLowerCase().includes(e)||t.category.toLowerCase().includes(e))}g.category&&(t=t.filter(e=>e.category===g.category)),t=t.filter(e=>e.price>=g.minPrice&&e.price<=g.maxPrice),l(t=i.j.sortProducts(t,g.sortBy,g.sortOrder)),P(t.length),w(Math.ceil(t.length/12)),j(1)},[e,g]),C=(0,a.useMemo)(()=>{let e=(y-1)*12;return r.slice(e,e+12)},[r,y,12]),S=(0,a.useCallback)(e=>{v(t=>({...t,...e}))},[]),R=(0,a.useCallback)(e=>{j(e),window.scrollTo({top:0,behavior:"smooth"})},[]),M=(0,a.useCallback)(()=>{v({category:"",minPrice:h.min,maxPrice:h.max,sortBy:"createdAt",sortOrder:"desc",searchQuery:""})},[h.min,h.max]);return d?(0,s.jsx)(eg,{message:d,onRetry:A}):(0,s.jsx)("div",{className:"min-h-screen bg-background",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsx)(ep,{totalProducts:N,filters:g,onFilterChange:S,onClearFilters:M}),(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,s.jsx)("aside",{className:"lg:w-64 flex-shrink-0",children:(0,s.jsx)(ec,{filters:g,categories:m,priceRange:h,onFilterChange:S,onClearFilters:M})}),(0,s.jsx)("main",{className:"flex-1",children:o?(0,s.jsx)(ex.default,{}):(0,s.jsx)(x,{products:C,currentPage:y,totalPages:b,onPageChange:R,isLoading:o})})]})]})})}},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,162,658,598,1,367,589],()=>r(90745));module.exports=s})();