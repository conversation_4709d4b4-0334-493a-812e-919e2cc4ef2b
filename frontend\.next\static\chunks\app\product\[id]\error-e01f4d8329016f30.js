(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6001],{107:(e,t,r)=>{Promise.resolve().then(r.bind(r,7247))},285:(e,t,r)=>{"use strict";r.d(t,{$:()=>o});var n=r(5155);r(2115);var a=r(9708),s=r(2085),l=r(9434);let i=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:s,asChild:o=!1,...c}=e,d=o?a.DX:"button";return(0,n.jsx)(d,{"data-slot":"button",className:(0,l.cn)(i({variant:r,size:s,className:t})),...c})}},1243:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},2085:(e,t,r)=>{"use strict";r.d(t,{F:()=>l});var n=r(2596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=n.$,l=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return s(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:i}=t,o=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],n=null==i?void 0:i[e];if(null===t)return null;let s=a(t)||a(n);return l[e][s]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return s(e,o,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...c}[t]):({...i,...c})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},3904:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>l,t:()=>s});var n=r(2115);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}function l(...e){return n.useCallback(s(...e),e)}},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>i,Zp:()=>s,aR:()=>l,wL:()=>d});var n=r(5155);r(2115);var a=r(9434);function s(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function l(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function i(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...r})}function o(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...r})}function c(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r})}},7247:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var n=r(5155),a=r(2115),s=r(6874),l=r.n(s),i=r(285),o=r(6695),c=r(1243),d=r(3904),u=r(7550),f=r(7340);function h(e){let{error:t,reset:r}=e;return(0,a.useEffect)(()=>{console.error("Product page error:",t)},[t]),(0,n.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center p-4",children:(0,n.jsx)(o.Zp,{className:"w-full max-w-lg",children:(0,n.jsxs)(o.Wu,{className:"flex flex-col items-center justify-center py-16 text-center space-y-6",children:[(0,n.jsx)("div",{className:"w-20 h-20 text-destructive",children:(0,n.jsx)(c.A,{className:"w-full h-full"})}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsx)("h2",{className:"text-2xl font-bold text-foreground",children:"Something went wrong!"}),(0,n.jsx)("p",{className:"text-muted-foreground max-w-md",children:"We encountered an error while loading this product page. This might be a temporary issue."}),!1]}),(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 w-full max-w-sm",children:[(0,n.jsxs)(i.$,{onClick:r,className:"flex-1 flex items-center gap-2",size:"lg",children:[(0,n.jsx)(d.A,{className:"w-4 h-4"}),"Try Again"]}),(0,n.jsx)(i.$,{variant:"outline",asChild:!0,className:"flex-1 flex items-center gap-2",size:"lg",children:(0,n.jsxs)(l(),{href:"/shop",children:[(0,n.jsx)(u.A,{className:"w-4 h-4"}),"Back to Shop"]})})]}),(0,n.jsx)(i.$,{variant:"ghost",asChild:!0,className:"flex items-center gap-2",children:(0,n.jsxs)(l(),{href:"/",children:[(0,n.jsx)(f.A,{className:"w-4 h-4"}),"Go Home"]})})]})})})}},7340:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},7550:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var n=r(2596),a=r(9688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,n.$)(t))}},9708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>i,TL:()=>l});var n=r(2115),a=r(6101),s=r(5155);function l(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...s}=e;if(n.isValidElement(r)){var l;let e,i,o=(l=r,(i=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(i=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),c=function(e,t){let r={...t};for(let n in t){let a=e[n],s=t[n];/^on[A-Z]/.test(n)?a&&s?r[n]=(...e)=>{let t=s(...e);return a(...e),t}:a&&(r[n]=a):"style"===n?r[n]={...a,...s}:"className"===n&&(r[n]=[a,s].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==n.Fragment&&(c.ref=t?(0,a.t)(t,o):o),n.cloneElement(r,c)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:a,...l}=e,i=n.Children.toArray(a),o=i.find(c);if(o){let e=o.props.children,a=i.map(t=>t!==o?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...l,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,a):null})}return(0,s.jsx)(t,{...l,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var i=l("Slot"),o=Symbol("radix.slottable");function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),l=e=>{let t=s(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:a=24,strokeWidth:s=2,absoluteStrokeWidth:l,className:d="",children:u,iconNode:f,...h}=e;return(0,n.createElement)("svg",{ref:t,...c,width:a,height:a,stroke:r,strokeWidth:l?24*Number(s)/Number(a):s,className:i("lucide",d),...!u&&!o(h)&&{"aria-hidden":"true"},...h},[...f.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let r=(0,n.forwardRef)((r,s)=>{let{className:o,...c}=r;return(0,n.createElement)(d,{ref:s,iconNode:t,className:i("lucide-".concat(a(l(e))),"lucide-".concat(e),o),...c})});return r.displayName=l(e),r}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6874,8441,1684,7358],()=>t(107)),_N_E=e.O()}]);