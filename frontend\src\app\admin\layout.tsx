import 'react-toastify/dist/ReactToastify.css';
import type { Metadata } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON>st_Mono } from "next/font/google";
import "../globals.css";
import { ToastContainer } from 'react-toastify';
import { AuthProvider } from '@/contexts/admin/AuthContext';
import AdminLayoutWrapper from '@/components/admin/AdminLayoutWrapper';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Mega Mall Admin",
  description: "Admin panel for Mega Mall e-commerce platform.",
};

export default function AdminLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <AuthProvider>
          <AdminLayoutWrapper>
            {children}
          </AdminLayoutWrapper>

          <ToastContainer
            position="top-right"
            autoClose={3000}
            hideProgressBar={false}
            newestOnTop
            closeOnClick
            pauseOnHover
            theme="light"
          />
        </AuthProvider>
      </body>
    </html>
  );
}
