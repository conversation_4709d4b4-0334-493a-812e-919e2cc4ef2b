(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1778],{283:(e,s,t)=>{"use strict";t.d(s,{A:()=>d,AuthProvider:()=>n});var a=t(5155),r=t(2115),l=t(5654);let i=(0,r.createContext)(void 0),d=()=>{let e=(0,r.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},n=e=>{let{children:s}=e,[t,d]=(0,r.useState)(null),[n,c]=(0,r.useState)(!0),o=!!t;(0,r.useEffect)(()=>{(async()=>{if(localStorage.getItem("token"))try{let e=await l.Dv.getUser();e.success&&e.data&&d(e.data)}catch(e){e.response&&(console.error("Error checking auth status:",e.response.data.message),localStorage.removeItem("token"))}c(!1)})()},[]);let m=async()=>{try{await l.Dv.logout()}catch(e){console.error("Error during logout:",e)}finally{localStorage.removeItem("token"),d(null)}},h=async()=>{try{let e=await l.Dv.getUser();e.success&&e.data&&d(e.data)}catch(e){console.error("Error refreshing user data:",e)}};return(0,a.jsx)(i.Provider,{value:{user:t,isLoading:n,isAuthenticated:o,login:(e,s)=>{localStorage.setItem("token",s),d(e)},logout:m,updateUser:e=>{t&&d({...t,...e})},refreshUser:h},children:s})}},646:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1788:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},1958:(e,s,t)=>{"use strict";t.d(s,{default:()=>S});var a=t(5155),r=t(2115),l=t(6874),i=t.n(l),d=t(6695),n=t(285),c=t(6126),o=t(2523),m=t(9409),h=t(5365),x=t(5654),u=t(283),p=t(7108),y=t(9799),g=t(646),f=t(4416),j=t(5339),v=t(7924),w=t(6932),b=t(6151),N=t(9074),A=t(2657),k=t(1788);let S=()=>{let{isAuthenticated:e}=(0,u.A)(),[s,t]=(0,r.useState)([]),[l,S]=(0,r.useState)([]),[C,M]=(0,r.useState)(!0),[R,D]=(0,r.useState)(null),[E,L]=(0,r.useState)(""),[P,I]=(0,r.useState)("all"),[q,_]=(0,r.useState)("newest");(0,r.useEffect)(()=>{(async()=>{if(e)try{M(!0);let e=await x.Qo.getOrders();e.success&&e.data?(t(e.data),S(e.data)):D("Failed to fetch orders")}catch(e){console.error("Error fetching orders:",e),D("Failed to load orders")}finally{M(!1)}})()},[e]),(0,r.useEffect)(()=>{let e=[...s];E&&(e=null==e?void 0:e.filter(e=>{var s;return(null==(s=e._id)?void 0:s.toLowerCase().includes(E.toLowerCase()))||e.orderItems.some(e=>{var s;return null==(s=e.product.title)?void 0:s.toLowerCase().includes(E.toLowerCase())})})),"all"!==P&&(e=e.filter(e=>e.status===P)),e.sort((e,s)=>{switch(q){case"newest":return new Date(s.createdAt).getTime()-new Date(e.createdAt).getTime();case"oldest":return new Date(e.createdAt).getTime()-new Date(s.createdAt).getTime();case"amount-high":return s.totalRevenue-e.totalRevenue;case"amount-low":return e.totalRevenue-s.totalRevenue;default:return 0}}),S(e)},[s,E,P,q]);let z=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),H=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"shipped":return"bg-purple-100 text-purple-800";case"delivered":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},F=e=>{switch(e){case"pending":default:return(0,a.jsx)(p.A,{className:"w-4 h-4"});case"shipped":return(0,a.jsx)(y.A,{className:"w-4 h-4"});case"delivered":return(0,a.jsx)(g.A,{className:"w-4 h-4"});case"cancelled":return(0,a.jsx)(f.A,{className:"w-4 h-4"})}};return e?C?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsx)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3 mb-4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-8"}),(0,a.jsx)("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,a.jsx)("div",{className:"h-32 bg-gray-200 rounded"},e))})]})})}):R?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsx)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)(h.Fc,{variant:"destructive",children:[(0,a.jsx)(j.A,{className:"h-4 w-4"}),(0,a.jsx)(h.TN,{children:R})]})})}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Order History"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Track and manage your orders"})]}),(0,a.jsx)(d.Zp,{className:"mb-6",children:(0,a.jsx)(d.Wu,{className:"pt-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)(o.p,{placeholder:"Search orders...",value:E,onChange:e=>L(e.target.value),className:"pl-10"})]}),(0,a.jsxs)(m.l6,{value:P,onValueChange:I,children:[(0,a.jsx)(m.bq,{children:(0,a.jsx)(m.yv,{placeholder:"Filter by status"})}),(0,a.jsxs)(m.gC,{children:[(0,a.jsx)(m.eb,{value:"all",children:"All Orders"}),(0,a.jsx)(m.eb,{value:"pending",children:"Pending"}),(0,a.jsx)(m.eb,{value:"shipped",children:"Shipped"}),(0,a.jsx)(m.eb,{value:"delivered",children:"Delivered"}),(0,a.jsx)(m.eb,{value:"cancelled",children:"Cancelled"})]})]}),(0,a.jsxs)(m.l6,{value:q,onValueChange:_,children:[(0,a.jsx)(m.bq,{children:(0,a.jsx)(m.yv,{placeholder:"Sort by"})}),(0,a.jsxs)(m.gC,{children:[(0,a.jsx)(m.eb,{value:"newest",children:"Newest First"}),(0,a.jsx)(m.eb,{value:"oldest",children:"Oldest First"}),(0,a.jsx)(m.eb,{value:"amount-high",children:"Amount: High to Low"}),(0,a.jsx)(m.eb,{value:"amount-low",children:"Amount: Low to High"})]})]}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(w.A,{className:"w-4 h-4 mr-2"}),l.length," of ",s.length," orders"]})]})})}),0===l.length?(0,a.jsx)(d.Zp,{children:(0,a.jsxs)(d.Wu,{className:"flex flex-col items-center justify-center py-12 text-center",children:[(0,a.jsx)(b.A,{className:"w-16 h-16 text-gray-400 mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:0===s.length?"No orders yet":"No orders found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:0===s.length?"Start shopping to see your orders here":"Try adjusting your search or filter criteria"}),0===s.length&&(0,a.jsx)(n.$,{asChild:!0,children:(0,a.jsx)(i(),{href:"/shop",children:"Start Shopping"})})]})}):(0,a.jsx)("div",{className:"space-y-4",children:l.map(e=>(0,a.jsx)(d.Zp,{className:"hover:shadow-md transition-shadow",children:(0,a.jsxs)(d.Wu,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"font-semibold",children:["Order #",e._id.slice(-8)]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Placed on ",z(e.createdAt)]})]}),(0,a.jsxs)(c.E,{className:"".concat(H(e.status)," flex items-center space-x-1"),children:[F(e.status),(0,a.jsxs)("span",{children:["Order Status: ",e.status]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"font-semibold text-lg",children:["$",e.totalRevenue.toFixed(2)]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.orderItems.length," item",1!==e.orderItems.length?"s":""]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[e.orderItems.slice(0,3).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-md flex items-center justify-center",children:(0,a.jsx)(p.A,{className:"w-6 h-6 text-gray-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium truncate max-w-32",children:e.product.title}),(0,a.jsxs)("p",{className:"text-xs text-gray-600",children:["Qty: ",e.quantity]})]})]},s)),e.orderItems.length>3&&(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["+",e.orderItems.length-3," more items"]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,a.jsx)(N.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-xs",children:"Delivery date will be updated soon"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(n.$,{variant:"outline",size:"sm",asChild:!0,children:(0,a.jsxs)(i(),{href:"/order-confirmation/".concat(e._id),children:[(0,a.jsx)(A.A,{className:"w-4 h-4 mr-2"}),"View Details"]})}),(0,a.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(k.A,{className:"w-4 h-4 mr-2"}),"Receipt"]})]})]})]})},e._id))})]})}):null}},2333:(e,s,t)=>{Promise.resolve().then(t.bind(t,6842)),Promise.resolve().then(t.bind(t,1958)),Promise.resolve().then(t.bind(t,2357))},2523:(e,s,t)=>{"use strict";t.d(s,{p:()=>l});var a=t(5155);t(2115);var r=t(9434);function l(e){let{className:s,type:t,...l}=e;return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...l})}},2657:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5339:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},6151:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("shopping-bag",[["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}],["path",{d:"M3.103 6.034h17.794",key:"awc11p"}],["path",{d:"M3.4 5.467a2 2 0 0 0-.4 1.2V20a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6.667a2 2 0 0 0-.4-1.2l-2-2.667A2 2 0 0 0 17 2H7a2 2 0 0 0-1.6.8z",key:"o988cm"}]])},6932:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},7924:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},9074:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9409:(e,s,t)=>{"use strict";t.d(s,{bq:()=>h,eb:()=>y,gC:()=>p,l6:()=>o,yv:()=>m});var a=t(5155),r=t(2115),l=t(2918),i=t(6474),d=t(7863),n=t(5196),c=t(9434);let o=l.bL;l.YJ;let m=l.WT,h=r.forwardRef((e,s)=>{let{className:t,children:r,...d}=e;return(0,a.jsxs)(l.l9,{ref:s,className:(0,c.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...d,children:[r,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]})});h.displayName=l.l9.displayName;let x=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.PP,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})});x.displayName=l.PP.displayName;let u=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.wn,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})});u.displayName=l.wn.displayName;let p=r.forwardRef((e,s)=>{let{className:t,children:r,position:i="popper",...d}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:s,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...d,children:[(0,a.jsx)(x,{}),(0,a.jsx)(l.LM,{className:(0,c.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,a.jsx)(u,{})]})})});p.displayName=l.UC.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.JU,{ref:s,className:(0,c.cn)("px-2 py-1.5 text-sm font-semibold",t),...r})}).displayName=l.JU.displayName;let y=r.forwardRef((e,s)=>{let{className:t,children:r,...i}=e;return(0,a.jsxs)(l.q7,{ref:s,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...i,children:[(0,a.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:r})]})});y.displayName=l.q7.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.wv,{ref:s,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",t),...r})}).displayName=l.wv.displayName},9799:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,9078,6874,3888,7172,7389,7455,8441,1684,7358],()=>s(2333)),_N_E=e.O()}]);