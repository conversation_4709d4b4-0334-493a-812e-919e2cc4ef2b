(()=>{var e={};e.id=213,e.ids=[213],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9815:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\app\\\\otpVerification\\\\[userId]\\\\[newEmail]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\otpVerification\\[userId]\\[newEmail]\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12597:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16189:(e,t,r)=>{"use strict";var s=r(65773);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27889:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(60687),a=r(43210),i=r(85814),n=r.n(i),o=r(16189),d=r(12597),l=r(13861),c=r(41862),u=r(29523),p=r(89667),m=r(54300),x=r(44493),f=r(58376),h=r(93853),v=r(63213);let g=()=>{let e=(0,o.useRouter)(),t=(0,o.useParams)(),{isAuthenticated:r}=(0,v.A)(),[i,g]=(0,a.useState)(""),[b,y]=(0,a.useState)(!1),[w,P]=(0,a.useState)(!1),j=t.userId,A=t.newEmail,R=decodeURIComponent(A),N=async t=>{if(t.preventDefault(),!j)return void h.oR.error("Please provide a valid user ID");if(6!==i.length)return void h.oR.error("Please enter a valid 6-digit OTP");P(!0);try{let t=await f.Dv.verifyOtp(j,i);if(r&&t.success)try{let e=await f.Dv.updateEmail({newEmail:R});e.success&&h.oR.success(e.data?.message||"Email updated successfully!")}catch(e){h.oR.error(e.response?.data?.message||"Failed to update email. Please try again.")}(t.success||!A)&&(h.oR.success("OTP verification successfully."),e.push("/signin"))}catch(e){h.oR.error(e.response?.data?.message||"An error occurred. Please try again.")}finally{P(!1)}},q=async()=>{try{(await f.Dv.resendOtp(j,R)).success&&h.oR.success("OTP resent successfull!")}catch(e){h.oR.error(e.response?.data.message||"failed to resend otp.")}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,s.jsxs)(x.Zp,{className:"w-full max-w-md shadow-lg rounded-2xl",children:[(0,s.jsxs)(x.aR,{className:"text-center",children:[(0,s.jsx)(x.ZB,{className:"text-2xl font-semibold",children:"OTP Verification"}),(0,s.jsx)(x.BT,{children:"Enter the 6-digit OTP sent to your email address."})]}),(0,s.jsx)(x.Wu,{children:(0,s.jsxs)("form",{onSubmit:N,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(m.J,{htmlFor:"otp",children:"One-Time Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(p.p,{id:"otp",type:b?"text":"password",value:i,onChange:e=>g(e.target.value),placeholder:"Enter 6-digit OTP",autoComplete:"off",maxLength:6,className:"pr-10"}),(0,s.jsx)("button",{type:"button",onClick:()=>y(e=>!e),className:"absolute top-1/2 right-2 -translate-y-1/2 text-muted-foreground",children:b?(0,s.jsx)(d.A,{size:18}):(0,s.jsx)(l.A,{size:18})})]})]}),(0,s.jsxs)(u.$,{type:"submit",className:"w-full",disabled:w,children:[w?(0,s.jsx)(c.A,{className:"w-4 h-4 animate-spin mr-2"}):null,w?"Verifying...":"Verify OTP"]})]})}),(0,s.jsxs)(x.wL,{className:"flex flex-col gap-2 text-sm text-center",children:[(0,s.jsxs)("p",{children:["Did not receive the OTP?"," ",(0,s.jsx)("button",{onClick:q,className:"text-primary underline",children:"Resend OTP"})]}),(0,s.jsxs)("p",{children:["Having trouble?"," ",(0,s.jsx)(n(),{href:"/contact",className:"text-primary underline",children:"Contact us"})]})]})]})})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32614:(e,t,r)=>{Promise.resolve().then(r.bind(r,27889))},33873:e=>{"use strict";e.exports=require("path")},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>i,aR:()=>n,wL:()=>c});var s=r(60687);r(43210);var a=r(4780);function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},54300:(e,t,r)=>{"use strict";r.d(t,{J:()=>d});var s=r(60687),a=r(43210),i=r(14163),n=a.forwardRef((e,t)=>(0,s.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var o=r(4780);function d({className:e,...t}){return(0,s.jsx)(n,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66270:(e,t,r)=>{Promise.resolve().then(r.bind(r,9815))},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85441:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let l={children:["",{children:["otpVerification",{children:["[userId]",{children:["[newEmail]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9815)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\otpVerification\\[userId]\\[newEmail]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\otpVerification\\[userId]\\[newEmail]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/otpVerification/[userId]/[newEmail]/page",pathname:"/otpVerification/[userId]/[newEmail]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(60687);r(43210);var a=r(4780);function i({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,162,658,367],()=>r(85441));module.exports=s})();