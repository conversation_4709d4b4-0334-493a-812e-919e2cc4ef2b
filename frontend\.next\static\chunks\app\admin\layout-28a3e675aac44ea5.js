(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7581],{285:(e,r,s)=>{"use strict";s.d(r,{$:()=>l});var t=s(5155);s(2115);var a=s(9708),i=s(2085),n=s(9434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:r,variant:s,size:i,asChild:l=!1,...o}=e,c=l?a.DX:"button";return(0,t.jsx)(c,{"data-slot":"button",className:(0,n.cn)(d({variant:s,size:i,className:r})),...o})}},1383:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,3578,23)),Promise.resolve().then(s.t.bind(s,4854,23)),Promise.resolve().then(s.bind(s,8543)),Promise.resolve().then(s.bind(s,5031)),Promise.resolve().then(s.bind(s,3779))},2346:(e,r,s)=>{"use strict";s.d(r,{w:()=>d});var t=s(5155),a=s(2115),i=s(7489),n=s(9434);let d=a.forwardRef((e,r)=>{let{className:s,orientation:a="horizontal",decorative:d=!0,...l}=e;return(0,t.jsx)(i.b,{ref:r,decorative:d,orientation:a,className:(0,n.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",s),...l})});d.displayName=i.b.displayName},2523:(e,r,s)=>{"use strict";s.d(r,{p:()=>i});var t=s(5155);s(2115);var a=s(9434);function i(e){let{className:r,type:s,...i}=e;return(0,t.jsx)("input",{type:s,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...i})}},3779:(e,r,s)=>{"use strict";s.d(r,{AuthProvider:()=>l,b:()=>d});var t=s(5155),a=s(2115),i=s(5654);let n=(0,a.createContext)(void 0),d=()=>{let e=(0,a.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},l=e=>{let{children:r}=e,[s,d]=(0,a.useState)(null),[l,o]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(async()=>{if(localStorage.getItem("access_token"))try{let e=await i.ZJ.getAdmin();e.success&&e.data&&d(e.data)}catch(e){e.response&&(console.error("Error checking auth status:",e.response.data.message),localStorage.removeItem("access_token"))}o(!1)})()},[]);let c=async()=>{try{await i.ZJ.logout()}catch(e){console.error("Error during logout:",e)}finally{localStorage.removeItem("access_token"),d(null)}};return(0,t.jsx)(n.Provider,{value:{admin:s,isLoading:l,isAuthenticated:!!s,login:(e,r)=>{localStorage.setItem("access_token",r),d(e)},logout:c},children:r})}},5031:(e,r,s)=>{"use strict";s.d(r,{default:()=>I});var t=s(5155),a=s(2115),i=s(3779),n=s(6874),d=s.n(n),l=s(5695),o=s(9434),c=s(3783),u=s(7108),m=s(7809),x=s(7580),h=s(2318),g=s(2713),v=s(1497),f=s(381),p=s(5937),b=s(4835),j=s(285),N=s(2346);let y=[{title:"Dashboard",href:"/admin",icon:c.A,description:"Overview and analytics"},{title:"Products",href:"/admin/products",icon:u.A,description:"Manage inventory"},{title:"Orders",href:"/admin/orders",icon:m.A,description:"Order management"},{title:"Customers",href:"/admin/customers",icon:x.A,description:"Customer management"},{title:"Admin Users",href:"/admin/admin-users",icon:h.A,description:"Manage admin accounts",superAdminOnly:!0},{title:"Analytics",href:"/admin/analytics",icon:g.A,description:"Sales and performance"},{title:"Messages",href:"/admin/messages",icon:v.A,description:"Customer support"},{title:"Settings",href:"/admin/settings",icon:f.A,description:"System configuration"}],w=()=>{let e=(0,l.usePathname)(),{admin:r,logout:s}=(0,i.b)(),a=async()=>{try{await s()}catch(e){console.error("Logout error:",e)}},n=y.filter(e=>!e.superAdminOnly||(null==r?void 0:r.role)==="superAdmin");return(0,t.jsxs)("div",{className:"w-64 bg-card border-r border-border flex flex-col h-screen",children:[(0,t.jsx)("div",{className:"p-6 border-b border-border",children:(0,t.jsxs)(d(),{href:"/admin",className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"h-8 w-8 text-primary"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-xl font-bold text-foreground",children:"Mega Mall"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Admin Panel"})]})]})}),(0,t.jsx)("nav",{className:"flex-1 p-4 space-y-2",children:n.map(r=>{let s=e===r.href||"/admin"!==r.href&&e.startsWith(r.href);return(0,t.jsxs)(d(),{href:r.href,className:(0,o.cn)("flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",s?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-accent"),children:[(0,t.jsx)(r.icon,{className:"h-5 w-5"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{children:r.title}),r.description&&(0,t.jsx)("div",{className:"text-xs opacity-70",children:r.description})]})]},r.href)})}),(0,t.jsxs)("div",{className:"p-4 border-t border-border",children:[(0,t.jsxs)("div",{className:"mb-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-foreground",children:null==r?void 0:r.fullName}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:null==r?void 0:r.email}),(0,t.jsx)("p",{className:"text-xs text-primary capitalize",children:null==r?void 0:r.role})]}),(0,t.jsx)(N.w,{className:"mb-3"}),(0,t.jsxs)(j.$,{variant:"ghost",size:"sm",onClick:a,className:"w-full justify-start text-muted-foreground hover:text-foreground",children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Logout"]})]})]})};var A=s(4783),k=s(7924),S=s(3861),E=s(2523),P=s(6126);let _=e=>{let r={"/admin":"Dashboard","/admin/products":"Product Management","/admin/orders":"Order Management","/admin/customers":"Customer Management","/admin/admin-users":"Admin User Management","/admin/analytics":"Analytics & Reports","/admin/messages":"Customer Messages","/admin/settings":"Settings"};if(r[e])return r[e];for(let[s,t]of Object.entries(r))if(e.startsWith(s)&&"/admin"!==s)return t;return"Admin Panel"},z=()=>{let e=_((0,l.usePathname)());return(0,t.jsxs)("header",{className:"h-16 bg-card border-b border-border px-6 flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(j.$,{variant:"ghost",size:"icon",className:"md:hidden",children:(0,t.jsx)(A.A,{className:"h-5 w-5"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-xl font-semibold text-foreground",children:e}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:new Date().toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"relative hidden md:block",children:[(0,t.jsx)(k.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(E.p,{placeholder:"Search...",className:"pl-10 w-64"})]}),(0,t.jsxs)(j.$,{variant:"ghost",size:"icon",className:"relative",children:[(0,t.jsx)(S.A,{className:"h-5 w-5"}),(0,t.jsx)(P.E,{variant:"destructive",className:"absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs",children:"3"})]}),(0,t.jsx)(j.$,{variant:"ghost",size:"icon",className:"md:hidden",children:(0,t.jsx)(k.A,{className:"h-5 w-5"})})]})]})};var M=s(5654),C=s(5057),O=s(6695),R=s(5365),D=s(8749),F=s(2657),L=s(1154),Z=s(8543);let $=()=>{let{login:e}=(0,i.b)(),[r,s]=(0,a.useState)({email:"",password:""}),[n,d]=(0,a.useState)(!1),[l,o]=(0,a.useState)(!1),[c,u]=(0,a.useState)(""),m=e=>{let{name:r,value:t}=e.target;s(e=>({...e,[r]:t})),c&&u("")},x=async s=>{s.preventDefault(),o(!0),u("");try{let s=await M.ZJ.login(r);if(s.success&&s.data){let{admin:r,access_token:t}=s.data;e(r,t),Z.oR.success("Login successful!")}else u(s.message||"Login failed")}catch(r){var t,a;let e=(null==(a=r.response)||null==(t=a.data)?void 0:t.message)||"An error occurred during login";u(e),Z.oR.error(e)}finally{o(!1)}};return(0,t.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"w-full max-w-md",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"flex justify-center mb-4",children:(0,t.jsx)(p.A,{className:"h-12 w-12 text-primary"})}),(0,t.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Mega Mall"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Admin Panel"})]}),(0,t.jsxs)(O.Zp,{children:[(0,t.jsxs)(O.aR,{children:[(0,t.jsx)(O.ZB,{children:"Admin Login"}),(0,t.jsx)(O.BT,{children:"Enter your credentials to access the admin panel"})]}),(0,t.jsx)(O.Wu,{children:(0,t.jsxs)("form",{onSubmit:x,className:"space-y-4",children:[c&&(0,t.jsx)(R.Fc,{variant:"destructive",children:c}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(C.J,{htmlFor:"email",children:"Email"}),(0,t.jsx)(E.p,{id:"email",name:"email",type:"email",placeholder:"<EMAIL>",value:r.email,onChange:m,required:!0,disabled:l})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(C.J,{htmlFor:"password",children:"Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(E.p,{id:"password",name:"password",type:n?"text":"password",placeholder:"Enter your password",value:r.password,onChange:m,required:!0,disabled:l}),(0,t.jsx)(j.$,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>d(!n),disabled:l,children:n?(0,t.jsx)(D.A,{className:"h-4 w-4"}):(0,t.jsx)(F.A,{className:"h-4 w-4"})})]})]}),(0,t.jsx)(j.$,{type:"submit",className:"w-full",disabled:l,children:l?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(L.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Signing in..."]}):"Sign In"})]})})]}),(0,t.jsx)("div",{className:"text-center mt-8 text-sm text-muted-foreground",children:(0,t.jsx)("p",{children:"\xa9 2024 Mega Mall. All rights reserved."})})]})})};var J=s(8856);let I=e=>{let{children:r}=e,{isAuthenticated:s,isLoading:a}=(0,i.b)();return a?(0,t.jsx)("div",{className:"min-h-screen bg-background",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsxs)("div",{className:"w-64 bg-card border-r border-border p-4",children:[(0,t.jsx)(J.E,{className:"h-8 w-32 mb-6"}),(0,t.jsx)("div",{className:"space-y-3",children:Array.from({length:8}).map((e,r)=>(0,t.jsx)(J.E,{className:"h-10 w-full"},r))})]}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"h-16 bg-card border-b border-border p-4",children:(0,t.jsx)(J.E,{className:"h-8 w-48"})}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)(J.E,{className:"h-8 w-64 mb-4"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:Array.from({length:4}).map((e,r)=>(0,t.jsx)(J.E,{className:"h-32 w-full"},r))}),(0,t.jsx)(J.E,{className:"h-64 w-full"})]})]})]})}):s?(0,t.jsx)("div",{className:"min-h-screen bg-background",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)(w,{}),(0,t.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,t.jsx)(z,{}),(0,t.jsx)("main",{className:"flex-1 p-6 overflow-auto",children:r})]})]})}):(0,t.jsx)($,{})}},5057:(e,r,s)=>{"use strict";s.d(r,{J:()=>n});var t=s(5155);s(2115);var a=s(968),i=s(9434);function n(e){let{className:r,...s}=e;return(0,t.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...s})}},5365:(e,r,s)=>{"use strict";s.d(r,{Fc:()=>l,TN:()=>o});var t=s(5155),a=s(2115),i=s(2085),n=s(9434);let d=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),l=a.forwardRef((e,r)=>{let{className:s,variant:a,...i}=e;return(0,t.jsx)("div",{ref:r,role:"alert",className:(0,n.cn)(d({variant:a}),s),...i})});l.displayName="Alert",a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("h5",{ref:r,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",s),...a})}).displayName="AlertTitle";let o=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",s),...a})});o.displayName="AlertDescription"},6126:(e,r,s)=>{"use strict";s.d(r,{E:()=>l});var t=s(5155);s(2115);var a=s(9708),i=s(2085),n=s(9434);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:r,variant:s,asChild:i=!1,...l}=e,o=i?a.DX:"span";return(0,t.jsx)(o,{"data-slot":"badge",className:(0,n.cn)(d({variant:s}),r),...l})}},6695:(e,r,s)=>{"use strict";s.d(r,{BT:()=>l,Wu:()=>o,ZB:()=>d,Zp:()=>i,aR:()=>n,wL:()=>c});var t=s(5155);s(2115);var a=s(9434);function i(e){let{className:r,...s}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...s})}function n(e){let{className:r,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...s})}function d(e){let{className:r,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",r),...s})}function l(e){let{className:r,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",r),...s})}function o(e){let{className:r,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",r),...s})}function c(e){let{className:r,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",r),...s})}},8856:(e,r,s)=>{"use strict";s.d(r,{E:()=>i});var t=s(5155),a=s(9434);function i(e){let{className:r,...s}=e;return(0,t.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",r),...s})}}},e=>{var r=r=>e(e.s=r);e.O(0,[421,4277,9078,8543,6874,5869,7389,8441,1684,7358],()=>r(1383)),_N_E=e.O()}]);