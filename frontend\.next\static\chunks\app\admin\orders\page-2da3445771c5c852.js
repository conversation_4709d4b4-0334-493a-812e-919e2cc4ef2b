(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4798],{646:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1007:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1264:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},1586:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},2346:(e,s,r)=>{"use strict";r.d(s,{w:()=>c});var t=r(5155),a=r(2115),l=r(7489),d=r(9434);let c=a.forwardRef((e,s)=>{let{className:r,orientation:a="horizontal",decorative:c=!0,...i}=e;return(0,t.jsx)(l.b,{ref:s,decorative:c,orientation:a,className:(0,d.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",r),...i})});c.displayName=l.b.displayName},2356:(e,s,r)=>{"use strict";r.d(s,{default:()=>q});var t=r(5155),a=r(2115),l=r(5654),d=r(285),c=r(2523),i=r(6695),n=r(6126),x=r(5365),m=r(8856),h=r(7108),o=r(4186),u=r(646),p=r(5868),j=r(7924),N=r(6932),g=r(5339),f=r(9799),y=r(9946);let v=(0,y.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var w=r(9074),b=r(1007),A=r(2657),k=r(5623),C=r(8543),S=r(4838),D=r(6766),O=r(2346),_=r(7550);let M=(0,y.A)("printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]]);var Z=r(3717),R=r(1264),z=r(9420),I=r(4516),W=r(1586);let P=e=>{let{order:s,onBack:r,onOrderUpdate:c}=e,[x,m]=(0,a.useState)(!1),j=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),N=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),g=async e=>{m(!0);try{let r={orderId:s._id,status:e},t=await l.ZJ.changeDeliveryStatus(r);t.success?(C.oR.success("Order status updated successfully"),c()):C.oR.error(t.message||"Failed to update order status")}catch(s){var r,t;let e=(null==(t=s.response)||null==(r=t.data)?void 0:r.message)||"An error occurred while updating order status";C.oR.error(e)}finally{m(!1)}},y=s.totalRevenue+s.codCharges;return(0,t.jsxs)("div",{className:"space-y-6 print:space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between print:hidden",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(d.$,{variant:"ghost",size:"icon",onClick:r,children:(0,t.jsx)(_.A,{className:"h-4 w-4"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Order Details"}),(0,t.jsxs)("p",{className:"text-muted-foreground",children:["Order #",s._id.slice(-8)]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(d.$,{variant:"outline",onClick:()=>{window.print()},children:[(0,t.jsx)(M,{className:"h-4 w-4 mr-2"}),"Print"]}),(0,t.jsxs)(S.rI,{children:[(0,t.jsx)(S.ty,{asChild:!0,children:(0,t.jsxs)(d.$,{disabled:x,children:[(0,t.jsx)(Z.A,{className:"h-4 w-4 mr-2"}),"Update Status"]})}),(0,t.jsxs)(S.SQ,{align:"end",children:[(0,t.jsxs)(S._2,{onClick:()=>g("pending"),children:[(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Mark as Pending"]}),(0,t.jsxs)(S._2,{onClick:()=>g("shipped"),children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Mark as Shipped"]}),(0,t.jsxs)(S._2,{onClick:()=>g("delivered"),children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Mark as Delivered"]}),(0,t.jsxs)(S._2,{onClick:()=>g("cancelled"),children:[(0,t.jsx)(v,{className:"h-4 w-4 mr-2"}),"Mark as Cancelled"]})]})]})]})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(i.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(h.A,{className:"h-5 w-5"}),(0,t.jsxs)("span",{children:["Order #",s._id.slice(-8)]})]}),(0,t.jsxs)(i.BT,{children:["Placed on ",N(s.createdAt)]})]}),(0,t.jsxs)(n.E,{className:"".concat((e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"shipped":return"bg-blue-100 text-blue-800";case"delivered":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(s.status)," flex items-center space-x-1"),children:[(e=>{switch(e){case"pending":return(0,t.jsx)(o.A,{className:"h-4 w-4"});case"shipped":return(0,t.jsx)(f.A,{className:"h-4 w-4"});case"delivered":return(0,t.jsx)(u.A,{className:"h-4 w-4"});case"cancelled":return(0,t.jsx)(v,{className:"h-4 w-4"});default:return(0,t.jsx)(h.A,{className:"h-4 w-4"})}})(s.status),(0,t.jsx)("span",{className:"capitalize",children:s.status})]})]})}),(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Items"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:s.orderItems.length})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total Amount"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-primary",children:j(y)})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Payment Method"}),(0,t.jsx)("p",{className:"text-lg font-medium",children:"Cash on Delivery"})]})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)(i.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(b.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Customer Information"})]})}),(0,t.jsx)(i.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-lg",children:s.user.fullName}),(0,t.jsxs)("div",{className:"space-y-2 mt-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,t.jsx)(R.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{children:s.user.email})]}),s.user.phoneNumber&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,t.jsx)(z.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{children:s.user.phoneNumber})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsxs)("span",{children:["Customer since ",N(s.user.createdAt)]})]})]})]})})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)(i.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(I.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Shipping Address"})]})}),(0,t.jsx)(i.Wu,{children:s.shippingDetails[0]?(0,t.jsxs)("div",{className:"space-y-2",children:[s.shippingDetails[0].address&&(0,t.jsx)("p",{className:"font-medium",children:s.shippingDetails[0].address}),(0,t.jsxs)("p",{children:[s.shippingDetails[0].city&&"".concat(s.shippingDetails[0].city,", "),s.shippingDetails[0].state&&"".concat(s.shippingDetails[0].state," "),s.shippingDetails[0].postalCode]}),s.shippingDetails[0].country&&(0,t.jsx)("p",{className:"text-muted-foreground",children:s.shippingDetails[0].country})]}):(0,t.jsx)("p",{className:"text-muted-foreground",children:"No address information available"})})]})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Order Items"}),(0,t.jsxs)(i.BT,{children:[s.orderItems.length," item(s) in this order"]})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:s.orderItems.map((e,s)=>{var r,a,l;return(0,t.jsxs)("div",{className:"flex items-center space-x-4 p-4 border border-border rounded-lg",children:[(null==(a=e.product.images)||null==(r=a[0])?void 0:r.url)?(0,t.jsx)(D.default,{src:null==(l=e.product.images[0])?void 0:l.url,alt:e.product.title,width:64,height:64,className:"object-cover rounded-lg"}):(0,t.jsx)("div",{className:"w-16 h-16 bg-muted rounded-lg flex items-center justify-center",children:(0,t.jsx)(h.A,{className:"h-6 w-6 text-muted-foreground"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h4",{className:"font-semibold",children:e.product.title}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-2",children:e.product.description}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 mt-2",children:[(0,t.jsxs)("span",{className:"text-sm",children:["Qty: ",e.quantity]}),(0,t.jsxs)("span",{className:"text-sm",children:["Weight: ",e.product.weight]}),(0,t.jsxs)("span",{className:"text-sm",children:["Category: ",e.product.category]})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("p",{className:"font-semibold",children:j(e.product.price)}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"per item"}),(0,t.jsx)("p",{className:"font-bold text-primary",children:j(e.product.price*e.quantity)})]})]},s)})})})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)(i.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(W.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Payment Summary"})]})}),(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Subtotal"}),(0,t.jsx)("span",{children:j(s.totalRevenue)})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"COD Charges"}),(0,t.jsx)("span",{children:j(s.codCharges)})]}),(0,t.jsx)(O.w,{}),(0,t.jsxs)("div",{className:"flex justify-between font-bold text-lg",children:[(0,t.jsx)("span",{children:"Total"}),(0,t.jsx)("span",{className:"text-primary",children:j(y)})]}),(0,t.jsxs)("div",{className:"mt-4 p-3 bg-muted rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Payment Method: Cash on Delivery"})]}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Payment will be collected upon delivery"})]})]})})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Order Timeline"}),(0,t.jsx)(i.BT,{children:"Track the progress of this order"})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-primary rounded-full flex items-center justify-center",children:(0,t.jsx)(u.A,{className:"h-4 w-4 text-primary-foreground"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Order Placed"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:N(s.createdAt)})]})]}),"pending"!==s.status&&(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat("shipped"===s.status||"delivered"===s.status?"bg-primary":"bg-muted"),children:(0,t.jsx)(f.A,{className:"h-4 w-4 ".concat("shipped"===s.status||"delivered"===s.status?"text-primary-foreground":"text-muted-foreground")})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Order Shipped"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"shipped"===s.status||"delivered"===s.status?"In transit":"Pending shipment"})]})]}),"delivered"===s.status&&(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center",children:(0,t.jsx)(u.A,{className:"h-4 w-4 text-white"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Order Delivered"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Successfully delivered"})]})]}),"cancelled"===s.status&&(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-red-500 rounded-full flex items-center justify-center",children:(0,t.jsx)(v,{className:"h-4 w-4 text-white"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Order Cancelled"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Order has been cancelled"})]})]})]})})]})]})},E=e=>{let s,{order:r,onStatusUpdate:a,onDelete:c,onViewDetails:x}=e;return r?(0,t.jsx)(i.Zp,{children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex-1 space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{className:"font-semibold text-lg",children:["Order #",r._id.slice(-8)]}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground flex items-center",children:[(0,t.jsx)(w.A,{className:"h-3 w-3 mr-1"}),l.Qo.formatOrderDate(r.createdAt)]})]}),(0,t.jsxs)(n.E,{className:"".concat((e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"shipped":return"bg-blue-100 text-blue-800";case"delivered":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(r.status)," flex items-center space-x-1"),children:[(e=>{switch(e){case"pending":return(0,t.jsx)(o.A,{className:"h-4 w-4"});case"shipped":return(0,t.jsx)(f.A,{className:"h-4 w-4"});case"delivered":return(0,t.jsx)(u.A,{className:"h-4 w-4"});case"cancelled":return(0,t.jsx)(v,{className:"h-4 w-4"});default:return(0,t.jsx)(h.A,{className:"h-4 w-4"})}})(r.status),(0,t.jsx)("span",{className:"capitalize",children:r.status})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Customer"}),(0,t.jsxs)("p",{className:"font-medium flex items-center",children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-1"}),r.user.fullName]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:r.user.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Items"}),(0,t.jsxs)("p",{className:"font-medium",children:[r.orderItems.length," item(s)"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total"}),(0,t.jsx)("p",{className:"font-bold text-lg text-primary",children:(s=r.totalRevenue,new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(s))})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(d.$,{variant:"outline",size:"sm",onClick:x,children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-1"}),"View"]}),(0,t.jsxs)(S.rI,{children:[(0,t.jsx)(S.ty,{asChild:!0,children:(0,t.jsx)(d.$,{variant:"outline",size:"sm",children:(0,t.jsx)(k.A,{className:"h-4 w-4"})})}),(0,t.jsxs)(S.SQ,{align:"end",children:[(0,t.jsx)(S._2,{onClick:()=>a(r._id,"pending"),children:"Mark as Pending"}),(0,t.jsx)(S._2,{onClick:()=>a(r._id,"shipped"),children:"Mark as Shipped"}),(0,t.jsx)(S._2,{onClick:()=>a(r._id,"delivered"),children:"Mark as Delivered"}),(0,t.jsx)(S._2,{onClick:()=>a(r._id,"cancelled"),children:"Mark as Cancelled"}),(0,t.jsx)(S._2,{onClick:c,className:"text-destructive",children:"Delete Order"})]})]})]})]})})}):(0,t.jsx)("div",{children:"No orders found"})},q=()=>{let[e,s]=(0,a.useState)([]),[r,n]=(0,a.useState)(!0),[f,y]=(0,a.useState)(""),[v,w]=(0,a.useState)(""),[b,A]=(0,a.useState)(""),[k,S]=(0,a.useState)(null),[D,O]=(0,a.useState)(!1),[_,M]=(0,a.useState)(0),Z=async()=>{try{n(!0);let e=await l.ZJ.getAllOrders();e.success&&e.data?(s(e.data.orders),M(e.data.totalSales)):y(e.message||"Failed to load orders")}catch(t){var e,r;let s=(null==(r=t.response)||null==(e=r.data)?void 0:e.message)||"An error occurred while loading orders";y(s),C.oR.error(s)}finally{n(!1)}};(0,a.useEffect)(()=>{Z()},[]);let R=async(e,s)=>{try{let r=await l.ZJ.changeDeliveryStatus({orderId:e,status:s});r.success?(C.oR.success("Order status updated successfully"),Z()):C.oR.error(r.message||"Failed to update order status")}catch(s){var r,t;let e=(null==(t=s.response)||null==(r=t.data)?void 0:r.message)||"An error occurred while updating order status";C.oR.error(e)}},z=async e=>{if(confirm("Are you sure you want to delete this order?"))try{let s=await l.ZJ.deleteOrder(e,"Order deleted by admin");s.success?(C.oR.success("Order deleted successfully"),Z()):C.oR.error(s.message||"Failed to delete order")}catch(t){var s,r;let e=(null==(r=t.response)||null==(s=r.data)?void 0:s.message)||"An error occurred while deleting order";C.oR.error(e)}},I=e=>{S(e),O(!0)},W=e.filter(e=>{let s=e._id.toLowerCase().includes(v.toLowerCase())||e.user.fullName.toLowerCase().includes(v.toLowerCase())||e.user.email.toLowerCase().includes(v.toLowerCase()),r=!b||e.status===b;return s&&r}),q=l.Qo.getOrderStats(e);return D&&k?(0,t.jsx)(P,{order:k,onBack:()=>{O(!1),S(null)},onOrderUpdate:()=>{Z(),O(!1),S(null)}}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Order Management"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"View and manage customer orders, update order status, and handle order-related tasks."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsx)(i.Zp,{children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Orders"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:q.total})]}),(0,t.jsx)(h.A,{className:"h-8 w-8 text-muted-foreground"})]})})}),(0,t.jsx)(i.Zp,{children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Pending Orders"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:q.pending})]}),(0,t.jsx)(o.A,{className:"h-8 w-8 text-yellow-600"})]})})}),(0,t.jsx)(i.Zp,{children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Delivered Orders"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-green-600",children:q.delivered})]}),(0,t.jsx)(u.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,t.jsx)(i.Zp,{children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Revenue"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-primary",children:new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(_)})]}),(0,t.jsx)(p.A,{className:"h-8 w-8 text-primary"})]})})})]}),(0,t.jsx)(i.Zp,{children:(0,t.jsx)(i.Wu,{className:"pt-6",children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,t.jsxs)("div",{className:"relative flex-1",children:[(0,t.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(c.p,{placeholder:"Search orders by ID, customer name, or email...",value:v,onChange:e=>w(e.target.value),className:"pl-10"})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("select",{value:b,onChange:e=>A(e.target.value),className:"px-3 py-2 border border-border rounded-md bg-background text-foreground",children:[(0,t.jsx)("option",{value:"",children:"All Statuses"}),(0,t.jsx)("option",{value:"pending",children:"Pending"}),(0,t.jsx)("option",{value:"shipped",children:"Shipped"}),(0,t.jsx)("option",{value:"delivered",children:"Delivered"}),(0,t.jsx)("option",{value:"cancelled",children:"Cancelled"})]}),(0,t.jsx)(d.$,{variant:"outline",size:"icon",children:(0,t.jsx)(N.A,{className:"h-4 w-4"})})]})]})})}),f&&(0,t.jsxs)(x.Fc,{variant:"destructive",children:[(0,t.jsx)(g.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{children:"Error loading orders"}),(0,t.jsx)("p",{children:f})]})]}),r&&(0,t.jsx)("div",{className:"space-y-4",children:Array.from({length:5}).map((e,s)=>(0,t.jsx)(i.Zp,{children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.E,{className:"h-4 w-32"}),(0,t.jsx)(m.E,{className:"h-4 w-48"}),(0,t.jsx)(m.E,{className:"h-4 w-24"})]}),(0,t.jsx)(m.E,{className:"h-8 w-20"})]})})},s))}),!r&&!f&&(0,t.jsx)(t.Fragment,{children:0===W.length?(0,t.jsx)(i.Zp,{children:(0,t.jsxs)(i.Wu,{className:"p-8 text-center",children:[(0,t.jsx)(h.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No orders found"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:v||b?"No orders match your current filters.":"No orders have been placed yet."})]})}):(0,t.jsx)("div",{className:"space-y-4",children:W.map(e=>(0,t.jsx)(E,{order:e,onStatusUpdate:R,onDelete:()=>z(e._id),onViewDetails:()=>I(e)},e._id))})})]})}},3584:(e,s,r)=>{Promise.resolve().then(r.bind(r,4368)),Promise.resolve().then(r.bind(r,2356))},4186:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4516:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},5868:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},7489:(e,s,r)=>{"use strict";r.d(s,{b:()=>n});var t=r(2115),a=r(3655),l=r(5155),d="horizontal",c=["horizontal","vertical"],i=t.forwardRef((e,s)=>{var r;let{decorative:t,orientation:i=d,...n}=e,x=(r=i,c.includes(r))?i:d;return(0,l.jsx)(a.sG.div,{"data-orientation":x,...t?{role:"none"}:{"aria-orientation":"vertical"===x?x:void 0,role:"separator"},...n,ref:s})});i.displayName="Separator";var n=i},9074:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9420:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},9799:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,9078,8543,6766,3888,9449,7389,5193,8441,1684,7358],()=>s(3584)),_N_E=e.O()}]);