exports.id=367,exports.ids=[367],exports.modules={2989:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,16444,23)),Promise.resolve().then(a.t.bind(a,16042,23)),Promise.resolve().then(a.t.bind(a,88170,23)),Promise.resolve().then(a.t.bind(a,49477,23)),Promise.resolve().then(a.t.bind(a,29345,23)),Promise.resolve().then(a.t.bind(a,12089,23)),Promise.resolve().then(a.t.bind(a,46577,23)),Promise.resolve().then(a.t.bind(a,31307,23))},4780:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n});var r=a(49384),s=a(82348);function n(...e){return(0,s.QP)((0,r.$)(e))}},15125:(e,t,a)=>{Promise.resolve().then(a.bind(a,93853)),Promise.resolve().then(a.bind(a,51317)),Promise.resolve().then(a.bind(a,15978)),Promise.resolve().then(a.bind(a,63213)),Promise.resolve().then(a.bind(a,28253))},15978:(e,t,a)=>{"use strict";a.d(t,{default:()=>P});var r=a(60687),s=a(28561),n=a(58869),i=a(86356),o=a(19080),l=a(40083),d=a(12941),c=a(85814),u=a.n(c),m=a(29523);a(43210);var h=a(26134),p=a(11860),g=a(4780);function x({...e}){return(0,r.jsx)(h.bL,{"data-slot":"sheet",...e})}function f({...e}){return(0,r.jsx)(h.l9,{"data-slot":"sheet-trigger",...e})}function y({...e}){return(0,r.jsx)(h.ZL,{"data-slot":"sheet-portal",...e})}function v({className:e,...t}){return(0,r.jsx)(h.hJ,{"data-slot":"sheet-overlay",className:(0,g.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function b({className:e,children:t,side:a="right",...s}){return(0,r.jsxs)(y,{children:[(0,r.jsx)(v,{}),(0,r.jsxs)(h.UC,{"data-slot":"sheet-content",className:(0,g.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===a&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===a&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===a&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===a&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...s,children:[t,(0,r.jsxs)(h.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,r.jsx)(p.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}var w=a(21342),j=a(63213),A=a(28253),N=a(93853);let C=[{name:"Home",href:"/"},{name:"Shop",href:"/shop"},{name:"About",href:"/about"},{name:"Contact",href:"/contact"}];function P(){let{user:e,isAuthenticated:t,logout:a}=(0,j.A)(),{itemCount:c}=(0,A._)(),h=async()=>{try{await a(),N.oR.success("Logged out successfully")}catch(e){console.log(e)}};return(0,r.jsx)("header",{className:"w-full border-b border-gray-200 bg-white/95 backdrop-blur-sm sticky top-0 z-50 shadow-sm",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:[(0,r.jsx)(u(),{href:"/",className:"text-xl font-bold text-primary transition-colors hover:text-primary/90",children:"Mega Mall"}),(0,r.jsx)("nav",{className:"hidden md:flex items-center space-x-8",children:C.map(e=>(0,r.jsx)(u(),{href:e.href,className:"text-sm font-medium text-gray-700 hover:text-primary transition-colors relative after:absolute after:bottom-[-4px] after:left-0 after:h-[2px] after:w-0 after:bg-primary after:transition-all hover:after:w-full",children:e.name},e.name))}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-4",children:[(0,r.jsx)(u(),{href:"/cart",className:"relative",children:(0,r.jsxs)(m.$,{variant:"ghost",size:"icon",className:"rounded-full hover:bg-gray-100",children:[(0,r.jsx)(s.A,{className:"w-5 h-5"}),c>0&&(0,r.jsx)("span",{className:"absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:c>99?"99+":c})]})}),t?(0,r.jsxs)(w.rI,{children:[(0,r.jsx)(w.ty,{asChild:!0,children:(0,r.jsx)(m.$,{variant:"ghost",size:"icon",className:"rounded-full hover:bg-gray-100",children:(0,r.jsx)(n.A,{className:"w-5 h-5"})})}),(0,r.jsxs)(w.SQ,{align:"end",className:"w-48",children:[(0,r.jsx)("div",{className:"px-2 py-1.5 text-sm font-medium text-gray-900",children:e?.fullName}),(0,r.jsx)("div",{className:"px-2 py-1.5 text-xs text-gray-500",children:e?.email}),(0,r.jsx)(w.mB,{}),(0,r.jsxs)(w._2,{className:"cursor-pointer py-2",children:[(0,r.jsx)(i.A,{className:"w-4 h-4 mr-2"}),(0,r.jsx)(u(),{href:"/profile",className:"flex w-full",children:"Profile"})]}),(0,r.jsxs)(w._2,{className:"cursor-pointer py-2",children:[(0,r.jsx)(o.A,{className:"w-4 h-4 mr-2"}),(0,r.jsx)(u(),{href:"/orders",className:"flex w-full",children:"Orders"})]}),(0,r.jsx)(w.mB,{}),(0,r.jsxs)(w._2,{className:"cursor-pointer py-2 text-destructive focus:text-destructive",onClick:h,children:[(0,r.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"Logout"]})]})]}):(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(u(),{href:"/signin",children:(0,r.jsx)(m.$,{variant:"ghost",size:"sm",className:"text-sm font-medium",children:"Sign In"})}),(0,r.jsx)(u(),{href:"/signup",children:(0,r.jsx)(m.$,{size:"sm",className:"text-sm font-medium",children:"Sign Up"})})]}),(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsxs)(x,{children:[(0,r.jsx)(f,{asChild:!0,children:(0,r.jsx)(m.$,{variant:"ghost",size:"icon",className:"rounded-full hover:bg-gray-100",children:(0,r.jsx)(d.A,{className:"w-5 h-5"})})}),(0,r.jsxs)(b,{side:"left",className:"w-[250px]",children:[(0,r.jsx)(u(),{href:"/",className:"flex items-center py-6 text-xl font-bold text-primary",children:"Mega Mall"}),(0,r.jsxs)("div",{className:"flex flex-col space-y-6 mt-6",children:[C.map(e=>(0,r.jsx)(u(),{href:e.href,className:"text-base font-medium text-gray-700 hover:text-primary transition-colors",children:e.name},e.name)),(0,r.jsxs)(u(),{href:"/cart",className:"flex items-center justify-between text-base font-medium text-gray-700 hover:text-primary transition-colors",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(s.A,{className:"w-4 h-4"}),"Cart"]}),c>0&&(0,r.jsx)("span",{className:"bg-primary text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:c>99?"99+":c})]}),(0,r.jsx)("div",{className:"h-px bg-gray-200 my-2"}),t?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900 mb-2",children:e?.fullName}),(0,r.jsx)(u(),{href:"/profile",className:"text-base font-medium text-gray-700 hover:text-primary",children:"Profile"}),(0,r.jsx)(u(),{href:"/orders",className:"text-base font-medium text-gray-700 hover:text-primary",children:"Orders"}),(0,r.jsx)("button",{onClick:h,className:"text-base font-medium text-destructive text-left w-full",children:"Logout"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u(),{href:"/signin",className:"text-base font-medium text-gray-700 hover:text-primary",children:"Sign In"}),(0,r.jsx)(u(),{href:"/signup",className:"text-base font-medium text-primary hover:text-primary/80",children:"Sign Up"})]})]})]})]})})]})]})})}},21342:(e,t,a)=>{"use strict";a.d(t,{SQ:()=>l,_2:()=>d,mB:()=>c,rI:()=>i,ty:()=>o});var r=a(60687);a(43210);var s=a(29398),n=a(4780);function i({...e}){return(0,r.jsx)(s.bL,{"data-slot":"dropdown-menu",...e})}function o({...e}){return(0,r.jsx)(s.l9,{"data-slot":"dropdown-menu-trigger",...e})}function l({className:e,sideOffset:t=4,...a}){return(0,r.jsx)(s.ZL,{children:(0,r.jsx)(s.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,n.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...a})})}function d({className:e,inset:t,variant:a="default",...i}){return(0,r.jsx)(s.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":a,className:(0,n.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i})}function c({className:e,...t}){return(0,r.jsx)(s.wv,{"data-slot":"dropdown-menu-separator",className:(0,n.cn)("bg-border -mx-1 my-1 h-px",e),...t})}},28253:(e,t,a)=>{"use strict";a.d(t,{CartProvider:()=>c,_:()=>d});var r=a(60687),s=a(43210),n=a(58376),i=a(63213),o=a(93853);let l=(0,s.createContext)(void 0),d=()=>{let e=(0,s.useContext)(l);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},c=({children:e})=>{let[t,a]=(0,s.useState)(null),[d,c]=(0,s.useState)(!1),[u,m]=(0,s.useState)(!1),{isAuthenticated:h,user:p}=(0,i.A)(),g=t?.length||0;(0,s.useEffect)(()=>{h&&p?x():a(null)},[h,p,x]);let x=async()=>{if(h)try{c(!0);let e=await n.CV.getCartItems();e.success&&e.data&&a(e.data)}catch{a(null)}finally{c(!1)}},f=async(e,t)=>{if(!h)return o.oR.error("Please sign in to add items to cart"),!1;try{m(!0);let r=await n.CV.addToCart({productId:e,quantity:t});if(r.success&&r.data)return a(r.data),o.oR.success("Item added to cart successfully"),!0;return!1}catch(e){return o.oR.error(e.response?.data.message||"Failed to add item to cart"),!1}finally{m(!1)}},y=async(e,t)=>{if(!h||t<1)return!1;try{m(!0);let r=await n.CV.updateQuantity({productId:e,quantity:t});if(r.success&&r.data)return o.oR.success("Quantity updated successfully"),a(r.data),!0;return!1}catch(e){return o.oR.error(e.response?.data.message||"Failed to update quantity"),!1}finally{m(!1)}},v=async e=>{if(!h)return!1;try{m(!0);let t=await n.CV.removeProduct(e);if(t.success&&t.data)return a(t.data),o.oR.success("Item removed from cart"),!0;return!1}catch(e){return o.oR.error(e.response?.data.message||"Failed to remove item"),!1}finally{m(!1)}};return(0,r.jsx)(l.Provider,{value:{cart:t,isLoading:d,isUpdating:u,itemCount:g,addToCart:f,updateQuantity:y,removeFromCart:v,clearCart:()=>{a(null)},refreshCart:x,getCartCalculations:()=>{if(!t)return{subtotal:0,tax:0,shipping:0,total:0};let e=n.CV.calculateCartTotal(t),a=n.CV.calculateTax(e),r=n.CV.calculateShipping(e),s=e+a+r;return{subtotal:e,tax:a,shipping:r,total:s}}},children:e})}},28659:(e,t,a)=>{"use strict";a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\components\\\\Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\Footer.tsx","default")},29131:(e,t,a)=>{"use strict";a.d(t,{AuthProvider:()=>s});var r=a(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\contexts\\AuthContext.tsx","useAuth");let s=(0,r.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\contexts\\AuthContext.tsx","AuthProvider")},29523:(e,t,a)=>{"use strict";a.d(t,{$:()=>l});var r=a(60687);a(43210);var s=a(8730),n=a(24224),i=a(4780);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:a,asChild:n=!1,...l}){let d=n?s.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:a,className:e})),...l})}},37043:(e,t,a)=>{"use strict";a.d(t,{CartProvider:()=>s});var r=a(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\contexts\\CartContext.tsx","useCart");let s=(0,r.registerClientReference)(function(){throw Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\contexts\\CartContext.tsx","CartProvider")},43520:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let r=a(51060).A.create({baseURL:process.env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:5000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});r.interceptors.request.use(e=>{let t=e.url?.includes("/admin")?localStorage.getItem("admin_token"):localStorage.getItem("token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response?.status===401&&(e.config?.url?.includes("/admin")?(localStorage.removeItem("admin_token"),window.location.href="/admin"):(localStorage.removeItem("token"),window.location.href="/login")),Promise.reject(e)));let s=r},51317:(e,t,a)=>{"use strict";a.d(t,{default:()=>w});var r=a(60687),s=a(85814),n=a.n(s),i=a(19526),o=a(72575),l=a(66232),d=a(84113),c=a(88059),u=a(13943),m=a(99891),h=a(85778),p=a(97992),g=a(48340),x=a(41550),f=a(29523);let y={shop:[{name:"All Products",href:"/shop"},{name:"New Arrivals",href:"/shop"},{name:"Best Sellers",href:"/"},{name:"Sale",href:"/shop"},{name:"Categories",href:"/"}],company:[{name:"About Us",href:"/about"},{name:"Careers",href:"/"},{name:"Press",href:"/"},{name:"Blog",href:"/"},{name:"Sustainability",href:"/"}],support:[{name:"Contact Us",href:"/contact"},{name:"FAQ",href:"/"},{name:"Size Guide",href:"/"},{name:"Shipping Info",href:"/"},{name:"Returns",href:"/orders"}],legal:[{name:"Privacy Policy",href:"/"},{name:"Terms of Service",href:"/"},{name:"Cookie Policy",href:"/"},{name:"Accessibility",href:"/"}]},v=[{name:"Facebook",href:"#",icon:i.A},{name:"Twitter",href:"#",icon:o.A},{name:"Instagram",href:"#",icon:l.A},{name:"YouTube",href:"#",icon:d.A}],b=[{icon:c.A,title:"Free Shipping",description:"On orders over $50"},{icon:u.A,title:"Easy Returns",description:"30-day return policy"},{icon:m.A,title:"Secure Payment",description:"SSL encrypted checkout"},{icon:h.A,title:"Multiple Payment",description:"Various payment options"}];function w(){return(0,r.jsxs)("footer",{className:"bg-gray-50 border-t border-gray-200",children:[(0,r.jsx)("div",{className:"border-b border-gray-200 bg-white",children:(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:b.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center space-x-3 text-center sm:text-left",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center",children:(0,r.jsx)(e.icon,{className:"w-6 h-6 text-primary"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]})]},t))})})}),(0,r.jsx)("div",{className:"bg-primary text-white",children:(0,r.jsx)("div",{className:"container mx-auto px-4 py-12",children:(0,r.jsxs)("div",{className:"max-w-2xl mx-auto text-center",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"Stay in the loop"}),(0,r.jsx)("p",{className:"text-primary-foreground/80 mb-6",children:"Subscribe to our newsletter for exclusive deals, new arrivals, and style tips."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 max-w-md mx-auto",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("input",{type:"email",placeholder:"Enter your email",className:"w-full px-4 py-3 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white/20"})}),(0,r.jsx)(f.$,{variant:"secondary",className:"px-6 py-3 bg-white text-primary hover:bg-gray-100 font-semibold",children:"Subscribe"})]})]})})}),(0,r.jsx)("div",{className:"container mx-auto px-4 py-12",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2",children:[(0,r.jsx)(n(),{href:"/",className:"text-2xl font-bold text-primary mb-4 block",children:"Mega Mall"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6 max-w-sm",children:"Your one-stop destination for quality products at unbeatable prices. Discover the latest trends and timeless classics."}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 text-gray-600",children:[(0,r.jsx)(p.A,{className:"w-4 h-4 flex-shrink-0"}),(0,r.jsx)("span",{className:"text-sm",children:"123 Shopping Street, City, State 12345"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3 text-gray-600",children:[(0,r.jsx)(g.A,{className:"w-4 h-4 flex-shrink-0"}),(0,r.jsx)("span",{className:"text-sm",children:"+1 (555) 123-4567"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3 text-gray-600",children:[(0,r.jsx)(x.A,{className:"w-4 h-4 flex-shrink-0"}),(0,r.jsx)("span",{className:"text-sm",children:"<EMAIL>"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 mb-4",children:"Shop"}),(0,r.jsx)("ul",{className:"space-y-3",children:y.shop.map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:e.href,className:"text-gray-600 hover:text-primary transition-colors text-sm",children:e.name})},e.name))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 mb-4",children:"Company"}),(0,r.jsx)("ul",{className:"space-y-3",children:y.company.map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:e.href,className:"text-gray-600 hover:text-primary transition-colors text-sm",children:e.name})},e.name))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 mb-4",children:"Support"}),(0,r.jsx)("ul",{className:"space-y-3",children:y.support.map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:e.href,className:"text-gray-600 hover:text-primary transition-colors text-sm",children:e.name})},e.name))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 mb-4",children:"Legal"}),(0,r.jsx)("ul",{className:"space-y-3",children:y.legal.map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:e.href,className:"text-gray-600 hover:text-primary transition-colors text-sm",children:e.name})},e.name))})]})]})}),(0,r.jsx)("div",{className:"border-t border-gray-200 bg-white",children:(0,r.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:["\xa9 ",new Date().getFullYear()," Mega Mall. All rights reserved."]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600 mr-2",children:"Follow us:"}),v.map(e=>(0,r.jsx)(n(),{href:e.href,className:"text-gray-400 hover:text-primary transition-colors","aria-label":e.name,children:(0,r.jsx)(e.icon,{className:"w-5 h-5"})},e.name))]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600 mr-2",children:"We accept:"}),(0,r.jsxs)("div",{className:"flex space-x-1",children:[(0,r.jsx)("div",{className:"w-8 h-5 bg-gray-200 rounded text-xs flex items-center justify-center font-bold text-gray-600",children:"VISA"}),(0,r.jsx)("div",{className:"w-8 h-5 bg-gray-200 rounded text-xs flex items-center justify-center font-bold text-gray-600",children:"MC"}),(0,r.jsx)("div",{className:"w-8 h-5 bg-gray-200 rounded text-xs flex items-center justify-center font-bold text-gray-600",children:"PP"})]})]})]})})})]})}},54351:(e,t,a)=>{"use strict";a.d(t,{j:()=>n});var r=a(43520);class s{async getAllProducts(){return(await r.A.get("/product/get-all-products")).data}async getProduct(e){return(await r.A.get(`/product/get-product/${e}`)).data}async getProductsByCategory(e={}){let t=new URLSearchParams;return e.limit&&t.append("limit",e.limit.toString()),e.page&&t.append("page",e.page.toString()),e.category&&t.append("category",e.category),e.minPrice&&t.append("minPrice",e.minPrice.toString()),e.maxPrice&&t.append("maxPrice",e.maxPrice.toString()),e.sortBy&&t.append("sortBy",e.sortBy),e.sortOrder&&t.append("sortOrder",e.sortOrder),(await r.A.get(`/product/get-product-by-category?${t.toString()}`)).data}async searchProducts(e,t={}){let a=new URLSearchParams;return t.limit&&a.append("limit",t.limit.toString()),t.page&&a.append("page",t.page.toString()),(await r.A.get(`/product/get-product-by-query?${a.toString()}`,{data:{query:e}})).data}async addReview(e,t){return(await r.A.post(`/product/reviews/${e}`,t)).data}async getReviews(e){return(await r.A.get(`/product/get-reviews/${e}`)).data}filterProducts(e,t){return e.filter(e=>(!t.category||e.category===t.category)&&(!t.minPrice||!(e.price<t.minPrice))&&(!t.maxPrice||!(e.price>t.maxPrice))&&(!t.minRating||!((e.averageRating||0)<t.minRating)))}sortProducts(e,t,a="asc"){return[...e].sort((e,r)=>{let s=0;switch(t){case"price":s=e.price-r.price;break;case"rating":s=(e.averageRating||0)-(r.averageRating||0);break;case"createdAt":s=new Date(e.createdAt).getTime()-new Date(r.createdAt).getTime()}return"desc"===a?-s:s})}getCategories(e){return[...new Set(e.map(e=>e.category))]}getPriceRange(e){if(0===e.length)return{min:0,max:0};let t=e.map(e=>e.price);return{min:Math.min(...t),max:Math.max(...t)}}}let n=new s},58376:(e,t,a)=>{"use strict";a.d(t,{ZJ:()=>n,CV:()=>c,Qo:()=>m,jU:()=>l.j,Dv:()=>o});var r=a(43520);class s{async signUp(e){return(await r.A.post("/admin/sign-up",e)).data}async login(e){return(await r.A.post("/admin/login",e)).data}async logout(){return(await r.A.post("/admin/logout")).data}async getAdmin(){return(await r.A.get("admin/get-admin")).data}async uploadProduct(e){let t=new FormData;return t.append("title",e.title),t.append("description",e.description),t.append("price",e.price.toString()),t.append("category",e.category),t.append("weight",e.weight),t.append("stock",e.stock.toString()),e.images.forEach(e=>{t.append("images",e)}),(await r.A.post("/admin/upload-product",t,{headers:{"Content-Type":"multipart/form-data"}})).data}async updateProduct(e,t){return(await r.A.patch(`/admin/update-product/${e}`,t)).data}async deleteProduct(e){return(await r.A.delete(`/admin/delete-product/${e}`)).data}async suggestProductDetails(e){let t=new FormData;return t.append("image",e),(await r.A.post("/admin/suggest-product-details",t,{headers:{"Content-Type":"multipart/form-data"}})).data}async changeDeliveryStatus(e){return(await r.A.patch("/admin/change-delivery-status",e)).data}async deleteOrder(e,t){return(await r.A.delete("/admin/delete-order",{data:{orderId:e,message:t}})).data}async getUserOrders(e){return(await r.A.get(`/admin/user-orders/${e}`)).data}async getAllOrders(){return(await r.A.get("/admin/orders")).data}async getAllUsers(){return(await r.A.get("/admin/users")).data}async getUserById(e){return(await r.A.get(`/admin/user/${e}`)).data}async getAllAdmins(){return(await r.A.get("/admin/admins")).data}async deleteAdmin(e){return(await r.A.delete(`/delete-admin/${e}`)).data}async getDashboard(){return(await r.A.get("/admin/dashboard")).data}async sendMessageToUser(e,t){return(await r.A.post(`/message/send-message-by-admin/${e}`,{message:t})).data}}let n=new s;class i{async signUp(e){return(await r.A.post("/user/sign-up",e)).data}async signIn(e){let t=await r.A.post("/user/sign-in",e);return t.data.success&&t.data.data?.token&&localStorage.setItem("token",t.data.data.token),t.data}async verifyOtp(e,t){return(await r.A.post(`/user/verify-otp/${e}`,{otp:t})).data}async resendOtp(e,t){return(await r.A.post(`/user/resend-otp/${e}`,{email:t})).data}async logout(){let e=await r.A.post("/user/logout");return localStorage.removeItem("token"),e.data}async getUser(){return(await r.A.get("/user/get-user")).data}async updatePreferences(e){return(await r.A.patch("/user/update-preferences",e)).data}async getAvatar(){return(await r.A.get("/user/get-avatar")).data}async updatePassword(e){return(await r.A.patch("/user/update-password",e)).data}async emailVerification(e){return(await r.A.post("/user/email-verification",e)).data}async updateEmail(e){return(await r.A.patch("/user/update-email",e)).data}async updatePersonalInfo(e){let t=await r.A.patch("/user/update-personal-info",e);return console.log(t),t.data}async updateAvatar(e){let t=new FormData;return t.append("avatar",e),(await r.A.patch("/user/update-avatar",t,{headers:{"Content-Type":"multipart/form-data"}})).data}async deleteAccount(e){let t=await r.A.delete("/user/delete-account",{data:{password:e}});return localStorage.removeItem("token"),t.data}}let o=new i;var l=a(54351);class d{async addToCart(e){return(await r.A.post("/cart/add-to-cart",e)).data}async getCartItems(){return(await r.A.get("/cart/get-cart-items")).data}async updateQuantity(e){return(await r.A.patch("/cart/toggle-quantity",e)).data}async removeProduct(e){return(await r.A.delete("/cart/remove-product",{data:{productId:e}})).data}async clearCart(){return(await r.A.delete("/cart/clear")).data}calculateCartTotal(e){return e.reduce((e,t)=>e+t.price*t.quantity,0)}calculateItemSubtotal(e){return e.price*e.quantity}getTotalItems(e){return e.reduce((e,t)=>e+t.quantity,0)}isProductInCart(e,t){return e.some(e=>e.productId===t)}getCartItem(e,t){return e.find(e=>e.productId===t)}calculateShipping(e,t=50){return e>=t?0:10}calculateTax(e,t=.08){return e*t}calculateFinalTotal(e,t={}){let a=this.calculateCartTotal(e),r=this.calculateShipping(a,t.freeShippingThreshold),s=this.calculateTax(a,t.taxRate),n=a+r+s;return{subtotal:a,shipping:r,tax:s,total:n}}validateCart(e){let t=[];return 0===e.length&&t.push("Cart is empty"),e.forEach(e=>{e.quantity<=0&&t.push(`Invalid quantity for ${e.title}`),e.quantity>e.stock&&t.push(`Not enough stock for ${e.title}. Available: ${e.stock}`)}),{isValid:0===t.length,errors:t}}}let c=new d;class u{async placeOrder(e){return(await r.A.post("/order/place-order",e)).data}async getOrders(){return(await r.A.get("/order/get-orders")).data}async cancelOrder(e){return(await r.A.delete("/order/cancel-order-by-user",{data:e})).data}getOrdersByStatus(e,t){return e.filter(e=>e.status===t)}getOrderById(e,t){return e.find(e=>e._id===t)}calculateOrderTotal(e){return e.orderItems.reduce((e,t)=>e+t.price*t.quantity,0)}getOrderStatusColor(e){return({pending:"text-yellow-600 bg-yellow-100",processing:"text-blue-600 bg-blue-100",shipped:"text-purple-600 bg-purple-100",delivered:"text-green-600 bg-green-100",cancelled:"text-red-600 bg-red-100"})[e]||"text-gray-600 bg-gray-100"}getOrderStatusText(e){return({pending:"Pending",shipped:"Shipped",delivered:"Delivered",cancelled:"Cancelled"})[e]||"Unknown"}canCancelOrder(e){return["pending","processing"].includes(e.status)}getEstimatedDeliveryDate(e){if("delivered"===e.status)return null;let t=new Date(e.createdAt),a={pending:7,processing:5,shipped:3,delivered:0,cancelled:0}[e.status]||7,r=new Date(t);return r.setDate(r.getDate()+a),r}formatOrderDate(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}getOrderStats(e){let t={total:e.length,pending:0,processing:0,shipped:0,delivered:0,cancelled:0,totalRevenue:0};return e.forEach(e=>{t[e.status]++,"cancelled"!==e.status&&(t.totalRevenue=e.totalRevenue)}),t}sortOrdersByDate(e,t=!1){return[...e].sort((e,a)=>{let r=new Date(e.createdAt).getTime(),s=new Date(a.createdAt).getTime();return t?r-s:s-r})}filterOrdersByDateRange(e,t,a){return e.filter(e=>{let r=new Date(e.createdAt);return r>=t&&r<=a})}}let m=new u;class h{async sendMessageByUser(e){return(await r.A.post("/message/send-message-by-user",e)).data}async getMessages(){return(await r.A.get("/message/get-messages")).data}sortMessagesByDate(e,t=!0){return[...e].sort((e,a)=>{let r=new Date(e.createdAt).getTime(),s=new Date(a.createdAt).getTime();return t?r-s:s-r})}groupMessagesByDate(e){let t={};return e.forEach(e=>{let a=new Date(e.createdAt).toDateString();t[a]||(t[a]=[]),t[a].push(e)}),t}formatMessageTime(e){return new Date(e).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"})}formatMessageDate(e){let t=new Date(e),a=new Date,r=new Date(a);return(r.setDate(r.getDate()-1),t.toDateString()===a.toDateString())?"Today":t.toDateString()===r.toDateString()?"Yesterday":t.toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})}getUnreadMessagesCount(e){return e.filter(e=>e.isFromAdmin&&!e.isRead).length}getLastMessage(e){return 0===e.length?null:this.sortMessagesByDate(e,!1)[0]}searchMessages(e,t){let a=t.toLowerCase();return e.filter(e=>e.message.toLowerCase().includes(a))}getMessagesByType(e,t){return e.filter(e=>e.isFromAdmin===t)}getConversationSummary(e){let t=this.getMessagesByType(e,!1),a=this.getMessagesByType(e,!0),r=this.getLastMessage(e);return{totalMessages:e.length,userMessages:t.length,adminMessages:a.length,lastMessageDate:r?r.createdAt:null}}}new h},61135:()=>{},63213:(e,t,a)=>{"use strict";a.d(t,{A:()=>o,AuthProvider:()=>l});var r=a(60687),s=a(43210),n=a(58376);let i=(0,s.createContext)(void 0),o=()=>{let e=(0,s.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},l=({children:e})=>{let[t,a]=(0,s.useState)(null),[o,l]=(0,s.useState)(!0),d=!!t;(0,s.useEffect)(()=>{(async()=>{if(localStorage.getItem("token"))try{let e=await n.Dv.getUser();e.success&&e.data&&a(e.data)}catch(e){e.response&&(console.error("Error checking auth status:",e.response.data.message),localStorage.removeItem("token"))}l(!1)})()},[]);let c=async()=>{try{await n.Dv.logout()}catch(e){console.error("Error during logout:",e)}finally{localStorage.removeItem("token"),a(null)}},u=async()=>{try{let e=await n.Dv.getUser();e.success&&e.data&&a(e.data)}catch(e){console.error("Error refreshing user data:",e)}};return(0,r.jsx)(i.Provider,{value:{user:t,isLoading:o,isAuthenticated:d,login:(e,t)=>{localStorage.setItem("token",t),a(e)},logout:c,updateUser:e=>{t&&a({...t,...e})},refreshUser:u},children:e})}},73269:(e,t,a)=>{Promise.resolve().then(a.bind(a,81819)),Promise.resolve().then(a.bind(a,28659)),Promise.resolve().then(a.bind(a,86964)),Promise.resolve().then(a.bind(a,29131)),Promise.resolve().then(a.bind(a,37043))},84429:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,86346,23)),Promise.resolve().then(a.t.bind(a,27924,23)),Promise.resolve().then(a.t.bind(a,35656,23)),Promise.resolve().then(a.t.bind(a,40099,23)),Promise.resolve().then(a.t.bind(a,38243,23)),Promise.resolve().then(a.t.bind(a,28827,23)),Promise.resolve().then(a.t.bind(a,62763,23)),Promise.resolve().then(a.t.bind(a,97173,23))},86964:(e,t,a)=>{"use strict";a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\components\\\\NavBar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\NavBar.tsx","default")},94431:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p,metadata:()=>h});var r=a(37413);a(56070);var s=a(22376),n=a.n(s),i=a(68726),o=a.n(i);a(61135);var l=a(86964),d=a(81819),c=a(28659),u=a(29131),m=a(37043);let h={title:"Mega Mall",description:"Your one-stop destination for quality products at unbeatable prices."};function p({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${n().variable} ${o().variable} antialiased`,children:(0,r.jsx)(u.AuthProvider,{children:(0,r.jsxs)(m.CartProvider,{children:[(0,r.jsx)(l.default,{}),e,(0,r.jsx)(d.ToastContainer,{position:"top-right",autoClose:3e3,hideProgressBar:!1,newestOnTop:!0,closeOnClick:!0,pauseOnHover:!0,theme:"light"}),(0,r.jsx)(c.default,{})]})})})})}}};