(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4005],{283:(e,t,s)=>{"use strict";s.d(t,{A:()=>n,AuthProvider:()=>c});var a=s(5155),r=s(2115),l=s(5654);let i=(0,r.createContext)(void 0),n=()=>{let e=(0,r.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},c=e=>{let{children:t}=e,[s,n]=(0,r.useState)(null),[c,d]=(0,r.useState)(!0),o=!!s;(0,r.useEffect)(()=>{(async()=>{if(localStorage.getItem("token"))try{let e=await l.Dv.getUser();e.success&&e.data&&n(e.data)}catch(e){e.response&&(console.error("Error checking auth status:",e.response.data.message),localStorage.removeItem("token"))}d(!1)})()},[]);let u=async()=>{try{await l.Dv.logout()}catch(e){console.error("Error during logout:",e)}finally{localStorage.removeItem("token"),n(null)}},m=async()=>{try{let e=await l.Dv.getUser();e.success&&e.data&&n(e.data)}catch(e){console.error("Error refreshing user data:",e)}};return(0,a.jsx)(i.Provider,{value:{user:s,isLoading:c,isAuthenticated:o,login:(e,t)=>{localStorage.setItem("token",t),n(e)},logout:u,updateUser:e=>{s&&n({...s,...e})},refreshUser:m},children:t})}},285:(e,t,s)=>{"use strict";s.d(t,{$:()=>c});var a=s(5155);s(2115);var r=s(9708),l=s(2085),i=s(9434);let n=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:s,size:l,asChild:c=!1,...d}=e,o=c?r.DX:"button";return(0,a.jsx)(o,{"data-slot":"button",className:(0,i.cn)(n({variant:s,size:l,className:t})),...d})}},434:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>F});var a=s(5155),r=s(5695),l=s(7550),i=s(6151),n=s(285),c=s(6695),d=s(7023),o=s(5323),u=s(9053),m=s(2115),x=s(6766),h=s(7712),p=s(4616),g=s(1154),f=s(2525),v=s(2523),y=s(6126),j=s(6408);let b=e=>{let{item:t,onQuantityUpdate:s,onRemove:r,isUpdating:l}=e,[i,d]=(0,m.useState)(t.quantity),[o,u]=(0,m.useState)(!1),b=async e=>{e<1||e===t.quantity||(u(!0),d(e),await s(t.productId,e)||d(t.quantity),u(!1))},N=async()=>{u(!0),await r(t.productId),u(!1)},w=(null==t?void 0:t.price)*(null==t?void 0:t.quantity)||0,k=l||o;return(0,a.jsx)(j.P.div,{layout:!0,initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},children:(0,a.jsx)(c.Zp,{className:"overflow-hidden border hover:shadow-md transition-shadow duration-200",children:(0,a.jsx)(c.Wu,{className:"p-3 sm:p-4",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"relative w-full sm:w-20 md:w-24 h-40 sm:h-20 md:h-24 bg-muted rounded-lg overflow-hidden flex-shrink-0",children:[t.images&&t.images.length>0?(0,a.jsx)(x.default,{src:t.images[0].url,alt:t.title,fill:!0,className:"object-cover"}):(0,a.jsx)("div",{className:"w-full h-full bg-gray-200 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"No Image"})}),t.stock<10&&(0,a.jsx)(y.E,{variant:"destructive",className:"absolute top-2 left-2 text-xs",children:"Low Stock"})]}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between gap-2 sm:gap-4",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h3",{className:"font-semibold text-base sm:text-lg text-gray-900 line-clamp-2",children:t.title}),(0,a.jsx)("p",{className:"text-xs sm:text-sm text-gray-600 mt-1 line-clamp-2 hidden sm:block",children:t.description}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 mt-2",children:[(0,a.jsxs)("span",{className:"text-base sm:text-lg font-bold text-primary",children:["$",Number(t.price).toFixed(2)]}),t.stock>0?(0,a.jsxs)(y.E,{variant:"secondary",className:"text-xs",children:[t.stock," in stock"]}):(0,a.jsx)(y.E,{variant:"destructive",className:"text-xs",children:"Out of stock"})]})]}),(0,a.jsxs)("div",{className:"text-right sm:text-right",children:[(0,a.jsx)("div",{className:"text-xs sm:text-sm text-gray-600",children:"Total"}),(0,a.jsxs)("div",{className:"text-lg sm:text-xl font-bold text-gray-900",children:["$",Number(w).toFixed(2)]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-2 mt-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600 hidden sm:block",children:"Quantity:"}),(0,a.jsxs)("div",{className:"flex items-center gap-1 border rounded-lg",children:[(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>b(i-1),disabled:k||i<=1,className:"h-8 w-8 p-0",children:(0,a.jsx)(h.A,{className:"w-3 h-3"})}),(0,a.jsx)(v.p,{type:"number",min:"1",max:t.stock,value:i,onChange:e=>{let t=parseInt(e.target.value);!isNaN(t)&&t>0&&d(t)},onBlur:()=>{i!==t.quantity&&b(i)},disabled:k,className:"w-16 h-8 text-center border-0 bg-transparent focus:ring-0"}),(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>b(i+1),disabled:k||i>=t.stock,className:"h-8 w-8 p-0",children:(0,a.jsx)(p.A,{className:"w-3 h-3"})})]}),o&&(0,a.jsx)(g.A,{className:"w-4 h-4 animate-spin text-primary"})]}),(0,a.jsxs)(n.$,{variant:"ghost",size:"sm",onClick:N,disabled:k,className:"text-red-600 hover:text-red-700 hover:bg-red-50 w-full sm:w-auto justify-center sm:justify-start",children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-1"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Remove Item"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Remove"})]})]})]})]})})})})};var N=s(1586),w=s(9799),k=s(2138),A=s(5525),C=s(5654);let E=e=>{let{cart:t,onProceedToCheckout:s,isUpdating:r}=e;if(!t)return null;let l=C.CV.calculateCartTotal(t),i=C.CV.calculateTax(l),d=C.CV.calculateShipping(l),o=l+i+d,u=50-l,m=l>=50;return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(c.Zp,{className:"lg:sticky lg:top-4",children:[(0,a.jsx)(c.aR,{className:"pb-4",children:(0,a.jsxs)(c.ZB,{className:"flex items-center gap-2 text-lg sm:text-xl",children:[(0,a.jsx)(N.A,{className:"w-5 h-5"}),"Order Summary"]})}),(0,a.jsxs)(c.Wu,{className:"space-y-4 p-4 sm:p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("span",{className:"text-gray-600",children:["Subtotal (",t.length," ",1===t.length?"item":"items",")"]}),(0,a.jsxs)("span",{className:"font-semibold",children:["$",l.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(w.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Shipping"})]}),(0,a.jsx)("div",{className:"text-right",children:m?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(y.E,{variant:"secondary",className:"text-green-600 bg-green-50",children:"FREE"}),(0,a.jsx)("span",{className:"line-through text-gray-400 text-sm",children:"$10.00"})]}):(0,a.jsxs)("span",{className:"font-semibold",children:["$",d.toFixed(2)]})})]}),!m&&u>0&&(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-blue-700 text-sm",children:[(0,a.jsx)(w.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:["Add ",(0,a.jsxs)("strong",{children:["$",u.toFixed(2)]})," more for FREE shipping!"]})]}),(0,a.jsx)("div",{className:"mt-2 bg-blue-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(Math.min(l/50*100,100),"%")}})})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Tax (8%)"}),(0,a.jsxs)("span",{className:"font-semibold",children:["$",i.toFixed(2)]})]}),(0,a.jsx)("hr",{className:"border-gray-200"}),(0,a.jsxs)("div",{className:"flex justify-between items-center text-lg font-bold",children:[(0,a.jsx)("span",{children:"Total"}),(0,a.jsxs)("span",{className:"text-primary",children:["$",o.toFixed(2)]})]}),(0,a.jsx)(n.$,{onClick:s,disabled:r,className:"w-full h-11 sm:h-12 text-sm sm:text-base font-semibold",size:"lg",children:r?"Updating...":(0,a.jsxs)(a.Fragment,{children:["Proceed to Checkout",(0,a.jsx)(k.A,{className:"w-4 h-4 ml-2"})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 text-sm text-gray-500 mt-4",children:[(0,a.jsx)(A.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Secure checkout with SSL encryption"})]})]})]}),(0,a.jsx)(c.Zp,{children:(0,a.jsxs)(c.Wu,{className:"p-4 space-y-3",children:[(0,a.jsx)("h4",{className:"font-semibold text-gray-900",children:"Why shop with us?"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm text-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,a.jsx)("span",{children:"Free returns within 30 days"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,a.jsx)("span",{children:"24/7 customer support"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,a.jsx)("span",{children:"Secure payment processing"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,a.jsx)("span",{children:"Fast shipping nationwide"})]})]})]})}),(0,a.jsx)(c.Zp,{children:(0,a.jsxs)(c.Wu,{className:"p-4",children:[(0,a.jsx)("h4",{className:"font-semibold text-gray-900 mb-3",children:"Accepted Payment Methods"}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,a.jsx)(y.E,{variant:"outline",className:"text-xs",children:"Visa"}),(0,a.jsx)(y.E,{variant:"outline",className:"text-xs",children:"Mastercard"}),(0,a.jsx)(y.E,{variant:"outline",className:"text-xs",children:"PayPal"}),(0,a.jsx)(y.E,{variant:"outline",className:"text-xs",children:"COD"})]})]})})]})};var S=s(1976),R=s(8564);let P=e=>{let{onContinueShopping:t}=e;return(0,a.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,a.jsx)(c.Zp,{className:"border-0 shadow-lg",children:(0,a.jsx)(c.Wu,{className:"p-6 sm:p-8 text-center",children:(0,a.jsxs)(j.P.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.5},className:"space-y-6",children:[(0,a.jsxs)("div",{className:"relative mx-auto w-24 sm:w-32 h-24 sm:h-32",children:[(0,a.jsx)(j.P.div,{animate:{rotate:[0,-10,10,-10,0],scale:[1,1.05,1]},transition:{duration:2,repeat:1/0,repeatDelay:3},className:"w-full h-full bg-gray-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(i.A,{className:"w-12 sm:w-16 h-12 sm:h-16 text-gray-400"})}),(0,a.jsx)(j.P.div,{animate:{y:[-5,5,-5],opacity:[.5,1,.5]},transition:{duration:2,repeat:1/0,delay:.5},className:"absolute -top-2 -right-2",children:(0,a.jsx)(S.A,{className:"w-6 h-6 text-red-300"})}),(0,a.jsx)(j.P.div,{animate:{y:[5,-5,5],opacity:[.5,1,.5]},transition:{duration:2,repeat:1/0,delay:1},className:"absolute -bottom-2 -left-2",children:(0,a.jsx)(R.A,{className:"w-5 h-5 text-yellow-300"})})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h2",{className:"text-xl sm:text-2xl font-bold text-gray-900",children:"Your cart is empty"}),(0,a.jsx)("p",{className:"text-sm sm:text-base text-gray-600 max-w-md mx-auto",children:"Looks like you haven't added any items to your cart yet. Start shopping to fill it up with amazing products!"})]}),(0,a.jsx)(j.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,a.jsxs)(n.$,{onClick:t,size:"lg",className:"h-11 sm:h-12 px-6 sm:px-8 text-sm sm:text-base font-semibold",children:["Start Shopping",(0,a.jsx)(k.A,{className:"w-4 h-4 ml-2"})]})}),(0,a.jsxs)("div",{className:"pt-6 border-t border-gray-100",children:[(0,a.jsx)("h3",{className:"text-base sm:text-lg font-semibold text-gray-900 mb-4",children:"Popular Categories"}),(0,a.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-3",children:[{name:"Electronics",emoji:"\uD83D\uDCF1"},{name:"Fashion",emoji:"\uD83D\uDC55"},{name:"Home & Garden",emoji:"\uD83C\uDFE0"},{name:"Sports",emoji:"⚽"}].map((e,s)=>(0,a.jsxs)(j.P.button,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*s+.5},whileHover:{scale:1.05},whileTap:{scale:.95},onClick:t,className:"p-2 sm:p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors duration-200 text-center",children:[(0,a.jsx)("div",{className:"text-xl sm:text-2xl mb-1",children:e.emoji}),(0,a.jsx)("div",{className:"text-xs sm:text-sm font-medium text-gray-700",children:e.name})]},e.name))})]}),(0,a.jsx)("div",{className:"pt-6 border-t border-gray-100",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-green-600 font-bold",children:"✓"})}),(0,a.jsx)("span",{className:"text-gray-600 text-center",children:"Free shipping on orders over $50"})]}),(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-blue-600 font-bold",children:"↩"})}),(0,a.jsx)("span",{className:"text-gray-600 text-center",children:"30-day return policy"})]}),(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-purple-600 font-bold",children:"\uD83D\uDD12"})}),(0,a.jsx)("span",{className:"text-gray-600 text-center",children:"Secure checkout"})]})]})})]})})})})};var M=s(1243),V=s(3904);class _ extends m.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("Cart Error Boundary caught an error:",e,t)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsx)("div",{className:"container mx-auto px-4 max-w-2xl",children:(0,a.jsxs)(c.Zp,{className:"border-red-200 bg-red-50",children:[(0,a.jsx)(c.aR,{children:(0,a.jsxs)(c.ZB,{className:"flex items-center gap-2 text-red-700",children:[(0,a.jsx)(M.A,{className:"w-5 h-5"}),"Something went wrong"]})}),(0,a.jsxs)(c.Wu,{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-red-600",children:"We encountered an error while loading your cart. This might be a temporary issue."}),this.state.error&&(0,a.jsxs)("details",{className:"text-sm text-red-500",children:[(0,a.jsx)("summary",{className:"cursor-pointer font-medium",children:"Error details"}),(0,a.jsx)("pre",{className:"mt-2 p-2 bg-red-100 rounded text-xs overflow-auto",children:this.state.error.message})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(n.$,{onClick:this.handleRetry,variant:"outline",className:"border-red-300 text-red-700 hover:bg-red-100",children:[(0,a.jsx)(V.A,{className:"w-4 h-4 mr-2"}),"Try Again"]}),(0,a.jsx)(n.$,{onClick:()=>window.location.href="/",variant:"default",children:"Go to Home"})]})]})]})})}):this.props.children}constructor(e){super(e),this.handleRetry=()=>{this.setState({hasError:!1,error:void 0})},this.state={hasError:!1}}}function F(){let{cart:e,isLoading:t,isUpdating:s,updateQuantity:m,removeFromCart:x}=(0,o._)(),h=(0,r.useRouter)(),p=async(e,t)=>(await m(e,t),!0),g=async e=>!!await x(e),f=()=>{h.push("/shop")};return t?(0,a.jsx)(d.AV,{message:"Loading your cart..."}):(0,a.jsx)(u.A,{children:(0,a.jsx)(_,{children:(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-4 sm:py-8",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl",children:[(0,a.jsxs)("div",{className:"mb-6 sm:mb-8",children:[(0,a.jsxs)(n.$,{variant:"ghost",onClick:f,className:"mb-4 text-gray-600 hover:text-gray-800 text-sm sm:text-base",children:[(0,a.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"Continue Shopping"]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(i.A,{className:"w-6 h-6 sm:w-8 sm:h-8 text-primary"}),(0,a.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900",children:"Shopping Cart"})]}),e&&e.map(e=>(0,a.jsxs)("span",{className:"text-sm sm:text-lg text-gray-600",children:["(",e.quantity," ",1===e.quantity?"item":"items",")"]},e.productId))]})]}),e&&0!==e.length?(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-2 space-y-4",children:(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{className:"pb-4",children:(0,a.jsx)(c.ZB,{className:"text-lg sm:text-xl",children:"Cart Items"})}),(0,a.jsx)(c.Wu,{className:"space-y-4 p-4 sm:p-6",children:e&&e.map(e=>(0,a.jsx)(b,{item:e,onQuantityUpdate:p,onRemove:g,isUpdating:s},e.productId))})]})}),(0,a.jsx)("div",{className:"lg:col-span-1 order-first lg:order-last",children:(0,a.jsx)(E,{cart:e&&e,onProceedToCheckout:()=>{h.push("/checkout")},isUpdating:s})})]}):(0,a.jsx)(P,{onContinueShopping:f})]})})})})}},1154:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1243:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},1586:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},1976:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},2138:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2523:(e,t,s)=>{"use strict";s.d(t,{p:()=>l});var a=s(5155);s(2115);var r=s(9434);function l(e){let{className:t,type:s,...l}=e;return(0,a.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...l})}},2525:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},3904:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},4616:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5323:(e,t,s)=>{"use strict";s.d(t,{CartProvider:()=>o,_:()=>d});var a=s(5155),r=s(2115),l=s(5654),i=s(283),n=s(8543);let c=(0,r.createContext)(void 0),d=()=>{let e=(0,r.useContext)(c);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},o=e=>{let{children:t}=e,[s,d]=(0,r.useState)(null),[o,u]=(0,r.useState)(!1),[m,x]=(0,r.useState)(!1),{isAuthenticated:h,user:p}=(0,i.A)(),g=(null==s?void 0:s.length)||0;(0,r.useEffect)(()=>{h&&p?f():d(null)},[h,p,f]);let f=async()=>{if(h)try{u(!0);let e=await l.CV.getCartItems();e.success&&e.data&&d(e.data)}catch(e){d(null)}finally{u(!1)}},v=async(e,t)=>{if(!h)return n.oR.error("Please sign in to add items to cart"),!1;try{x(!0);let s=await l.CV.addToCart({productId:e,quantity:t});if(s.success&&s.data)return d(s.data),n.oR.success("Item added to cart successfully"),!0;return!1}catch(e){var s;return n.oR.error((null==(s=e.response)?void 0:s.data.message)||"Failed to add item to cart"),!1}finally{x(!1)}},y=async(e,t)=>{if(!h||t<1)return!1;try{x(!0);let s=await l.CV.updateQuantity({productId:e,quantity:t});if(s.success&&s.data)return n.oR.success("Quantity updated successfully"),d(s.data),!0;return!1}catch(e){var s;return n.oR.error((null==(s=e.response)?void 0:s.data.message)||"Failed to update quantity"),!1}finally{x(!1)}},j=async e=>{if(!h)return!1;try{x(!0);let t=await l.CV.removeProduct(e);if(t.success&&t.data)return d(t.data),n.oR.success("Item removed from cart"),!0;return!1}catch(e){var t;return n.oR.error((null==(t=e.response)?void 0:t.data.message)||"Failed to remove item"),!1}finally{x(!1)}};return(0,a.jsx)(c.Provider,{value:{cart:s,isLoading:o,isUpdating:m,itemCount:g,addToCart:v,updateQuantity:y,removeFromCart:j,clearCart:()=>{d(null)},refreshCart:f,getCartCalculations:()=>{if(!s)return{subtotal:0,tax:0,shipping:0,total:0};let e=l.CV.calculateCartTotal(s),t=l.CV.calculateTax(e),a=l.CV.calculateShipping(e),r=e+t+a;return{subtotal:e,tax:t,shipping:a,total:r}}},children:t})}},5525:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5695:(e,t,s)=>{"use strict";var a=s(8999);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}})},6101:(e,t,s)=>{"use strict";s.d(t,{s:()=>i,t:()=>l});var a=s(2115);function r(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let s=!1,a=e.map(e=>{let a=r(e,t);return s||"function"!=typeof a||(s=!0),a});if(s)return()=>{for(let t=0;t<a.length;t++){let s=a[t];"function"==typeof s?s():r(e[t],null)}}}}function i(...e){return a.useCallback(l(...e),e)}},6126:(e,t,s)=>{"use strict";s.d(t,{E:()=>c});var a=s(5155);s(2115);var r=s(9708),l=s(2085),i=s(9434);let n=(0,l.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:s,asChild:l=!1,...c}=e,d=l?r.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(n({variant:s}),t),...c})}},6151:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("shopping-bag",[["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}],["path",{d:"M3.103 6.034h17.794",key:"awc11p"}],["path",{d:"M3.4 5.467a2 2 0 0 0-.4 1.2V20a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6.667a2 2 0 0 0-.4-1.2l-2-2.667A2 2 0 0 0 17 2H7a2 2 0 0 0-1.6.8z",key:"o988cm"}]])},6632:(e,t,s)=>{Promise.resolve().then(s.bind(s,434))},6654:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return r}});let a=s(2115);function r(e,t){let s=(0,a.useRef)(null),r=(0,a.useRef)(null);return(0,a.useCallback)(a=>{if(null===a){let e=s.current;e&&(s.current=null,e());let t=r.current;t&&(r.current=null,t())}else e&&(s.current=l(e,a)),t&&(r.current=l(t,a))},[e,t])}function l(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let s=e(t);return"function"==typeof s?s:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>n,Zp:()=>l,aR:()=>i,wL:()=>o});var a=s(5155);s(2115);var r=s(9434);function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...s})}function o(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...s})}},7023:(e,t,s)=>{"use strict";s.d(t,{AV:()=>l});var a=s(5155);s(2115);let r=e=>{let{size:t="md",className:s=""}=e;return(0,a.jsx)("div",{className:"animate-spin rounded-full border-2 border-gray-300 border-t-primary ".concat({sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"}[t]," ").concat(s)})},l=e=>{let{message:t="Loading..."}=e;return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(r,{size:"lg",className:"mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:t})]})})}},7550:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7712:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]])},8564:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},9053:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var a=s(5155),r=s(2115),l=s(5695),i=s(283),n=s(7023);function c(e){let{children:t,redirectTo:s="/signin",requireAuth:c=!0}=e,{isAuthenticated:d,isLoading:o}=(0,i.A)(),u=(0,l.useRouter)();return((0,r.useEffect)(()=>{o||(c&&!d?u.push(s):!c&&d&&u.push("/"))},[d,o,c,s,u]),o)?(0,a.jsx)(n.AV,{message:"Checking authentication..."}):c&&!d?(0,a.jsx)(n.AV,{message:"Redirecting to sign in..."}):!c&&d?(0,a.jsx)(n.AV,{message:"Redirecting..."}):(0,a.jsx)(a.Fragment,{children:t})}},9708:(e,t,s)=>{"use strict";s.d(t,{DX:()=>n,TL:()=>i});var a=s(2115),r=s(6101),l=s(5155);function i(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:s,...l}=e;if(a.isValidElement(s)){var i;let e,n,c=(i=s,(n=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(n=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,t){let s={...t};for(let a in t){let r=e[a],l=t[a];/^on[A-Z]/.test(a)?r&&l?s[a]=(...e)=>{let t=l(...e);return r(...e),t}:r&&(s[a]=r):"style"===a?s[a]={...r,...l}:"className"===a&&(s[a]=[r,l].filter(Boolean).join(" "))}return{...e,...s}}(l,s.props);return s.type!==a.Fragment&&(d.ref=t?(0,r.t)(t,c):c),a.cloneElement(s,d)}return a.Children.count(s)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),s=a.forwardRef((e,s)=>{let{children:r,...i}=e,n=a.Children.toArray(r),c=n.find(d);if(c){let e=c.props.children,r=n.map(t=>t!==c?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...i,ref:s,children:a.isValidElement(e)?a.cloneElement(e,void 0,r):null})}return(0,l.jsx)(t,{...i,ref:s,children:r})});return s.displayName=`${e}.Slot`,s}var n=i("Slot"),c=Symbol("radix.slottable");function d(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===c}},9799:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,9078,8543,6766,6408,7389,8441,1684,7358],()=>t(6632)),_N_E=e.O()}]);