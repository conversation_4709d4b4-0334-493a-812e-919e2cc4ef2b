(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8279],{283:(e,s,t)=>{"use strict";t.d(s,{A:()=>n,AuthProvider:()=>d});var a=t(5155),r=t(2115),l=t(5654);let i=(0,r.createContext)(void 0),n=()=>{let e=(0,r.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},d=e=>{let{children:s}=e,[t,n]=(0,r.useState)(null),[d,c]=(0,r.useState)(!0),o=!!t;(0,r.useEffect)(()=>{(async()=>{if(localStorage.getItem("token"))try{let e=await l.Dv.getUser();e.success&&e.data&&n(e.data)}catch(e){e.response&&(console.error("Error checking auth status:",e.response.data.message),localStorage.removeItem("token"))}c(!1)})()},[]);let u=async()=>{try{await l.Dv.logout()}catch(e){console.error("Error during logout:",e)}finally{localStorage.removeItem("token"),n(null)}},m=async()=>{try{let e=await l.Dv.getUser();e.success&&e.data&&n(e.data)}catch(e){console.error("Error refreshing user data:",e)}};return(0,a.jsx)(i.Provider,{value:{user:t,isLoading:d,isAuthenticated:o,login:(e,s)=>{localStorage.setItem("token",s),n(e)},logout:u,updateUser:e=>{t&&n({...t,...e})},refreshUser:m},children:s})}},2235:(e,s,t)=>{"use strict";t.d(s,{default:()=>$});var a=t(5155),r=t(2115),l=t(5695),i=t(6695),n=t(285),d=t(9428),c=t(9434);let o=r.createContext({}),u=r.forwardRef((e,s)=>{let{className:t,value:r,onValueChange:l,children:i,...n}=e;return(0,a.jsx)(o.Provider,{value:{value:r,onValueChange:l},children:(0,a.jsx)("div",{ref:s,className:(0,c.cn)("grid gap-2",t),role:"radiogroup",...n,children:i})})});u.displayName="RadioGroup";let m=r.forwardRef((e,s)=>{let{className:t,value:l,id:i,disabled:n,children:u,...m}=e,x=r.useContext(o),h=x.value===l;return(0,a.jsxs)("button",{ref:s,type:"button",role:"radio","aria-checked":h,"data-state":h?"checked":"unchecked",disabled:n,className:(0,c.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),onClick:()=>{!n&&x.onValueChange&&x.onValueChange(l)},id:i,...m,children:[h&&(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)(d.A,{className:"h-2.5 w-2.5 fill-current text-current"})}),u]})});m.displayName="RadioGroupItem";var x=t(5057),h=t(6126),p=t(2346),f=t(5365),j=t(2523),v=t(283),g=t(1007),y=t(5339);let N=e=>{let{onSubmit:s,onValidationChange:t,isSubmitting:l=!1}=e,{user:d,isAuthenticated:c}=(0,v.A)(),[o,u]=(0,r.useState)({fullName:"",email:"",phone:"",address:"",city:"",state:"",zipCode:"",country:"United States"}),[m,h]=(0,r.useState)({}),[p,f]=(0,r.useState)({});(0,r.useEffect)(()=>{c&&d&&u(e=>({...e,fullName:d.fullName||"",email:d.email||""}))},[c,d]);let N=(e,s)=>{switch(e){case"fullName":if(!s.trim())return"Full name is required";if(s.trim().length<2)return"Full name must be at least 2 characters";return"";case"email":if(!s.trim())return"Email is required";if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(s))return"Please enter a valid email address";return"";case"phone":if(!s.trim())return"Phone number is required";if(!/^03\d{9}$/.test(s))return"Please enter a valid phone number";return"";case"address":if(!s.trim())return"Address is required";if(s.trim().length<5)return"Please enter a complete address";return"";case"city":if(!s.trim())return"City is required";if(s.trim().length<2)return"Please enter a valid city name";return"";case"state":if(!s.trim())return"State is required";return"";case"zipCode":if(!s.trim())return"ZIP code is required";if(!/^\d{5}(-\d{4})?$/.test(s))return"Please enter a valid ZIP code (e.g., 12345 or 12345-6789)";return"";case"country":if(!s.trim())return"Country is required";return"";default:return""}},b=()=>{let e={},s=!0;return Object.keys(o).forEach(t=>{let a=N(t,o[t]);a&&(e[t]=a,s=!1)}),h(e),s},w=(e,s)=>{u(t=>({...t,[e]:s})),m[e]&&h(s=>({...s,[e]:""}))},C=e=>{f(s=>({...s,[e]:!0}));let s=N(e,o[e]);h(t=>({...t,[e]:s}))};return(0,r.useEffect)(()=>{t(b())},[o,t,b]),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(g.A,{className:"w-5 h-5"}),"Shipping Information"]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),f(Object.keys(o).reduce((e,s)=>(e[s]=!0,e),{})),b()&&s(o)},className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"font-medium text-sm text-muted-foreground uppercase tracking-wide",children:"Contact Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(x.J,{htmlFor:"fullName",children:"Full Name *"}),(0,a.jsx)(j.p,{id:"fullName",value:o.fullName,onChange:e=>w("fullName",e.target.value),onBlur:()=>C("fullName"),placeholder:"Enter your full name",className:m.fullName&&p.fullName?"border-destructive":""}),m.fullName&&p.fullName&&(0,a.jsxs)("div",{className:"flex items-center gap-1 text-destructive text-xs",children:[(0,a.jsx)(y.A,{className:"w-3 h-3"}),m.fullName]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(x.J,{htmlFor:"email",children:"Email Address *"}),(0,a.jsx)(j.p,{id:"email",type:"email",value:o.email,onChange:e=>w("email",e.target.value),onBlur:()=>C("email"),placeholder:"Enter your email",className:m.email&&p.email?"border-destructive":""}),m.email&&p.email&&(0,a.jsxs)("div",{className:"flex items-center gap-1 text-destructive text-xs",children:[(0,a.jsx)(y.A,{className:"w-3 h-3"}),m.email]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(x.J,{htmlFor:"phone",children:"Phone Number *"}),(0,a.jsx)(j.p,{id:"phone",type:"tel",value:o.phone,onChange:e=>w("phone",e.target.value),onBlur:()=>C("phone"),placeholder:"Enter your phone number",className:m.phone&&p.phone?"border-destructive":""}),m.phone&&p.phone&&(0,a.jsxs)("div",{className:"flex items-center gap-1 text-destructive text-xs",children:[(0,a.jsx)(y.A,{className:"w-3 h-3"}),m.phone]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"font-medium text-sm text-muted-foreground uppercase tracking-wide",children:"Shipping Address"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(x.J,{htmlFor:"address",children:"Street Address *"}),(0,a.jsx)(j.p,{id:"address",value:o.address,onChange:e=>w("address",e.target.value),onBlur:()=>C("address"),placeholder:"Enter your street address",className:m.address&&p.address?"border-destructive":""}),m.address&&p.address&&(0,a.jsxs)("div",{className:"flex items-center gap-1 text-destructive text-xs",children:[(0,a.jsx)(y.A,{className:"w-3 h-3"}),m.address]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(x.J,{htmlFor:"city",children:"City *"}),(0,a.jsx)(j.p,{id:"city",value:o.city,onChange:e=>w("city",e.target.value),onBlur:()=>C("city"),placeholder:"City",className:m.city&&p.city?"border-destructive":""}),m.city&&p.city&&(0,a.jsxs)("div",{className:"flex items-center gap-1 text-destructive text-xs",children:[(0,a.jsx)(y.A,{className:"w-3 h-3"}),m.city]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(x.J,{htmlFor:"state",children:"State *"}),(0,a.jsx)(j.p,{id:"state",value:o.state,onChange:e=>w("state",e.target.value),onBlur:()=>C("state"),placeholder:"State",className:m.state&&p.state?"border-destructive":""}),m.state&&p.state&&(0,a.jsxs)("div",{className:"flex items-center gap-1 text-destructive text-xs",children:[(0,a.jsx)(y.A,{className:"w-3 h-3"}),m.state]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(x.J,{htmlFor:"zipCode",children:"ZIP Code *"}),(0,a.jsx)(j.p,{id:"zipCode",value:o.zipCode,onChange:e=>w("zipCode",e.target.value),onBlur:()=>C("zipCode"),placeholder:"12345",className:m.zipCode&&p.zipCode?"border-destructive":""}),m.zipCode&&p.zipCode&&(0,a.jsxs)("div",{className:"flex items-center gap-1 text-destructive text-xs",children:[(0,a.jsx)(y.A,{className:"w-3 h-3"}),m.zipCode]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(x.J,{htmlFor:"country",children:"Country *"}),(0,a.jsx)("select",{id:"country",value:o.country,onChange:e=>w("country",e.target.value),onBlur:()=>C("country"),className:"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",children:["United States","Canada","United Kingdom","Australia","Germany","France","Japan","Other"].map(e=>(0,a.jsx)("option",{value:e,children:e},e))})]})]}),(0,a.jsx)("div",{className:"flex justify-end pt-4",children:(0,a.jsx)(n.$,{type:"submit",disabled:l||Object.keys(m).some(e=>m[e]),className:"min-w-[120px]",children:l?"Processing...":"Continue to Payment"})})]})})]})};var b=t(6766),w=t(5323);let C=[{type:"cod",name:"Cash on Delivery",description:"Pay when your order is delivered to your doorstep",fee:50,available:!0},{type:"online",name:"Online Payment",description:"Credit Card, Debit Card, PayPal (Coming Soon)",fee:0,available:!1}];var S=t(6151),P=t(7108),A=t(1586);let k=e=>{let{showTitle:s=!0,compact:t=!1}=e,{cart:r,getCartCalculations:l}=(0,w._)(),n=l(),d=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e);if(!r||0===r.length)return(0,a.jsx)(i.Zp,{children:(0,a.jsxs)(i.Wu,{className:"flex flex-col items-center justify-center py-12 text-center",children:[(0,a.jsx)(S.A,{className:"w-16 h-16 text-muted-foreground mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Your cart is empty"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Add some items to proceed with checkout"})]})});let c=n.total+50;return(0,a.jsxs)(i.Zp,{children:[s&&(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(P.A,{className:"w-5 h-5"}),"Order Summary"]})}),(0,a.jsxs)(i.Wu,{className:"space-y-6",children:[(0,a.jsx)("div",{className:"space-y-4",children:r.map(e=>(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsxs)("div",{className:"relative w-16 h-16 bg-muted rounded-md overflow-hidden flex-shrink-0",children:[e.images&&e.images.length>0?(0,a.jsx)(b.default,{src:e.images[0].url,alt:e.title,fill:!0,className:"object-cover",sizes:"64px"}):(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,a.jsx)(P.A,{className:"w-6 h-6 text-muted-foreground"})}),(0,a.jsx)(h.E,{className:"absolute -top-2 -right-2 w-6 h-6 rounded-full p-0 flex items-center justify-center text-xs",children:e.quantity})]}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h4",{className:"font-medium text-sm leading-tight line-clamp-2",children:e.title}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:e.category}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[d(e.price)," \xd7 ",e.quantity]}),(0,a.jsx)("span",{className:"font-semibold text-sm",children:d(e.price*e.quantity)})]})]})]},e.productId))}),(0,a.jsx)(p.w,{}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsxs)("span",{className:"text-muted-foreground",children:["Subtotal (",r.reduce((e,s)=>e+s.quantity,0)," items)"]}),(0,a.jsx)("span",{children:d(n.subtotal)})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(A.A,{className:"w-4 h-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"Cash on Delivery Fee"})]}),(0,a.jsx)("span",{children:d(50)})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Shipping"}),(0,a.jsx)("span",{className:"text-green-600 font-medium",children:"Free"})]}),(0,a.jsx)(p.w,{}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Tax (8%)"}),(0,a.jsx)("span",{children:d(n.tax)})]}),(0,a.jsxs)("div",{className:"flex justify-between text-lg font-bold",children:[(0,a.jsx)("span",{children:"Total"}),(0,a.jsx)("span",{className:"text-primary",children:d(c)})]})]}),(0,a.jsxs)("div",{className:"bg-muted/50 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(A.A,{className:"w-4 h-4 text-primary"}),(0,a.jsx)("span",{className:"font-medium text-sm",children:"Payment Method"})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Cash on Delivery - Pay when your order arrives at your doorstep"})]}),!t&&(0,a.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-950/20 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(P.A,{className:"w-4 h-4 text-blue-600"}),(0,a.jsx)("span",{className:"font-medium text-sm text-blue-900 dark:text-blue-100",children:"Estimated Delivery"})]}),(0,a.jsx)("p",{className:"text-sm text-blue-700 dark:text-blue-200",children:"3-5 business days from order confirmation"})]}),!t&&(0,a.jsxs)("div",{className:"text-xs text-muted-foreground space-y-1",children:[(0,a.jsx)("p",{children:"• Free shipping on all orders"}),(0,a.jsx)("p",{children:"• 30-day easy returns"}),(0,a.jsx)("p",{children:"• Secure packaging guaranteed"})]})]})]})};var F=t(5654),E=t(8543),R=t(646),z=t(5525),q=t(1154),I=t(7809),O=t(9799);let $=()=>{let e=(0,l.useRouter)(),{cart:s,getCartCalculations:t,clearCart:d}=(0,w._)(),{isAuthenticated:c}=(0,v.A)(),[o,j]=(0,r.useState)(null),[g,b]=(0,r.useState)(C[0]),[S,P]=(0,r.useState)(!1),[$,B]=(0,r.useState)(!1),[D,J]=(0,r.useState)(null),V=t();(0,r.useEffect)(()=>{if(!c){E.oR.error("Please sign in to proceed with checkout"),e.push("/signin");return}},[c,e]),(0,r.useEffect)(()=>{if(c&&(!s||0===s.length))return void e.push("/cart")},[s,c,e]);let Z=async()=>{if(!o||!S||!(null==s?void 0:s.length))return void J("Please complete all required fields");if(!g.available)return void J("Selected payment method is not available");B(!0),J(null);try{let s={fullName:o.fullName,address:o.address,phone:o.phone,city:o.city,state:o.state,postalCode:o.zipCode,country:o.country,paymentMethod:g.type},t=await F.Qo.placeOrder(s);if(t.success&&t.data)d(),E.oR.success("Order placed successfully!"),e.push("/order-confirmation/".concat(t.data._id));else throw Error(t.message||"Failed to place order")}catch(s){var t,a;console.error("Order placement error:",s);let e=(null==(a=s.response)||null==(t=a.data)?void 0:t.message)||s.message||"Failed to place order. Please try again.";J(e),E.oR.error(e)}finally{B(!1)}};if(!c||!(null==s?void 0:s.length))return null;let _=(()=>{let e=V.subtotal,s=V.tax;return e+("cod"===g.type?g.fee:0)+s})();return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Checkout"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Complete your order with secure checkout"})]}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(R.A,{className:"w-5 h-5 text-green-500 mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-green-700",children:"Cart Review"})]}),(0,a.jsx)("div",{className:"flex-1 h-px bg-gray-300"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-5 h-5 bg-blue-500 rounded-full mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-blue-700",children:"Checkout"})]}),(0,a.jsx)("div",{className:"flex-1 h-px bg-gray-300"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-5 h-5 bg-gray-300 rounded-full mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Confirmation"})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsx)(N,{onSubmit:e=>{j(e),J(null)},onValidationChange:e=>{P(e)},isSubmitting:$}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center",children:[(0,a.jsx)(A.A,{className:"w-5 h-5 mr-2"}),"Payment Method"]})}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)(u,{value:g.type,onValueChange:e=>{let s=C.find(s=>s.type===e);s&&s.available&&(b(s),J(null))},className:"space-y-3",children:C.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-4 border rounded-lg transition-colors ".concat(e.available?"hover:bg-gray-50 cursor-pointer":"bg-gray-50 cursor-not-allowed opacity-60"," ").concat(g.type===e.type&&e.available?"border-blue-500 bg-blue-50":"border-gray-200"),children:[(0,a.jsx)(m,{value:e.type,id:e.type,disabled:!e.available}),(0,a.jsx)(x.J,{htmlFor:e.type,className:"flex-1 cursor-pointer ".concat(e.available?"":"cursor-not-allowed"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[e.fee>0&&(0,a.jsxs)(h.E,{variant:"secondary",children:["+$",e.fee]}),!e.available&&(0,a.jsx)(h.E,{variant:"outline",children:"Coming Soon"})]})]})})]},e.type))}),(0,a.jsx)("div",{className:"mt-4 p-3 bg-green-50 border border-green-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(z.A,{className:"w-4 h-4 text-green-600 mr-2"}),(0,a.jsx)("span",{className:"text-sm text-green-800",children:"Your payment information is secure and encrypted"})]})})]})]}),D&&(0,a.jsxs)(f.Fc,{variant:"destructive",children:[(0,a.jsx)(y.A,{className:"h-4 w-4"}),(0,a.jsx)(f.TN,{children:D})]})]}),(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsxs)("div",{className:"sticky top-8",children:[(0,a.jsx)(k,{showTitle:!0,compact:!1}),(0,a.jsx)(i.Zp,{className:"mt-4",children:(0,a.jsxs)(i.Wu,{className:"pt-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"Subtotal"}),(0,a.jsxs)("span",{children:["$",V.subtotal.toFixed(2)]})]}),g.fee>0&&(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"COD Fee"}),(0,a.jsxs)("span",{children:["$",g.fee.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"Tax (8%)"}),(0,a.jsxs)("span",{children:["$",V.tax.toFixed(2)]})]}),(0,a.jsx)(p.w,{}),(0,a.jsxs)("div",{className:"flex justify-between font-semibold",children:[(0,a.jsx)("span",{children:"Total"}),(0,a.jsxs)("span",{children:["$",_.toFixed(2)]})]})]}),(0,a.jsx)(n.$,{onClick:Z,disabled:!S||$||!g.available,className:"w-full mt-6",size:"lg",children:$?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(q.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Placing Order..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(I.A,{className:"w-4 h-4 mr-2"}),"Place Order ($",_.toFixed(2),")"]})}),(0,a.jsxs)("div",{className:"mt-4 flex items-center justify-center text-xs text-gray-500",children:[(0,a.jsx)(O.A,{className:"w-3 h-3 mr-1"}),"Free delivery on orders over $50"]})]})})]})})]})]})})}},2346:(e,s,t)=>{"use strict";t.d(s,{w:()=>n});var a=t(5155),r=t(2115),l=t(7489),i=t(9434);let n=r.forwardRef((e,s)=>{let{className:t,orientation:r="horizontal",decorative:n=!0,...d}=e;return(0,a.jsx)(l.b,{ref:s,decorative:n,orientation:r,className:(0,i.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",t),...d})});n.displayName=l.b.displayName},2523:(e,s,t)=>{"use strict";t.d(s,{p:()=>l});var a=t(5155);t(2115);var r=t(9434);function l(e){let{className:s,type:t,...l}=e;return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...l})}},5057:(e,s,t)=>{"use strict";t.d(s,{J:()=>i});var a=t(5155);t(2115);var r=t(968),l=t(9434);function i(e){let{className:s,...t}=e;return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...t})}},5323:(e,s,t)=>{"use strict";t.d(s,{CartProvider:()=>o,_:()=>c});var a=t(5155),r=t(2115),l=t(5654),i=t(283),n=t(8543);let d=(0,r.createContext)(void 0),c=()=>{let e=(0,r.useContext)(d);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},o=e=>{let{children:s}=e,[t,c]=(0,r.useState)(null),[o,u]=(0,r.useState)(!1),[m,x]=(0,r.useState)(!1),{isAuthenticated:h,user:p}=(0,i.A)(),f=(null==t?void 0:t.length)||0;(0,r.useEffect)(()=>{h&&p?j():c(null)},[h,p,j]);let j=async()=>{if(h)try{u(!0);let e=await l.CV.getCartItems();e.success&&e.data&&c(e.data)}catch(e){c(null)}finally{u(!1)}},v=async(e,s)=>{if(!h)return n.oR.error("Please sign in to add items to cart"),!1;try{x(!0);let t=await l.CV.addToCart({productId:e,quantity:s});if(t.success&&t.data)return c(t.data),n.oR.success("Item added to cart successfully"),!0;return!1}catch(e){var t;return n.oR.error((null==(t=e.response)?void 0:t.data.message)||"Failed to add item to cart"),!1}finally{x(!1)}},g=async(e,s)=>{if(!h||s<1)return!1;try{x(!0);let t=await l.CV.updateQuantity({productId:e,quantity:s});if(t.success&&t.data)return n.oR.success("Quantity updated successfully"),c(t.data),!0;return!1}catch(e){var t;return n.oR.error((null==(t=e.response)?void 0:t.data.message)||"Failed to update quantity"),!1}finally{x(!1)}},y=async e=>{if(!h)return!1;try{x(!0);let s=await l.CV.removeProduct(e);if(s.success&&s.data)return c(s.data),n.oR.success("Item removed from cart"),!0;return!1}catch(e){var s;return n.oR.error((null==(s=e.response)?void 0:s.data.message)||"Failed to remove item"),!1}finally{x(!1)}};return(0,a.jsx)(d.Provider,{value:{cart:t,isLoading:o,isUpdating:m,itemCount:f,addToCart:v,updateQuantity:g,removeFromCart:y,clearCart:()=>{c(null)},refreshCart:j,getCartCalculations:()=>{if(!t)return{subtotal:0,tax:0,shipping:0,total:0};let e=l.CV.calculateCartTotal(t),s=l.CV.calculateTax(e),a=l.CV.calculateShipping(e),r=e+s+a;return{subtotal:e,tax:s,shipping:a,total:r}}},children:s})}},6570:(e,s,t)=>{Promise.resolve().then(t.bind(t,2235)),Promise.resolve().then(t.bind(t,6842)),Promise.resolve().then(t.bind(t,2357))}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,9078,8543,6766,2102,7389,7455,8441,1684,7358],()=>s(6570)),_N_E=e.O()}]);