(()=>{var e={};e.id=879,e.ids=[879],e.modules={1510:(e,s,r)=>{"use strict";r.d(s,{pe:()=>a});let{Axios:t,AxiosError:a,CanceledError:i,isCancel:l,CancelToken:n,VERSION:o,all:d,Cancel:c,isAxiosError:m,spread:u,toFormData:p,AxiosHeaders:x,HttpStatusCode:h,formToJSON:f,getAdapter:g,mergeConfig:v}=r(51060).A},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12597:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},13964:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},16189:(e,s,r)=>{"use strict";var t=r(65773);r.o(t,"useParams")&&r.d(s,{useParams:function(){return t.useParams}}),r.o(t,"usePathname")&&r.d(s,{usePathname:function(){return t.usePathname}}),r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20769:(e,s,r)=>{"use strict";r.d(s,{A:()=>n});var t=r(60687);r(43210);var a=r(16189),i=r(63213),l=r(52027);function n({children:e,redirectTo:s="/signin",requireAuth:r=!0}){let{isAuthenticated:n,isLoading:o}=(0,i.A)();return((0,a.useRouter)(),o)?(0,t.jsx)(l.AV,{message:"Checking authentication..."}):r&&!n?(0,t.jsx)(l.AV,{message:"Redirecting to sign in..."}):!r&&n?(0,t.jsx)(l.AV,{message:"Redirecting..."}):(0,t.jsx)(t.Fragment,{children:e})}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33221:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>N});var t=r(60687),a=r(43210),i=r(85814),l=r.n(i),n=r(16189),o=r(28559),d=r(58869),c=r(41550),m=r(64021),u=r(12597),p=r(13861),x=r(13964),h=r(29523),f=r(89667),g=r(54300),v=r(44493),y=r(58376),b=r(93853),w=r(20769),j=r(1510);function N(){var e;let s,r=(0,n.useRouter)(),[i,N]=(0,a.useState)({fullName:"",email:"",password:"",confirmPassword:""}),[P,A]=(0,a.useState)(!1),[k,C]=(0,a.useState)(!1),[q,R]=(0,a.useState)(!1),[M,_]=(0,a.useState)({}),[z,S]=(0,a.useState)(!1),G=()=>{let e={};return i.fullName.trim()?i.fullName.trim().length<2&&(e.fullName="Full name must be at least 2 characters"):e.fullName="Full name is required",i.email?/\S+@\S+\.\S+/.test(i.email)||(e.email="Please enter a valid email"):e.email="Email is required",i.password?i.password.length<8?e.password="Password must be at least 8 characters":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(i.password)||(e.password="Password must contain at least one uppercase letter, one lowercase letter, and one number"):e.password="Password is required",i.confirmPassword?i.password!==i.confirmPassword&&(e.confirmPassword="Passwords do not match"):e.confirmPassword="Please confirm your password",z||(e.terms="You must accept the terms and conditions"),_(e),0===Object.keys(e).length},H=e=>{let{name:s,value:r}=e.target;N(e=>({...e,[s]:r})),M[s]&&_(e=>({...e,[s]:""}))},$=async e=>{if(e.preventDefault(),G()){R(!0);try{let e=await y.Dv.signUp(i);if(e.success){let s=e.data?.user._id,t=e.data?.user.email;b.oR.success(e.message||"Account created successfully! Please check your email for verification."),r.push(`/otpVerification/${s}/${t}`)}else b.oR.error(e.message||"Sign up failed. Please try again.")}catch(e){e instanceof j.pe&&b.oR.error(e.response?.data?.message||"An error occurred. Please try again.")}finally{R(!1)}}},F=(e=i.password,s=0,e.length>=8&&s++,/[a-z]/.test(e)&&s++,/[A-Z]/.test(e)&&s++,/\d/.test(e)&&s++,/[^A-Za-z0-9]/.test(e)&&s++,s),V=["bg-red-500","bg-red-400","bg-yellow-500","bg-blue-500","bg-green-500"];return(0,t.jsx)(w.A,{requireAuth:!1,children:(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-50 to-pink-100 flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"w-full max-w-md",children:[(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsxs)(l(),{href:"/",className:"inline-flex items-center text-sm text-gray-600 hover:text-gray-900 transition-colors",children:[(0,t.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Back to Home"]})}),(0,t.jsxs)(v.Zp,{className:"shadow-xl border-0",children:[(0,t.jsxs)(v.aR,{className:"space-y-1 text-center",children:[(0,t.jsx)(v.ZB,{className:"text-2xl font-bold text-gray-900",children:"Create Account"}),(0,t.jsx)(v.BT,{className:"text-gray-600",children:"Join Mega Mall and start shopping today"})]}),(0,t.jsxs)(v.Wu,{className:"space-y-6",children:[(0,t.jsxs)("form",{onSubmit:$,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(g.J,{htmlFor:"fullName",className:"text-sm font-medium text-gray-700",children:"Full Name"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(d.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,t.jsx)(f.p,{id:"fullName",name:"fullName",type:"text",placeholder:"Enter your full name",value:i.fullName,onChange:H,className:`pl-10 ${M.fullName?"border-red-500 focus-visible:ring-red-500":""}`,disabled:q})]}),M.fullName&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:M.fullName})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(g.J,{htmlFor:"email",className:"text-sm font-medium text-gray-700",children:"Email Address"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,t.jsx)(f.p,{id:"email",name:"email",type:"email",placeholder:"Enter your email",value:i.email,onChange:H,className:`pl-10 ${M.email?"border-red-500 focus-visible:ring-red-500":""}`,disabled:q})]}),M.email&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:M.email})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(g.J,{htmlFor:"password",className:"text-sm font-medium text-gray-700",children:"Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,t.jsx)(f.p,{id:"password",name:"password",type:P?"text":"password",placeholder:"Create a strong password",value:i.password,onChange:H,className:`pl-10 pr-10 ${M.password?"border-red-500 focus-visible:ring-red-500":""}`,disabled:q}),(0,t.jsx)("button",{type:"button",onClick:()=>A(!P),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",disabled:q,children:P?(0,t.jsx)(u.A,{className:"w-4 h-4"}):(0,t.jsx)(p.A,{className:"w-4 h-4"})})]}),i.password&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"flex space-x-1",children:[1,2,3,4,5].map(e=>(0,t.jsx)("div",{className:`h-1 flex-1 rounded-full ${e<=F?V[F-1]:"bg-gray-200"}`},e))}),(0,t.jsxs)("p",{className:"text-xs text-gray-600",children:["Password strength: ",["Very Weak","Weak","Fair","Good","Strong"][F-1]||"Very Weak"]})]}),M.password&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:M.password})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(g.J,{htmlFor:"confirmPassword",className:"text-sm font-medium text-gray-700",children:"Confirm Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,t.jsx)(f.p,{id:"confirmPassword",name:"confirmPassword",type:k?"text":"password",placeholder:"Confirm your password",value:i.confirmPassword,onChange:H,className:`pl-10 pr-10 ${M.confirmPassword?"border-red-500 focus-visible:ring-red-500":""}`,disabled:q}),(0,t.jsx)("button",{type:"button",onClick:()=>C(!k),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",disabled:q,children:k?(0,t.jsx)(u.A,{className:"w-4 h-4"}):(0,t.jsx)(p.A,{className:"w-4 h-4"})}),i.confirmPassword&&i.password===i.confirmPassword&&(0,t.jsx)(x.A,{className:"absolute right-10 top-1/2 transform -translate-y-1/2 text-green-500 w-4 h-4"})]}),M.confirmPassword&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:M.confirmPassword})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,t.jsx)("input",{type:"checkbox",id:"terms",checked:z,onChange:e=>{S(e.target.checked),M.terms&&_(e=>({...e,terms:""}))},className:"mt-1 h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded",disabled:q}),(0,t.jsxs)("label",{htmlFor:"terms",className:"text-sm text-gray-600",children:["I agree to the"," ",(0,t.jsx)(l(),{href:"/terms",className:"text-primary hover:text-primary/80 underline",children:"Terms of Service"})," ","and"," ",(0,t.jsx)(l(),{href:"/privacy",className:"text-primary hover:text-primary/80 underline",children:"Privacy Policy"})]})]}),M.terms&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:M.terms})]}),(0,t.jsx)(h.$,{type:"submit",className:"w-full h-11 text-base font-medium",disabled:q,children:q?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Creating Account..."]}):"Create Account"})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,t.jsx)("span",{className:"w-full border-t border-gray-300"})}),(0,t.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,t.jsx)("span",{className:"bg-white px-2 text-gray-500",children:"Or"})})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(h.$,{variant:"outline",className:"w-full h-11",disabled:q,children:[(0,t.jsxs)("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",children:[(0,t.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,t.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,t.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,t.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Continue with Google"]}),(0,t.jsxs)(h.$,{variant:"outline",className:"w-full h-11",disabled:q,children:[(0,t.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})}),"Continue with Facebook"]})]}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",(0,t.jsx)(l(),{href:"/signin",className:"font-medium text-primary hover:text-primary/80 transition-colors",children:"Sign in here"})]})})]})]})]})})})}},33873:e=>{"use strict";e.exports=require("path")},38255:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\app\\\\signup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\signup\\page.tsx","default")},42273:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=r(65239),a=r(48088),i=r(88170),l=r.n(i),n=r(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(s,o);let d={children:["",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,38255)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\signup\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\signup\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/signup/page",pathname:"/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},44493:(e,s,r)=>{"use strict";r.d(s,{BT:()=>o,Wu:()=>d,ZB:()=>n,Zp:()=>i,aR:()=>l,wL:()=>c});var t=r(60687);r(43210);var a=r(4780);function i({className:e,...s}){return(0,t.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...s})}function l({className:e,...s}){return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...s})}function n({className:e,...s}){return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...s})}function o({className:e,...s}){return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...s})}function d({className:e,...s}){return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...s})}function c({className:e,...s}){return(0,t.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...s})}},52027:(e,s,r)=>{"use strict";r.d(s,{AV:()=>i});var t=r(60687);r(43210);let a=({size:e="md",className:s=""})=>(0,t.jsx)("div",{className:`animate-spin rounded-full border-2 border-gray-300 border-t-primary ${{sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"}[e]} ${s}`}),i=({message:e="Loading..."})=>(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(a,{size:"lg",className:"mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600 text-lg",children:e})]})})},54300:(e,s,r)=>{"use strict";r.d(s,{J:()=>o});var t=r(60687),a=r(43210),i=r(14163),l=a.forwardRef((e,s)=>(0,t.jsx)(i.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));l.displayName="Label";var n=r(4780);function o({className:e,...s}){return(0,t.jsx)(l,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...s})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64021:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},70178:(e,s,r)=>{Promise.resolve().then(r.bind(r,38255))},70440:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88330:(e,s,r)=>{Promise.resolve().then(r.bind(r,33221))},89667:(e,s,r)=>{"use strict";r.d(s,{p:()=>i});var t=r(60687);r(43210);var a=r(4780);function i({className:e,type:s,...r}){return(0,t.jsx)("input",{type:s,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,162,658,367],()=>r(42273));module.exports=t})();