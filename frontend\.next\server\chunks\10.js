exports.id=10,exports.ids=[10],exports.modules={8975:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},12597:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},23026:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},39907:(e,t,r)=>{"use strict";r.d(t,{w:()=>c});var a=r(60687),s=r(43210),i=r(14163),n="horizontal",l=["horizontal","vertical"],d=s.forwardRef((e,t)=>{var r;let{decorative:s,orientation:d=n,...o}=e,c=(r=d,l.includes(r))?d:n;return(0,a.jsx)(i.sG.div,{"data-orientation":c,...s?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...o,ref:t})});d.displayName="Separator";var o=r(4780);let c=s.forwardRef(({className:e,orientation:t="horizontal",decorative:r=!0,...s},i)=>(0,a.jsx)(d,{ref:i,decorative:r,orientation:t,className:(0,o.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...s}));c.displayName=d.displayName},41312:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},43515:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>o,ZB:()=>l,Zp:()=>i,aR:()=>n,wL:()=>c});var a=r(60687);r(43210);var s=r(4780);function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},54300:(e,t,r)=>{"use strict";r.d(t,{J:()=>d});var a=r(60687),s=r(43210),i=r(14163),n=s.forwardRef((e,t)=>(0,a.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=r(4780);function d({className:e,...t}){return(0,a.jsx)(n,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},58345:(e,t,r)=>{Promise.resolve().then(r.bind(r,93853)),Promise.resolve().then(r.bind(r,76779)),Promise.resolve().then(r.bind(r,58873))},58873:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>d,b:()=>l});var a=r(60687),s=r(43210),i=r(58376);let n=(0,s.createContext)(void 0),l=()=>{let e=(0,s.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},d=({children:e})=>{let[t,r]=(0,s.useState)(null),[l,d]=(0,s.useState)(!0);(0,s.useEffect)(()=>{(async()=>{if(localStorage.getItem("access_token"))try{let e=await i.ZJ.getAdmin();e.success&&e.data&&r(e.data)}catch(e){e.response&&(console.error("Error checking auth status:",e.response.data.message),localStorage.removeItem("access_token"))}d(!1)})()},[]);let o=async()=>{try{await i.ZJ.logout()}catch(e){console.error("Error during logout:",e)}finally{localStorage.removeItem("access_token"),r(null)}};return(0,a.jsx)(n.Provider,{value:{admin:t,isLoading:l,isAuthenticated:!!t,login:(e,t)=>{localStorage.setItem("access_token",t),r(e)},logout:o},children:e})}},58887:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},68073:(e,t,r)=>{Promise.resolve().then(r.bind(r,81819)),Promise.resolve().then(r.bind(r,83726)),Promise.resolve().then(r.bind(r,92495))},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},76779:(e,t,r)=>{"use strict";r.d(t,{default:()=>$});var a=r(60687),s=r(43210),i=r(58873),n=r(85814),l=r.n(n),d=r(16189),o=r(4780),c=r(62688);let m=(0,c.A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]);var u=r(19080),h=r(28561),x=r(41312),p=r(23026);let f=(0,c.A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);var v=r(58887);let g=(0,c.A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),b=(0,c.A)("store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12a2 2 0 0 1-2-2V7",key:"6c3vgh"}]]);var y=r(40083),j=r(29523),N=r(39907);let A=[{title:"Dashboard",href:"/admin",icon:m,description:"Overview and analytics"},{title:"Products",href:"/admin/products",icon:u.A,description:"Manage inventory"},{title:"Orders",href:"/admin/orders",icon:h.A,description:"Order management"},{title:"Customers",href:"/admin/customers",icon:x.A,description:"Customer management"},{title:"Admin Users",href:"/admin/admin-users",icon:p.A,description:"Manage admin accounts",superAdminOnly:!0},{title:"Analytics",href:"/admin/analytics",icon:f,description:"Sales and performance"},{title:"Messages",href:"/admin/messages",icon:v.A,description:"Customer support"},{title:"Settings",href:"/admin/settings",icon:g,description:"System configuration"}],w=()=>{let e=(0,d.usePathname)(),{admin:t,logout:r}=(0,i.b)(),s=async()=>{try{await r()}catch(e){console.error("Logout error:",e)}},n=A.filter(e=>!e.superAdminOnly||t?.role==="superAdmin");return(0,a.jsxs)("div",{className:"w-64 bg-card border-r border-border flex flex-col h-screen",children:[(0,a.jsx)("div",{className:"p-6 border-b border-border",children:(0,a.jsxs)(l(),{href:"/admin",className:"flex items-center space-x-2",children:[(0,a.jsx)(b,{className:"h-8 w-8 text-primary"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-foreground",children:"Mega Mall"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Admin Panel"})]})]})}),(0,a.jsx)("nav",{className:"flex-1 p-4 space-y-2",children:n.map(t=>{let r=e===t.href||"/admin"!==t.href&&e.startsWith(t.href);return(0,a.jsxs)(l(),{href:t.href,className:(0,o.cn)("flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",r?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-accent"),children:[(0,a.jsx)(t.icon,{className:"h-5 w-5"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{children:t.title}),t.description&&(0,a.jsx)("div",{className:"text-xs opacity-70",children:t.description})]})]},t.href)})}),(0,a.jsxs)("div",{className:"p-4 border-t border-border",children:[(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-foreground",children:t?.fullName}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:t?.email}),(0,a.jsx)("p",{className:"text-xs text-primary capitalize",children:t?.role})]}),(0,a.jsx)(N.w,{className:"mb-3"}),(0,a.jsxs)(j.$,{variant:"ghost",size:"sm",onClick:s,className:"w-full justify-start text-muted-foreground hover:text-foreground",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Logout"]})]})]})};var k=r(12941),M=r(99270),C=r(97051),P=r(89667),S=r(96834);let z=e=>{let t={"/admin":"Dashboard","/admin/products":"Product Management","/admin/orders":"Order Management","/admin/customers":"Customer Management","/admin/admin-users":"Admin User Management","/admin/analytics":"Analytics & Reports","/admin/messages":"Customer Messages","/admin/settings":"Settings"};if(t[e])return t[e];for(let[r,a]of Object.entries(t))if(e.startsWith(r)&&"/admin"!==r)return a;return"Admin Panel"},E=()=>{let e=z((0,d.usePathname)());return(0,a.jsxs)("header",{className:"h-16 bg-card border-b border-border px-6 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(j.$,{variant:"ghost",size:"icon",className:"md:hidden",children:(0,a.jsx)(k.A,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-semibold text-foreground",children:e}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:new Date().toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"relative hidden md:block",children:[(0,a.jsx)(M.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(P.p,{placeholder:"Search...",className:"pl-10 w-64"})]}),(0,a.jsxs)(j.$,{variant:"ghost",size:"icon",className:"relative",children:[(0,a.jsx)(C.A,{className:"h-5 w-5"}),(0,a.jsx)(S.E,{variant:"destructive",className:"absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs",children:"3"})]}),(0,a.jsx)(j.$,{variant:"ghost",size:"icon",className:"md:hidden",children:(0,a.jsx)(M.A,{className:"h-5 w-5"})})]})]})};var _=r(58376),R=r(54300),L=r(44493),F=r(91821),D=r(12597),G=r(13861),O=r(41862),H=r(93853);let I=()=>{let{login:e}=(0,i.b)(),[t,r]=(0,s.useState)({email:"",password:""}),[n,l]=(0,s.useState)(!1),[d,o]=(0,s.useState)(!1),[c,m]=(0,s.useState)(""),u=e=>{let{name:t,value:a}=e.target;r(e=>({...e,[t]:a})),c&&m("")},h=async r=>{r.preventDefault(),o(!0),m("");try{let r=await _.ZJ.login(t);if(r.success&&r.data){let{admin:t,access_token:a}=r.data;e(t,a),H.oR.success("Login successful!")}else m(r.message||"Login failed")}catch(t){let e=t.response?.data?.message||"An error occurred during login";m(e),H.oR.error(e)}finally{o(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"flex justify-center mb-4",children:(0,a.jsx)(b,{className:"h-12 w-12 text-primary"})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Mega Mall"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Admin Panel"})]}),(0,a.jsxs)(L.Zp,{children:[(0,a.jsxs)(L.aR,{children:[(0,a.jsx)(L.ZB,{children:"Admin Login"}),(0,a.jsx)(L.BT,{children:"Enter your credentials to access the admin panel"})]}),(0,a.jsx)(L.Wu,{children:(0,a.jsxs)("form",{onSubmit:h,className:"space-y-4",children:[c&&(0,a.jsx)(F.Fc,{variant:"destructive",children:c}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(R.J,{htmlFor:"email",children:"Email"}),(0,a.jsx)(P.p,{id:"email",name:"email",type:"email",placeholder:"<EMAIL>",value:t.email,onChange:u,required:!0,disabled:d})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(R.J,{htmlFor:"password",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(P.p,{id:"password",name:"password",type:n?"text":"password",placeholder:"Enter your password",value:t.password,onChange:u,required:!0,disabled:d}),(0,a.jsx)(j.$,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>l(!n),disabled:d,children:n?(0,a.jsx)(D.A,{className:"h-4 w-4"}):(0,a.jsx)(G.A,{className:"h-4 w-4"})})]})]}),(0,a.jsx)(j.$,{type:"submit",className:"w-full",disabled:d,children:d?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(O.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Signing in..."]}):"Sign In"})]})})]}),(0,a.jsx)("div",{className:"text-center mt-8 text-sm text-muted-foreground",children:(0,a.jsx)("p",{children:"\xa9 2024 Mega Mall. All rights reserved."})})]})})};var V=r(85726);let $=({children:e})=>{let{isAuthenticated:t,isLoading:r}=(0,i.b)();return r?(0,a.jsx)("div",{className:"min-h-screen bg-background",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsxs)("div",{className:"w-64 bg-card border-r border-border p-4",children:[(0,a.jsx)(V.E,{className:"h-8 w-32 mb-6"}),(0,a.jsx)("div",{className:"space-y-3",children:Array.from({length:8}).map((e,t)=>(0,a.jsx)(V.E,{className:"h-10 w-full"},t))})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"h-16 bg-card border-b border-border p-4",children:(0,a.jsx)(V.E,{className:"h-8 w-48"})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)(V.E,{className:"h-8 w-64 mb-4"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:Array.from({length:4}).map((e,t)=>(0,a.jsx)(V.E,{className:"h-32 w-full"},t))}),(0,a.jsx)(V.E,{className:"h-64 w-full"})]})]})]})}):t?(0,a.jsx)("div",{className:"min-h-screen bg-background",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)(w,{}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,a.jsx)(E,{}),(0,a.jsx)("main",{className:"flex-1 p-6 overflow-auto",children:e})]})]})}):(0,a.jsx)(I,{})}},83726:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminLayoutWrapper.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminLayoutWrapper.tsx","default")},85726:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var a=r(60687),s=r(4780);function i({className:e,...t}){return(0,a.jsx)("div",{className:(0,s.cn)("animate-pulse rounded-md bg-muted",e),...t})}},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var a=r(60687);r(43210);var s=r(4780);function i({className:e,type:t,...r}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},91821:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>d,TN:()=>o});var a=r(60687),s=r(43210),i=r(24224),n=r(4780);let l=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=s.forwardRef(({className:e,variant:t,...r},s)=>(0,a.jsx)("div",{ref:s,role:"alert",className:(0,n.cn)(l({variant:t}),e),...r}));d.displayName="Alert",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("h5",{ref:r,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let o=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...t}));o.displayName="AlertDescription"},92495:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>s});var a=r(12907);(0,a.registerClientReference)(function(){throw Error("Attempted to call useAdminAuth() from the server but useAdminAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\contexts\\admin\\AuthContext.tsx","useAdminAuth");let s=(0,a.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\contexts\\admin\\AuthContext.tsx","AuthProvider")},93613:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>d});var a=r(60687);r(43210);var s=r(8730),i=r(24224),n=r(4780);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:r=!1,...i}){let d=r?s.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(l({variant:t}),e),...i})}},97051:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},99111:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>m});var a=r(37413);r(56070);var s=r(43515),i=r.n(s),n=r(8975),l=r.n(n);r(61135);var d=r(81819),o=r(92495),c=r(83726);let m={title:"Mega Mall Admin",description:"Admin panel for Mega Mall e-commerce platform."};function u({children:e}){return(0,a.jsx)("html",{lang:"en",children:(0,a.jsx)("body",{className:`${i().variable} ${l().variable} antialiased`,children:(0,a.jsxs)(o.AuthProvider,{children:[(0,a.jsx)(c.default,{children:e}),(0,a.jsx)(d.ToastContainer,{position:"top-right",autoClose:3e3,hideProgressBar:!1,newestOnTop:!0,closeOnClick:!0,pauseOnHover:!0,theme:"light"})]})})})}},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};