"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2949],{283:(e,t,r)=>{r.d(t,{A:()=>s,AuthProvider:()=>l});var n=r(5155),a=r(2115),i=r(5654);let o=(0,a.createContext)(void 0),s=()=>{let e=(0,a.useContext)(o);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},l=e=>{let{children:t}=e,[r,s]=(0,a.useState)(null),[l,d]=(0,a.useState)(!0),u=!!r;(0,a.useEffect)(()=>{(async()=>{if(localStorage.getItem("token"))try{let e=await i.Dv.getUser();e.success&&e.data&&s(e.data)}catch(e){e.response&&(console.error("Error checking auth status:",e.response.data.message),localStorage.removeItem("token"))}d(!1)})()},[]);let c=async()=>{try{await i.Dv.logout()}catch(e){console.error("Error during logout:",e)}finally{localStorage.removeItem("token"),s(null)}},f=async()=>{try{let e=await i.Dv.getUser();e.success&&e.data&&s(e.data)}catch(e){console.error("Error refreshing user data:",e)}};return(0,n.jsx)(o.Provider,{value:{user:r,isLoading:l,isAuthenticated:u,login:(e,t)=>{localStorage.setItem("token",t),s(e)},logout:c,updateUser:e=>{r&&s({...r,...e})},refreshUser:f},children:t})}},285:(e,t,r)=>{r.d(t,{$:()=>l});var n=r(5155);r(2115);var a=r(9708),i=r(2085),o=r(9434);let s=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:i,asChild:l=!1,...d}=e,u=l?a.DX:"button";return(0,n.jsx)(u,{"data-slot":"button",className:(0,o.cn)(s({variant:r,size:i,className:t})),...d})}},968:(e,t,r)=>{r.d(t,{b:()=>s});var n=r(2115),a=r(3655),i=r(5155),o=n.forwardRef((e,t)=>(0,i.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var s=o},1264:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},2523:(e,t,r)=>{r.d(t,{p:()=>i});var n=r(5155);r(2115);var a=r(9434);function i(e){let{className:t,type:r,...i}=e;return(0,n.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},2657:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2919:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},3655:(e,t,r)=>{r.d(t,{hO:()=>l,sG:()=>s});var n=r(2115),a=r(7650),i=r(9708),o=r(5155),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.TL)(`Primitive.${t}`),a=n.forwardRef((e,n)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(a?r:t,{...i,ref:n})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function l(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},5057:(e,t,r)=>{r.d(t,{J:()=>o});var n=r(5155);r(2115);var a=r(968),i=r(9434);function o(e){let{className:t,...r}=e;return(0,n.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},5695:(e,t,r)=>{var n=r(8999);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}})},6101:(e,t,r)=>{r.d(t,{s:()=>o,t:()=>i});var n=r(2115);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}function o(...e){return n.useCallback(i(...e),e)}},6695:(e,t,r)=>{r.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>s,Zp:()=>i,aR:()=>o,wL:()=>u});var n=r(5155);r(2115);var a=r(9434);function i(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function o(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function s(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...r})}function l(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...r})}function d(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...r})}function u(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r})}},7023:(e,t,r)=>{r.d(t,{AV:()=>i});var n=r(5155);r(2115);let a=e=>{let{size:t="md",className:r=""}=e;return(0,n.jsx)("div",{className:"animate-spin rounded-full border-2 border-gray-300 border-t-primary ".concat({sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"}[t]," ").concat(r)})},i=e=>{let{message:t="Loading..."}=e;return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)(a,{size:"lg",className:"mx-auto mb-4"}),(0,n.jsx)("p",{className:"text-gray-600 text-lg",children:t})]})})}},7550:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8749:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},9053:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(5155),a=r(2115),i=r(5695),o=r(283),s=r(7023);function l(e){let{children:t,redirectTo:r="/signin",requireAuth:l=!0}=e,{isAuthenticated:d,isLoading:u}=(0,o.A)(),c=(0,i.useRouter)();return((0,a.useEffect)(()=>{u||(l&&!d?c.push(r):!l&&d&&c.push("/"))},[d,u,l,r,c]),u)?(0,n.jsx)(s.AV,{message:"Checking authentication..."}):l&&!d?(0,n.jsx)(s.AV,{message:"Redirecting to sign in..."}):!l&&d?(0,n.jsx)(s.AV,{message:"Redirecting..."}):(0,n.jsx)(n.Fragment,{children:t})}},9708:(e,t,r)=>{r.d(t,{DX:()=>s,TL:()=>o});var n=r(2115),a=r(6101),i=r(5155);function o(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){var o;let e,s,l=(o=r,(s=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(s=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),d=function(e,t){let r={...t};for(let n in t){let a=e[n],i=t[n];/^on[A-Z]/.test(n)?a&&i?r[n]=(...e)=>{let t=i(...e);return a(...e),t}:a&&(r[n]=a):"style"===n?r[n]={...a,...i}:"className"===n&&(r[n]=[a,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(d.ref=t?(0,a.t)(t,l):l),n.cloneElement(r,d)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:a,...o}=e,s=n.Children.toArray(a),l=s.find(d);if(l){let e=l.props.children,a=s.map(t=>t!==l?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,a):null})}return(0,i.jsx)(t,{...o,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var s=o("Slot"),l=Symbol("radix.slottable");function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}}}]);