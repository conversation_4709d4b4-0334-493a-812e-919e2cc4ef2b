(()=>{var e={};e.id=279,e.ids=[279],e.modules={2779:(e,s,t)=>{Promise.resolve().then(t.bind(t,47362)),Promise.resolve().then(t.bind(t,55118)),Promise.resolve().then(t.bind(t,17215))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32192:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},33873:e=>{"use strict";e.exports=require("path")},39907:(e,s,t)=>{"use strict";t.d(s,{w:()=>o});var r=t(60687),a=t(43210),l=t(14163),i="horizontal",n=["horizontal","vertical"],d=a.forwardRef((e,s)=>{var t;let{decorative:a,orientation:d=i,...c}=e,o=(t=d,n.includes(t))?d:i;return(0,r.jsx)(l.sG.div,{"data-orientation":o,...a?{role:"none"}:{"aria-orientation":"vertical"===o?o:void 0,role:"separator"},...c,ref:s})});d.displayName="Separator";var c=t(4780);let o=a.forwardRef(({className:e,orientation:s="horizontal",decorative:t=!0,...a},l)=>(0,r.jsx)(d,{ref:l,decorative:t,orientation:s,className:(0,c.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...a}));o.displayName=d.displayName},41862:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},43649:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},47362:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\components\\\\checkout\\\\CheckoutPage.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\checkout\\CheckoutPage.tsx","default")},54300:(e,s,t)=>{"use strict";t.d(s,{J:()=>d});var r=t(60687),a=t(43210),l=t(14163),i=a.forwardRef((e,s)=>(0,r.jsx)(l.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));i.displayName="Label";var n=t(4780);function d({className:e,...s}){return(0,r.jsx)(i,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...s})}},54787:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c,metadata:()=>d});var r=t(37413),a=t(61120),l=t(47362),i=t(55118),n=t(17215);let d={title:"Checkout - Mega Mall | Secure Order Placement",description:"Complete your order securely with our easy checkout process. Cash on delivery available with fast shipping and excellent customer service.",keywords:"checkout, order, payment, cash on delivery, COD, secure checkout, mega mall",openGraph:{title:"Checkout - Mega Mall | Secure Order Placement",description:"Complete your order securely with our easy checkout process.",type:"website",url:"/checkout"},twitter:{card:"summary",title:"Checkout - Mega Mall | Secure Order Placement",description:"Complete your order securely with our easy checkout process."},robots:{index:!1,follow:!1}};function c(){return(0,r.jsx)(n.default,{children:(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)(i.default,{}),children:(0,r.jsx)(l.default,{})})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71057:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("shopping-bag",[["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}],["path",{d:"M3.103 6.034h17.794",key:"awc11p"}],["path",{d:"M3.4 5.467a2 2 0 0 0-.4 1.2V20a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6.667a2 2 0 0 0-.4-1.2l-2-2.667A2 2 0 0 0 17 2H7a2 2 0 0 0-1.6.8z",key:"o988cm"}]])},74075:e=>{"use strict";e.exports=require("zlib")},75838:(e,s,t)=>{"use strict";t.d(s,{default:()=>G});var r=t(60687),a=t(43210),l=t(16189),i=t(44493),n=t(29523);let d=(0,t(62688).A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);var c=t(4780);let o=a.createContext({}),u=a.forwardRef(({className:e,value:s,onValueChange:t,children:a,...l},i)=>(0,r.jsx)(o.Provider,{value:{value:s,onValueChange:t},children:(0,r.jsx)("div",{ref:i,className:(0,c.cn)("grid gap-2",e),role:"radiogroup",...l,children:a})}));u.displayName="RadioGroup";let m=a.forwardRef(({className:e,value:s,id:t,disabled:l,children:i,...n},u)=>{let m=a.useContext(o),x=m.value===s;return(0,r.jsxs)("button",{ref:u,type:"button",role:"radio","aria-checked":x,"data-state":x?"checked":"unchecked",disabled:l,className:(0,c.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),onClick:()=>{!l&&m.onValueChange&&m.onValueChange(s)},id:t,...n,children:[x&&(0,r.jsx)("div",{className:"flex items-center justify-center",children:(0,r.jsx)(d,{className:"h-2.5 w-2.5 fill-current text-current"})}),i]})});m.displayName="RadioGroupItem";var x=t(54300),p=t(96834),h=t(39907),f=t(91821),v=t(89667),j=t(63213),g=t(58869),y=t(93613);let b=({onSubmit:e,onValidationChange:s,isSubmitting:t=!1})=>{let{user:l,isAuthenticated:d}=(0,j.A)(),[c,o]=(0,a.useState)({fullName:"",email:"",phone:"",address:"",city:"",state:"",zipCode:"",country:"United States"}),[u,m]=(0,a.useState)({}),[p,h]=(0,a.useState)({});(0,a.useEffect)(()=>{d&&l&&o(e=>({...e,fullName:l.fullName||"",email:l.email||""}))},[d,l]);let f=(e,s)=>{switch(e){case"fullName":if(!s.trim())return"Full name is required";if(s.trim().length<2)return"Full name must be at least 2 characters";return"";case"email":if(!s.trim())return"Email is required";if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(s))return"Please enter a valid email address";return"";case"phone":if(!s.trim())return"Phone number is required";if(!/^03\d{9}$/.test(s))return"Please enter a valid phone number";return"";case"address":if(!s.trim())return"Address is required";if(s.trim().length<5)return"Please enter a complete address";return"";case"city":if(!s.trim())return"City is required";if(s.trim().length<2)return"Please enter a valid city name";return"";case"state":if(!s.trim())return"State is required";return"";case"zipCode":if(!s.trim())return"ZIP code is required";if(!/^\d{5}(-\d{4})?$/.test(s))return"Please enter a valid ZIP code (e.g., 12345 or 12345-6789)";return"";case"country":if(!s.trim())return"Country is required";return"";default:return""}},b=()=>{let e={},s=!0;return Object.keys(c).forEach(t=>{let r=f(t,c[t]);r&&(e[t]=r,s=!1)}),m(e),s},N=(e,s)=>{o(t=>({...t,[e]:s})),u[e]&&m(s=>({...s,[e]:""}))},w=e=>{h(s=>({...s,[e]:!0}));let s=f(e,c[e]);m(t=>({...t,[e]:s}))};return(0,a.useEffect)(()=>{s(b())},[c,s,b]),(0,r.jsxs)(i.Zp,{children:[(0,r.jsx)(i.aR,{children:(0,r.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(g.A,{className:"w-5 h-5"}),"Shipping Information"]})}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)("form",{onSubmit:s=>{s.preventDefault(),h(Object.keys(c).reduce((e,s)=>(e[s]=!0,e),{})),b()&&e(c)},className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"font-medium text-sm text-muted-foreground uppercase tracking-wide",children:"Contact Information"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(x.J,{htmlFor:"fullName",children:"Full Name *"}),(0,r.jsx)(v.p,{id:"fullName",value:c.fullName,onChange:e=>N("fullName",e.target.value),onBlur:()=>w("fullName"),placeholder:"Enter your full name",className:u.fullName&&p.fullName?"border-destructive":""}),u.fullName&&p.fullName&&(0,r.jsxs)("div",{className:"flex items-center gap-1 text-destructive text-xs",children:[(0,r.jsx)(y.A,{className:"w-3 h-3"}),u.fullName]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(x.J,{htmlFor:"email",children:"Email Address *"}),(0,r.jsx)(v.p,{id:"email",type:"email",value:c.email,onChange:e=>N("email",e.target.value),onBlur:()=>w("email"),placeholder:"Enter your email",className:u.email&&p.email?"border-destructive":""}),u.email&&p.email&&(0,r.jsxs)("div",{className:"flex items-center gap-1 text-destructive text-xs",children:[(0,r.jsx)(y.A,{className:"w-3 h-3"}),u.email]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(x.J,{htmlFor:"phone",children:"Phone Number *"}),(0,r.jsx)(v.p,{id:"phone",type:"tel",value:c.phone,onChange:e=>N("phone",e.target.value),onBlur:()=>w("phone"),placeholder:"Enter your phone number",className:u.phone&&p.phone?"border-destructive":""}),u.phone&&p.phone&&(0,r.jsxs)("div",{className:"flex items-center gap-1 text-destructive text-xs",children:[(0,r.jsx)(y.A,{className:"w-3 h-3"}),u.phone]})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"font-medium text-sm text-muted-foreground uppercase tracking-wide",children:"Shipping Address"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(x.J,{htmlFor:"address",children:"Street Address *"}),(0,r.jsx)(v.p,{id:"address",value:c.address,onChange:e=>N("address",e.target.value),onBlur:()=>w("address"),placeholder:"Enter your street address",className:u.address&&p.address?"border-destructive":""}),u.address&&p.address&&(0,r.jsxs)("div",{className:"flex items-center gap-1 text-destructive text-xs",children:[(0,r.jsx)(y.A,{className:"w-3 h-3"}),u.address]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(x.J,{htmlFor:"city",children:"City *"}),(0,r.jsx)(v.p,{id:"city",value:c.city,onChange:e=>N("city",e.target.value),onBlur:()=>w("city"),placeholder:"City",className:u.city&&p.city?"border-destructive":""}),u.city&&p.city&&(0,r.jsxs)("div",{className:"flex items-center gap-1 text-destructive text-xs",children:[(0,r.jsx)(y.A,{className:"w-3 h-3"}),u.city]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(x.J,{htmlFor:"state",children:"State *"}),(0,r.jsx)(v.p,{id:"state",value:c.state,onChange:e=>N("state",e.target.value),onBlur:()=>w("state"),placeholder:"State",className:u.state&&p.state?"border-destructive":""}),u.state&&p.state&&(0,r.jsxs)("div",{className:"flex items-center gap-1 text-destructive text-xs",children:[(0,r.jsx)(y.A,{className:"w-3 h-3"}),u.state]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(x.J,{htmlFor:"zipCode",children:"ZIP Code *"}),(0,r.jsx)(v.p,{id:"zipCode",value:c.zipCode,onChange:e=>N("zipCode",e.target.value),onBlur:()=>w("zipCode"),placeholder:"12345",className:u.zipCode&&p.zipCode?"border-destructive":""}),u.zipCode&&p.zipCode&&(0,r.jsxs)("div",{className:"flex items-center gap-1 text-destructive text-xs",children:[(0,r.jsx)(y.A,{className:"w-3 h-3"}),u.zipCode]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(x.J,{htmlFor:"country",children:"Country *"}),(0,r.jsx)("select",{id:"country",value:c.country,onChange:e=>N("country",e.target.value),onBlur:()=>w("country"),className:"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",children:["United States","Canada","United Kingdom","Australia","Germany","France","Japan","Other"].map(e=>(0,r.jsx)("option",{value:e,children:e},e))})]})]}),(0,r.jsx)("div",{className:"flex justify-end pt-4",children:(0,r.jsx)(n.$,{type:"submit",disabled:t||Object.keys(u).some(e=>u[e]),className:"min-w-[120px]",children:t?"Processing...":"Continue to Payment"})})]})})]})};var N=t(30474),w=t(28253);let C=[{type:"cod",name:"Cash on Delivery",description:"Pay when your order is delivered to your doorstep",fee:50,available:!0},{type:"online",name:"Online Payment",description:"Credit Card, Debit Card, PayPal (Coming Soon)",fee:0,available:!1}];var k=t(71057),A=t(19080),P=t(85778);let q=({showTitle:e=!0,compact:s=!1})=>{let{cart:t,getCartCalculations:a}=(0,w._)(),l=a(),n=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e);if(!t||0===t.length)return(0,r.jsx)(i.Zp,{children:(0,r.jsxs)(i.Wu,{className:"flex flex-col items-center justify-center py-12 text-center",children:[(0,r.jsx)(k.A,{className:"w-16 h-16 text-muted-foreground mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Your cart is empty"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Add some items to proceed with checkout"})]})});let d=l.total+50;return(0,r.jsxs)(i.Zp,{children:[e&&(0,r.jsx)(i.aR,{children:(0,r.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(A.A,{className:"w-5 h-5"}),"Order Summary"]})}),(0,r.jsxs)(i.Wu,{className:"space-y-6",children:[(0,r.jsx)("div",{className:"space-y-4",children:t.map(e=>(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsxs)("div",{className:"relative w-16 h-16 bg-muted rounded-md overflow-hidden flex-shrink-0",children:[e.images&&e.images.length>0?(0,r.jsx)(N.default,{src:e.images[0].url,alt:e.title,fill:!0,className:"object-cover",sizes:"64px"}):(0,r.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,r.jsx)(A.A,{className:"w-6 h-6 text-muted-foreground"})}),(0,r.jsx)(p.E,{className:"absolute -top-2 -right-2 w-6 h-6 rounded-full p-0 flex items-center justify-center text-xs",children:e.quantity})]}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h4",{className:"font-medium text-sm leading-tight line-clamp-2",children:e.title}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:e.category}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:[n(e.price)," \xd7 ",e.quantity]}),(0,r.jsx)("span",{className:"font-semibold text-sm",children:n(e.price*e.quantity)})]})]})]},e.productId))}),(0,r.jsx)(h.w,{}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsxs)("span",{className:"text-muted-foreground",children:["Subtotal (",t.reduce((e,s)=>e+s.quantity,0)," items)"]}),(0,r.jsx)("span",{children:n(l.subtotal)})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(P.A,{className:"w-4 h-4 text-muted-foreground"}),(0,r.jsx)("span",{className:"text-muted-foreground",children:"Cash on Delivery Fee"})]}),(0,r.jsx)("span",{children:n(50)})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Shipping"}),(0,r.jsx)("span",{className:"text-green-600 font-medium",children:"Free"})]}),(0,r.jsx)(h.w,{}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Tax (8%)"}),(0,r.jsx)("span",{children:n(l.tax)})]}),(0,r.jsxs)("div",{className:"flex justify-between text-lg font-bold",children:[(0,r.jsx)("span",{children:"Total"}),(0,r.jsx)("span",{className:"text-primary",children:n(d)})]})]}),(0,r.jsxs)("div",{className:"bg-muted/50 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)(P.A,{className:"w-4 h-4 text-primary"}),(0,r.jsx)("span",{className:"font-medium text-sm",children:"Payment Method"})]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Cash on Delivery - Pay when your order arrives at your doorstep"})]}),!s&&(0,r.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-950/20 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)(A.A,{className:"w-4 h-4 text-blue-600"}),(0,r.jsx)("span",{className:"font-medium text-sm text-blue-900 dark:text-blue-100",children:"Estimated Delivery"})]}),(0,r.jsx)("p",{className:"text-sm text-blue-700 dark:text-blue-200",children:"3-5 business days from order confirmation"})]}),!s&&(0,r.jsxs)("div",{className:"text-xs text-muted-foreground space-y-1",children:[(0,r.jsx)("p",{children:"• Free shipping on all orders"}),(0,r.jsx)("p",{children:"• 30-day easy returns"}),(0,r.jsx)("p",{children:"• Secure packaging guaranteed"})]})]})]})};var S=t(58376),F=t(93853),z=t(5336),M=t(99891),R=t(41862),E=t(28561),_=t(88059);let G=()=>{let e=(0,l.useRouter)(),{cart:s,getCartCalculations:t,clearCart:d}=(0,w._)(),{isAuthenticated:c}=(0,j.A)(),[o,v]=(0,a.useState)(null),[g,N]=(0,a.useState)(C[0]),[k,A]=(0,a.useState)(!1),[G,O]=(0,a.useState)(!1),[$,D]=(0,a.useState)(null),B=t();(0,a.useEffect)(()=>{if(!c){F.oR.error("Please sign in to proceed with checkout"),e.push("/signin");return}},[c,e]),(0,a.useEffect)(()=>{if(c&&(!s||0===s.length))return void e.push("/cart")},[s,c,e]);let J=async()=>{if(!o||!k||!s?.length)return void D("Please complete all required fields");if(!g.available)return void D("Selected payment method is not available");O(!0),D(null);try{let s={fullName:o.fullName,address:o.address,phone:o.phone,city:o.city,state:o.state,postalCode:o.zipCode,country:o.country,paymentMethod:g.type},t=await S.Qo.placeOrder(s);if(t.success&&t.data)d(),F.oR.success("Order placed successfully!"),e.push(`/order-confirmation/${t.data._id}`);else throw Error(t.message||"Failed to place order")}catch(s){console.error("Order placement error:",s);let e=s.response?.data?.message||s.message||"Failed to place order. Please try again.";D(e),F.oR.error(e)}finally{O(!1)}};if(!c||!s?.length)return null;let Z=(()=>{let e=B.subtotal,s=B.tax;return e+("cod"===g.type?g.fee:0)+s})();return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Checkout"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Complete your order with secure checkout"})]}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(z.A,{className:"w-5 h-5 text-green-500 mr-2"}),(0,r.jsx)("span",{className:"text-sm font-medium text-green-700",children:"Cart Review"})]}),(0,r.jsx)("div",{className:"flex-1 h-px bg-gray-300"}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-5 h-5 bg-blue-500 rounded-full mr-2"}),(0,r.jsx)("span",{className:"text-sm font-medium text-blue-700",children:"Checkout"})]}),(0,r.jsx)("div",{className:"flex-1 h-px bg-gray-300"}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-5 h-5 bg-gray-300 rounded-full mr-2"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Confirmation"})]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,r.jsx)(b,{onSubmit:e=>{v(e),D(null)},onValidationChange:e=>{A(e)},isSubmitting:G}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsx)(i.aR,{children:(0,r.jsxs)(i.ZB,{className:"flex items-center",children:[(0,r.jsx)(P.A,{className:"w-5 h-5 mr-2"}),"Payment Method"]})}),(0,r.jsxs)(i.Wu,{children:[(0,r.jsx)(u,{value:g.type,onValueChange:e=>{let s=C.find(s=>s.type===e);s&&s.available&&(N(s),D(null))},className:"space-y-3",children:C.map(e=>(0,r.jsxs)("div",{className:`flex items-center space-x-3 p-4 border rounded-lg transition-colors ${e.available?"hover:bg-gray-50 cursor-pointer":"bg-gray-50 cursor-not-allowed opacity-60"} ${g.type===e.type&&e.available?"border-blue-500 bg-blue-50":"border-gray-200"}`,children:[(0,r.jsx)(m,{value:e.type,id:e.type,disabled:!e.available}),(0,r.jsx)(x.J,{htmlFor:e.type,className:`flex-1 cursor-pointer ${!e.available?"cursor-not-allowed":""}`,children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[e.fee>0&&(0,r.jsxs)(p.E,{variant:"secondary",children:["+$",e.fee]}),!e.available&&(0,r.jsx)(p.E,{variant:"outline",children:"Coming Soon"})]})]})})]},e.type))}),(0,r.jsx)("div",{className:"mt-4 p-3 bg-green-50 border border-green-200 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(M.A,{className:"w-4 h-4 text-green-600 mr-2"}),(0,r.jsx)("span",{className:"text-sm text-green-800",children:"Your payment information is secure and encrypted"})]})})]})]}),$&&(0,r.jsxs)(f.Fc,{variant:"destructive",children:[(0,r.jsx)(y.A,{className:"h-4 w-4"}),(0,r.jsx)(f.TN,{children:$})]})]}),(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"sticky top-8",children:[(0,r.jsx)(q,{showTitle:!0,compact:!1}),(0,r.jsx)(i.Zp,{className:"mt-4",children:(0,r.jsxs)(i.Wu,{className:"pt-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{children:"Subtotal"}),(0,r.jsxs)("span",{children:["$",B.subtotal.toFixed(2)]})]}),g.fee>0&&(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{children:"COD Fee"}),(0,r.jsxs)("span",{children:["$",g.fee.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{children:"Tax (8%)"}),(0,r.jsxs)("span",{children:["$",B.tax.toFixed(2)]})]}),(0,r.jsx)(h.w,{}),(0,r.jsxs)("div",{className:"flex justify-between font-semibold",children:[(0,r.jsx)("span",{children:"Total"}),(0,r.jsxs)("span",{children:["$",Z.toFixed(2)]})]})]}),(0,r.jsx)(n.$,{onClick:J,disabled:!k||G||!g.available,className:"w-full mt-6",size:"lg",children:G?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(R.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Placing Order..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(E.A,{className:"w-4 h-4 mr-2"}),"Place Order ($",Z.toFixed(2),")"]})}),(0,r.jsxs)("div",{className:"mt-4 flex items-center justify-center text-xs text-gray-500",children:[(0,r.jsx)(_.A,{className:"w-3 h-3 mr-1"}),"Free delivery on orders over $50"]})]})})]})})]})]})})}},76601:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=t(65239),a=t(48088),l=t(88170),i=t.n(l),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c={children:["",{children:["checkout",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,54787)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\checkout\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\checkout\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/checkout/page",pathname:"/checkout",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},78122:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79219:(e,s,t)=>{Promise.resolve().then(t.bind(t,75838)),Promise.resolve().then(t.bind(t,72080)),Promise.resolve().then(t.bind(t,66981))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,s,t)=>{"use strict";t.d(s,{p:()=>l});var r=t(60687);t(43210);var a=t(4780);function l({className:e,type:s,...t}){return(0,r.jsx)("input",{type:s,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,162,658,598,367,781],()=>t(76601));module.exports=r})();