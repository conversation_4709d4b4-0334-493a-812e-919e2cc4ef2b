"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9449],{8905:(e,n,r)=>{r.d(n,{C:()=>u});var t=r(2115),o=r(6101),a=r(2712),u=e=>{let{present:n,children:r}=e,u=function(e){var n,r;let[o,u]=t.useState(),i=t.useRef(null),s=t.useRef(e),d=t.useRef("none"),[c,f]=(n=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},t.useReducer((e,n)=>{let t=r[e][n];return null!=t?t:e},n));return t.useEffect(()=>{let e=l(i.current);d.current="mounted"===c?e:"none"},[c]),(0,a.N)(()=>{let n=i.current,r=s.current;if(r!==e){let t=d.current,o=l(n);e?f("MOUNT"):"none"===o||(null==n?void 0:n.display)==="none"?f("UNMOUNT"):r&&t!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,a.N)(()=>{if(o){var e;let n,r=null!=(e=o.ownerDocument.defaultView)?e:window,t=e=>{let t=l(i.current).includes(e.animationName);if(e.target===o&&t&&(f("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",n=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(d.current=l(i.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",t),o.addEventListener("animationend",t),()=>{r.clearTimeout(n),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",t),o.removeEventListener("animationend",t)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:t.useCallback(e=>{i.current=e?getComputedStyle(e):null,u(e)},[])}}(n),i="function"==typeof r?r({present:u.isPresent}):t.Children.only(r),s=(0,o.s)(u.ref,function(e){var n,r;let t=null==(n=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:n.get,o=t&&"isReactWarning"in t&&t.isReactWarning;return o?e.ref:(o=(t=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof r||u.isPresent?t.cloneElement(i,{ref:s}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}u.displayName="Presence"},9449:(e,n,r)=>{r.d(n,{UC:()=>e4,q7:()=>e6,ZL:()=>e7,bL:()=>e3,wv:()=>ne,l9:()=>e2});var t=r(2115),o=r(5185),a=r(6101),u=r(6081),l=r(5845),i=r(3655),s=r(7328),d=r(4315),c=r(9178),f=r(2293),p=r(7900),m=r(1285),v=r(5152),w=r(4378),g=r(8905),h=r(9033),x=r(5155),y="rovingFocusGroup.onEntryFocus",b={bubbles:!1,cancelable:!0},M="RovingFocusGroup",[C,R,D]=(0,s.N)(M),[j,N]=(0,u.A)(M,[D]),[_,I]=j(M),T=t.forwardRef((e,n)=>(0,x.jsx)(C.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,x.jsx)(C.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,x.jsx)(k,{...e,ref:n})})}));T.displayName=M;var k=t.forwardRef((e,n)=>{let{__scopeRovingFocusGroup:r,orientation:u,loop:s=!1,dir:c,currentTabStopId:f,defaultCurrentTabStopId:p,onCurrentTabStopIdChange:m,onEntryFocus:v,preventScrollOnEntryFocus:w=!1,...g}=e,C=t.useRef(null),D=(0,a.s)(n,C),j=(0,d.jH)(c),[N,I]=(0,l.i)({prop:f,defaultProp:null!=p?p:null,onChange:m,caller:M}),[T,k]=t.useState(!1),E=(0,h.c)(v),P=R(r),O=t.useRef(!1),[S,F]=t.useState(0);return t.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(y,E),()=>e.removeEventListener(y,E)},[E]),(0,x.jsx)(_,{scope:r,orientation:u,dir:j,loop:s,currentTabStopId:N,onItemFocus:t.useCallback(e=>I(e),[I]),onItemShiftTab:t.useCallback(()=>k(!0),[]),onFocusableItemAdd:t.useCallback(()=>F(e=>e+1),[]),onFocusableItemRemove:t.useCallback(()=>F(e=>e-1),[]),children:(0,x.jsx)(i.sG.div,{tabIndex:T||0===S?-1:0,"data-orientation":u,...g,ref:D,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let n=!O.current;if(e.target===e.currentTarget&&n&&!T){let n=new CustomEvent(y,b);if(e.currentTarget.dispatchEvent(n),!n.defaultPrevented){let e=P().filter(e=>e.focusable);A([e.find(e=>e.active),e.find(e=>e.id===N),...e].filter(Boolean).map(e=>e.ref.current),w)}}O.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>k(!1))})})}),E="RovingFocusGroupItem",P=t.forwardRef((e,n)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:u=!1,tabStopId:l,children:s,...d}=e,c=(0,m.B)(),f=l||c,p=I(E,r),v=p.currentTabStopId===f,w=R(r),{onFocusableItemAdd:g,onFocusableItemRemove:h,currentTabStopId:y}=p;return t.useEffect(()=>{if(a)return g(),()=>h()},[a,g,h]),(0,x.jsx)(C.ItemSlot,{scope:r,id:f,focusable:a,active:u,children:(0,x.jsx)(i.sG.span,{tabIndex:v?0:-1,"data-orientation":p.orientation,...d,ref:n,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?p.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>p.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void p.onItemShiftTab();if(e.target!==e.currentTarget)return;let n=function(e,n,r){var t;let o=(t=e.key,"rtl"!==r?t:"ArrowLeft"===t?"ArrowRight":"ArrowRight"===t?"ArrowLeft":t);if(!("vertical"===n&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===n&&["ArrowUp","ArrowDown"].includes(o)))return O[o]}(e,p.orientation,p.dir);if(void 0!==n){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=w().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===n)r.reverse();else if("prev"===n||"next"===n){"prev"===n&&r.reverse();let t=r.indexOf(e.currentTarget);r=p.loop?function(e,n){return e.map((r,t)=>e[(n+t)%e.length])}(r,t+1):r.slice(t+1)}setTimeout(()=>A(r))}}),children:"function"==typeof s?s({isCurrentTabStop:v,hasTabStop:null!=y}):s})})});P.displayName=E;var O={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function A(e){let n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let t of e)if(t===r||(t.focus({preventScroll:n}),document.activeElement!==r))return}var S=r(9708),F=r(8168),L=r(3795),K=["Enter"," "],U=["ArrowUp","PageDown","End"],G=["ArrowDown","PageUp","Home",...U],B={ltr:[...K,"ArrowRight"],rtl:[...K,"ArrowLeft"]},V={ltr:["ArrowLeft"],rtl:["ArrowRight"]},W="Menu",[X,H,q]=(0,s.N)(W),[z,Z]=(0,u.A)(W,[q,v.Bk,N]),Y=(0,v.Bk)(),J=N(),[Q,$]=z(W),[ee,en]=z(W),er=e=>{let{__scopeMenu:n,open:r=!1,children:o,dir:a,onOpenChange:u,modal:l=!0}=e,i=Y(n),[s,c]=t.useState(null),f=t.useRef(!1),p=(0,h.c)(u),m=(0,d.jH)(a);return t.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",n,{capture:!0,once:!0}),document.addEventListener("pointermove",n,{capture:!0,once:!0})},n=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",n,{capture:!0}),document.removeEventListener("pointermove",n,{capture:!0})}},[]),(0,x.jsx)(v.bL,{...i,children:(0,x.jsx)(Q,{scope:n,open:r,onOpenChange:p,content:s,onContentChange:c,children:(0,x.jsx)(ee,{scope:n,onClose:t.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:m,modal:l,children:o})})})};er.displayName=W;var et=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e,o=Y(r);return(0,x.jsx)(v.Mz,{...o,...t,ref:n})});et.displayName="MenuAnchor";var eo="MenuPortal",[ea,eu]=z(eo,{forceMount:void 0}),el=e=>{let{__scopeMenu:n,forceMount:r,children:t,container:o}=e,a=$(eo,n);return(0,x.jsx)(ea,{scope:n,forceMount:r,children:(0,x.jsx)(g.C,{present:r||a.open,children:(0,x.jsx)(w.Z,{asChild:!0,container:o,children:t})})})};el.displayName=eo;var ei="MenuContent",[es,ed]=z(ei),ec=t.forwardRef((e,n)=>{let r=eu(ei,e.__scopeMenu),{forceMount:t=r.forceMount,...o}=e,a=$(ei,e.__scopeMenu),u=en(ei,e.__scopeMenu);return(0,x.jsx)(X.Provider,{scope:e.__scopeMenu,children:(0,x.jsx)(g.C,{present:t||a.open,children:(0,x.jsx)(X.Slot,{scope:e.__scopeMenu,children:u.modal?(0,x.jsx)(ef,{...o,ref:n}):(0,x.jsx)(ep,{...o,ref:n})})})})}),ef=t.forwardRef((e,n)=>{let r=$(ei,e.__scopeMenu),u=t.useRef(null),l=(0,a.s)(n,u);return t.useEffect(()=>{let e=u.current;if(e)return(0,F.Eq)(e)},[]),(0,x.jsx)(ev,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),ep=t.forwardRef((e,n)=>{let r=$(ei,e.__scopeMenu);return(0,x.jsx)(ev,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),em=(0,S.TL)("MenuContent.ScrollLock"),ev=t.forwardRef((e,n)=>{let{__scopeMenu:r,loop:u=!1,trapFocus:l,onOpenAutoFocus:i,onCloseAutoFocus:s,disableOutsidePointerEvents:d,onEntryFocus:m,onEscapeKeyDown:w,onPointerDownOutside:g,onFocusOutside:h,onInteractOutside:y,onDismiss:b,disableOutsideScroll:M,...C}=e,R=$(ei,r),D=en(ei,r),j=Y(r),N=J(r),_=H(r),[I,k]=t.useState(null),E=t.useRef(null),P=(0,a.s)(n,E,R.onContentChange),O=t.useRef(0),A=t.useRef(""),S=t.useRef(0),F=t.useRef(null),K=t.useRef("right"),B=t.useRef(0),V=M?L.A:t.Fragment,W=e=>{var n,r;let t=A.current+e,o=_().filter(e=>!e.disabled),a=document.activeElement,u=null==(n=o.find(e=>e.ref.current===a))?void 0:n.textValue,l=function(e,n,r){var t;let o=n.length>1&&Array.from(n).every(e=>e===n[0])?n[0]:n,a=r?e.indexOf(r):-1,u=(t=Math.max(a,0),e.map((n,r)=>e[(t+r)%e.length]));1===o.length&&(u=u.filter(e=>e!==r));let l=u.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==r?l:void 0}(o.map(e=>e.textValue),t,u),i=null==(r=o.find(e=>e.textValue===l))?void 0:r.ref.current;!function e(n){A.current=n,window.clearTimeout(O.current),""!==n&&(O.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};t.useEffect(()=>()=>window.clearTimeout(O.current),[]),(0,f.Oh)();let X=t.useCallback(e=>{var n,r;return K.current===(null==(n=F.current)?void 0:n.side)&&function(e,n){return!!n&&function(e,n){let{x:r,y:t}=e,o=!1;for(let e=0,a=n.length-1;e<n.length;a=e++){let u=n[e],l=n[a],i=u.x,s=u.y,d=l.x,c=l.y;s>t!=c>t&&r<(d-i)*(t-s)/(c-s)+i&&(o=!o)}return o}({x:e.clientX,y:e.clientY},n)}(e,null==(r=F.current)?void 0:r.area)},[]);return(0,x.jsx)(es,{scope:r,searchRef:A,onItemEnter:t.useCallback(e=>{X(e)&&e.preventDefault()},[X]),onItemLeave:t.useCallback(e=>{var n;X(e)||(null==(n=E.current)||n.focus(),k(null))},[X]),onTriggerLeave:t.useCallback(e=>{X(e)&&e.preventDefault()},[X]),pointerGraceTimerRef:S,onPointerGraceIntentChange:t.useCallback(e=>{F.current=e},[]),children:(0,x.jsx)(V,{...M?{as:em,allowPinchZoom:!0}:void 0,children:(0,x.jsx)(p.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.m)(i,e=>{var n;e.preventDefault(),null==(n=E.current)||n.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,x.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:w,onPointerDownOutside:g,onFocusOutside:h,onInteractOutside:y,onDismiss:b,children:(0,x.jsx)(T,{asChild:!0,...N,dir:D.dir,orientation:"vertical",loop:u,currentTabStopId:I,onCurrentTabStopIdChange:k,onEntryFocus:(0,o.m)(m,e=>{D.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,x.jsx)(v.UC,{role:"menu","aria-orientation":"vertical","data-state":eG(R.open),"data-radix-menu-content":"",dir:D.dir,...j,...C,ref:P,style:{outline:"none",...C.style},onKeyDown:(0,o.m)(C.onKeyDown,e=>{let n=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;n&&("Tab"===e.key&&e.preventDefault(),!r&&t&&W(e.key));let o=E.current;if(e.target!==o||!G.includes(e.key))return;e.preventDefault();let a=_().filter(e=>!e.disabled).map(e=>e.ref.current);U.includes(e.key)&&a.reverse(),function(e){let n=document.activeElement;for(let r of e)if(r===n||(r.focus(),document.activeElement!==n))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(O.current),A.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eW(e=>{let n=e.target,r=B.current!==e.clientX;e.currentTarget.contains(n)&&r&&(K.current=e.clientX>B.current?"right":"left",B.current=e.clientX)}))})})})})})})});ec.displayName=ei;var ew=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,x.jsx)(i.sG.div,{role:"group",...t,ref:n})});ew.displayName="MenuGroup";var eg=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,x.jsx)(i.sG.div,{...t,ref:n})});eg.displayName="MenuLabel";var eh="MenuItem",ex="menu.itemSelect",ey=t.forwardRef((e,n)=>{let{disabled:r=!1,onSelect:u,...l}=e,s=t.useRef(null),d=en(eh,e.__scopeMenu),c=ed(eh,e.__scopeMenu),f=(0,a.s)(n,s),p=t.useRef(!1);return(0,x.jsx)(eb,{...l,ref:f,disabled:r,onClick:(0,o.m)(e.onClick,()=>{let e=s.current;if(!r&&e){let n=new CustomEvent(ex,{bubbles:!0,cancelable:!0});e.addEventListener(ex,e=>null==u?void 0:u(e),{once:!0}),(0,i.hO)(e,n),n.defaultPrevented?p.current=!1:d.onClose()}}),onPointerDown:n=>{var r;null==(r=e.onPointerDown)||r.call(e,n),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var n;p.current||null==(n=e.currentTarget)||n.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let n=""!==c.searchRef.current;r||n&&" "===e.key||K.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ey.displayName=eh;var eb=t.forwardRef((e,n)=>{let{__scopeMenu:r,disabled:u=!1,textValue:l,...s}=e,d=ed(eh,r),c=J(r),f=t.useRef(null),p=(0,a.s)(n,f),[m,v]=t.useState(!1),[w,g]=t.useState("");return t.useEffect(()=>{let e=f.current;if(e){var n;g((null!=(n=e.textContent)?n:"").trim())}},[s.children]),(0,x.jsx)(X.ItemSlot,{scope:r,disabled:u,textValue:null!=l?l:w,children:(0,x.jsx)(P,{asChild:!0,...c,focusable:!u,children:(0,x.jsx)(i.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":u||void 0,"data-disabled":u?"":void 0,...s,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,eW(e=>{u?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eW(e=>d.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>v(!0)),onBlur:(0,o.m)(e.onBlur,()=>v(!1))})})})}),eM=t.forwardRef((e,n)=>{let{checked:r=!1,onCheckedChange:t,...a}=e;return(0,x.jsx)(eT,{scope:e.__scopeMenu,checked:r,children:(0,x.jsx)(ey,{role:"menuitemcheckbox","aria-checked":eB(r)?"mixed":r,...a,ref:n,"data-state":eV(r),onSelect:(0,o.m)(a.onSelect,()=>null==t?void 0:t(!!eB(r)||!r),{checkForDefaultPrevented:!1})})})});eM.displayName="MenuCheckboxItem";var eC="MenuRadioGroup",[eR,eD]=z(eC,{value:void 0,onValueChange:()=>{}}),ej=t.forwardRef((e,n)=>{let{value:r,onValueChange:t,...o}=e,a=(0,h.c)(t);return(0,x.jsx)(eR,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,x.jsx)(ew,{...o,ref:n})})});ej.displayName=eC;var eN="MenuRadioItem",e_=t.forwardRef((e,n)=>{let{value:r,...t}=e,a=eD(eN,e.__scopeMenu),u=r===a.value;return(0,x.jsx)(eT,{scope:e.__scopeMenu,checked:u,children:(0,x.jsx)(ey,{role:"menuitemradio","aria-checked":u,...t,ref:n,"data-state":eV(u),onSelect:(0,o.m)(t.onSelect,()=>{var e;return null==(e=a.onValueChange)?void 0:e.call(a,r)},{checkForDefaultPrevented:!1})})})});e_.displayName=eN;var eI="MenuItemIndicator",[eT,ek]=z(eI,{checked:!1}),eE=t.forwardRef((e,n)=>{let{__scopeMenu:r,forceMount:t,...o}=e,a=ek(eI,r);return(0,x.jsx)(g.C,{present:t||eB(a.checked)||!0===a.checked,children:(0,x.jsx)(i.sG.span,{...o,ref:n,"data-state":eV(a.checked)})})});eE.displayName=eI;var eP=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,x.jsx)(i.sG.div,{role:"separator","aria-orientation":"horizontal",...t,ref:n})});eP.displayName="MenuSeparator";var eO=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e,o=Y(r);return(0,x.jsx)(v.i3,{...o,...t,ref:n})});eO.displayName="MenuArrow";var[eA,eS]=z("MenuSub"),eF="MenuSubTrigger",eL=t.forwardRef((e,n)=>{let r=$(eF,e.__scopeMenu),u=en(eF,e.__scopeMenu),l=eS(eF,e.__scopeMenu),i=ed(eF,e.__scopeMenu),s=t.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:c}=i,f={__scopeMenu:e.__scopeMenu},p=t.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return t.useEffect(()=>p,[p]),t.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),c(null)}},[d,c]),(0,x.jsx)(et,{asChild:!0,...f,children:(0,x.jsx)(eb,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":l.contentId,"data-state":eG(r.open),...e,ref:(0,a.t)(n,l.onTriggerChange),onClick:n=>{var t;null==(t=e.onClick)||t.call(e,n),e.disabled||n.defaultPrevented||(n.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eW(n=>{i.onItemEnter(n),!n.defaultPrevented&&(e.disabled||r.open||s.current||(i.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eW(e=>{var n,t;p();let o=null==(n=r.content)?void 0:n.getBoundingClientRect();if(o){let n=null==(t=r.content)?void 0:t.dataset.side,a="right"===n,u=o[a?"left":"right"],l=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:u,y:o.top},{x:l,y:o.top},{x:l,y:o.bottom},{x:u,y:o.bottom}],side:n}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,n=>{let t=""!==i.searchRef.current;if(!e.disabled&&(!t||" "!==n.key)&&B[u.dir].includes(n.key)){var o;r.onOpenChange(!0),null==(o=r.content)||o.focus(),n.preventDefault()}})})})});eL.displayName=eF;var eK="MenuSubContent",eU=t.forwardRef((e,n)=>{let r=eu(ei,e.__scopeMenu),{forceMount:u=r.forceMount,...l}=e,i=$(ei,e.__scopeMenu),s=en(ei,e.__scopeMenu),d=eS(eK,e.__scopeMenu),c=t.useRef(null),f=(0,a.s)(n,c);return(0,x.jsx)(X.Provider,{scope:e.__scopeMenu,children:(0,x.jsx)(g.C,{present:u||i.open,children:(0,x.jsx)(X.Slot,{scope:e.__scopeMenu,children:(0,x.jsx)(ev,{id:d.contentId,"aria-labelledby":d.triggerId,...l,ref:f,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var n;s.isUsingKeyboardRef.current&&(null==(n=c.current)||n.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let n=e.currentTarget.contains(e.target),r=V[s.dir].includes(e.key);if(n&&r){var t;i.onOpenChange(!1),null==(t=d.trigger)||t.focus(),e.preventDefault()}})})})})})});function eG(e){return e?"open":"closed"}function eB(e){return"indeterminate"===e}function eV(e){return eB(e)?"indeterminate":e?"checked":"unchecked"}function eW(e){return n=>"mouse"===n.pointerType?e(n):void 0}eU.displayName=eK;var eX="DropdownMenu",[eH,eq]=(0,u.A)(eX,[Z]),ez=Z(),[eZ,eY]=eH(eX),eJ=e=>{let{__scopeDropdownMenu:n,children:r,dir:o,open:a,defaultOpen:u,onOpenChange:i,modal:s=!0}=e,d=ez(n),c=t.useRef(null),[f,p]=(0,l.i)({prop:a,defaultProp:null!=u&&u,onChange:i,caller:eX});return(0,x.jsx)(eZ,{scope:n,triggerId:(0,m.B)(),triggerRef:c,contentId:(0,m.B)(),open:f,onOpenChange:p,onOpenToggle:t.useCallback(()=>p(e=>!e),[p]),modal:s,children:(0,x.jsx)(er,{...d,open:f,onOpenChange:p,dir:o,modal:s,children:r})})};eJ.displayName=eX;var eQ="DropdownMenuTrigger",e$=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,disabled:t=!1,...u}=e,l=eY(eQ,r),s=ez(r);return(0,x.jsx)(et,{asChild:!0,...s,children:(0,x.jsx)(i.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...u,ref:(0,a.t)(n,l.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!t&&0===e.button&&!1===e.ctrlKey&&(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!t&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});e$.displayName=eQ;var e0=e=>{let{__scopeDropdownMenu:n,...r}=e,t=ez(n);return(0,x.jsx)(el,{...t,...r})};e0.displayName="DropdownMenuPortal";var e1="DropdownMenuContent",e5=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...a}=e,u=eY(e1,r),l=ez(r),i=t.useRef(!1);return(0,x.jsx)(ec,{id:u.contentId,"aria-labelledby":u.triggerId,...l,...a,ref:n,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var n;i.current||null==(n=u.triggerRef.current)||n.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let n=e.detail.originalEvent,r=0===n.button&&!0===n.ctrlKey,t=2===n.button||r;(!u.modal||t)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e5.displayName=e1,t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=ez(r);return(0,x.jsx)(ew,{...o,...t,ref:n})}).displayName="DropdownMenuGroup",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=ez(r);return(0,x.jsx)(eg,{...o,...t,ref:n})}).displayName="DropdownMenuLabel";var e9=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=ez(r);return(0,x.jsx)(ey,{...o,...t,ref:n})});e9.displayName="DropdownMenuItem",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=ez(r);return(0,x.jsx)(eM,{...o,...t,ref:n})}).displayName="DropdownMenuCheckboxItem",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=ez(r);return(0,x.jsx)(ej,{...o,...t,ref:n})}).displayName="DropdownMenuRadioGroup",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=ez(r);return(0,x.jsx)(e_,{...o,...t,ref:n})}).displayName="DropdownMenuRadioItem",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=ez(r);return(0,x.jsx)(eE,{...o,...t,ref:n})}).displayName="DropdownMenuItemIndicator";var e8=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=ez(r);return(0,x.jsx)(eP,{...o,...t,ref:n})});e8.displayName="DropdownMenuSeparator",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=ez(r);return(0,x.jsx)(eO,{...o,...t,ref:n})}).displayName="DropdownMenuArrow",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=ez(r);return(0,x.jsx)(eL,{...o,...t,ref:n})}).displayName="DropdownMenuSubTrigger",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=ez(r);return(0,x.jsx)(eU,{...o,...t,ref:n,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var e3=eJ,e2=e$,e7=e0,e4=e5,e6=e9,ne=e8}}]);