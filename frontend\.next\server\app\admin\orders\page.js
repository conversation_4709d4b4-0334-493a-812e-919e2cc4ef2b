(()=>{var e={};e.id=798,e.ids=[798],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5220:(e,s,r)=>{"use strict";r.d(s,{default:()=>z});var t=r(60687),a=r(43210),l=r(58376),i=r(29523),d=r(89667),n=r(44493),c=r(96834),o=r(91821),x=r(85726),m=r(19080),h=r(48730),u=r(5336),p=r(23928),j=r(99270),f=r(80462),g=r(93613),N=r(88059),v=r(62688);let y=(0,v.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var w=r(40228),b=r(58869),A=r(13861),k=r(93661),C=r(93853),P=r(21342),S=r(30474),_=r(39907),R=r(28559);let D=(0,v.A)("printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]]);var O=r(63143),q=r(41550),M=r(48340),Z=r(97992),I=r(85778);let L=({order:e,onBack:s,onOrderUpdate:r})=>{let[d,o]=(0,a.useState)(!1),x=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),j=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),f=async s=>{o(!0);try{let t={orderId:e._id,status:s},a=await l.ZJ.changeDeliveryStatus(t);a.success?(C.oR.success("Order status updated successfully"),r()):C.oR.error(a.message||"Failed to update order status")}catch(s){let e=s.response?.data?.message||"An error occurred while updating order status";C.oR.error(e)}finally{o(!1)}},g=e.totalRevenue+e.codCharges;return(0,t.jsxs)("div",{className:"space-y-6 print:space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between print:hidden",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(i.$,{variant:"ghost",size:"icon",onClick:s,children:(0,t.jsx)(R.A,{className:"h-4 w-4"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Order Details"}),(0,t.jsxs)("p",{className:"text-muted-foreground",children:["Order #",e._id.slice(-8)]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(i.$,{variant:"outline",onClick:()=>{window.print()},children:[(0,t.jsx)(D,{className:"h-4 w-4 mr-2"}),"Print"]}),(0,t.jsxs)(P.rI,{children:[(0,t.jsx)(P.ty,{asChild:!0,children:(0,t.jsxs)(i.$,{disabled:d,children:[(0,t.jsx)(O.A,{className:"h-4 w-4 mr-2"}),"Update Status"]})}),(0,t.jsxs)(P.SQ,{align:"end",children:[(0,t.jsxs)(P._2,{onClick:()=>f("pending"),children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Mark as Pending"]}),(0,t.jsxs)(P._2,{onClick:()=>f("shipped"),children:[(0,t.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"Mark as Shipped"]}),(0,t.jsxs)(P._2,{onClick:()=>f("delivered"),children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Mark as Delivered"]}),(0,t.jsxs)(P._2,{onClick:()=>f("cancelled"),children:[(0,t.jsx)(y,{className:"h-4 w-4 mr-2"}),"Mark as Cancelled"]})]})]})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.A,{className:"h-5 w-5"}),(0,t.jsxs)("span",{children:["Order #",e._id.slice(-8)]})]}),(0,t.jsxs)(n.BT,{children:["Placed on ",j(e.createdAt)]})]}),(0,t.jsxs)(c.E,{className:`${(e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"shipped":return"bg-blue-100 text-blue-800";case"delivered":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(e.status)} flex items-center space-x-1`,children:[(e=>{switch(e){case"pending":return(0,t.jsx)(h.A,{className:"h-4 w-4"});case"shipped":return(0,t.jsx)(N.A,{className:"h-4 w-4"});case"delivered":return(0,t.jsx)(u.A,{className:"h-4 w-4"});case"cancelled":return(0,t.jsx)(y,{className:"h-4 w-4"});default:return(0,t.jsx)(m.A,{className:"h-4 w-4"})}})(e.status),(0,t.jsx)("span",{className:"capitalize",children:e.status})]})]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Items"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:e.orderItems.length})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total Amount"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-primary",children:x(g)})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Payment Method"}),(0,t.jsx)("p",{className:"text-lg font-medium",children:"Cash on Delivery"})]})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(b.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Customer Information"})]})}),(0,t.jsx)(n.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-lg",children:e.user.fullName}),(0,t.jsxs)("div",{className:"space-y-2 mt-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,t.jsx)(q.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{children:e.user.email})]}),e.user.phoneNumber&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,t.jsx)(M.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{children:e.user.phoneNumber})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsxs)("span",{children:["Customer since ",j(e.user.createdAt)]})]})]})]})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(Z.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Shipping Address"})]})}),(0,t.jsx)(n.Wu,{children:e.shippingDetails[0]?(0,t.jsxs)("div",{className:"space-y-2",children:[e.shippingDetails[0].address&&(0,t.jsx)("p",{className:"font-medium",children:e.shippingDetails[0].address}),(0,t.jsxs)("p",{children:[e.shippingDetails[0].city&&`${e.shippingDetails[0].city}, `,e.shippingDetails[0].state&&`${e.shippingDetails[0].state} `,e.shippingDetails[0].postalCode]}),e.shippingDetails[0].country&&(0,t.jsx)("p",{className:"text-muted-foreground",children:e.shippingDetails[0].country})]}):(0,t.jsx)("p",{className:"text-muted-foreground",children:"No address information available"})})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{children:"Order Items"}),(0,t.jsxs)(n.BT,{children:[e.orderItems.length," item(s) in this order"]})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:e.orderItems.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-4 p-4 border border-border rounded-lg",children:[e.product.images?.[0]?.url?(0,t.jsx)(S.default,{src:e.product.images[0]?.url,alt:e.product.title,width:64,height:64,className:"object-cover rounded-lg"}):(0,t.jsx)("div",{className:"w-16 h-16 bg-muted rounded-lg flex items-center justify-center",children:(0,t.jsx)(m.A,{className:"h-6 w-6 text-muted-foreground"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h4",{className:"font-semibold",children:e.product.title}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-2",children:e.product.description}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 mt-2",children:[(0,t.jsxs)("span",{className:"text-sm",children:["Qty: ",e.quantity]}),(0,t.jsxs)("span",{className:"text-sm",children:["Weight: ",e.product.weight]}),(0,t.jsxs)("span",{className:"text-sm",children:["Category: ",e.product.category]})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("p",{className:"font-semibold",children:x(e.product.price)}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"per item"}),(0,t.jsx)("p",{className:"font-bold text-primary",children:x(e.product.price*e.quantity)})]})]},s))})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(I.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Payment Summary"})]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Subtotal"}),(0,t.jsx)("span",{children:x(e.totalRevenue)})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"COD Charges"}),(0,t.jsx)("span",{children:x(e.codCharges)})]}),(0,t.jsx)(_.w,{}),(0,t.jsxs)("div",{className:"flex justify-between font-bold text-lg",children:[(0,t.jsx)("span",{children:"Total"}),(0,t.jsx)("span",{className:"text-primary",children:x(g)})]}),(0,t.jsxs)("div",{className:"mt-4 p-3 bg-muted rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Payment Method: Cash on Delivery"})]}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Payment will be collected upon delivery"})]})]})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{children:"Order Timeline"}),(0,t.jsx)(n.BT,{children:"Track the progress of this order"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-primary rounded-full flex items-center justify-center",children:(0,t.jsx)(u.A,{className:"h-4 w-4 text-primary-foreground"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Order Placed"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:j(e.createdAt)})]})]}),"pending"!==e.status&&(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${"shipped"===e.status||"delivered"===e.status?"bg-primary":"bg-muted"}`,children:(0,t.jsx)(N.A,{className:`h-4 w-4 ${"shipped"===e.status||"delivered"===e.status?"text-primary-foreground":"text-muted-foreground"}`})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Order Shipped"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"shipped"===e.status||"delivered"===e.status?"In transit":"Pending shipment"})]})]}),"delivered"===e.status&&(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center",children:(0,t.jsx)(u.A,{className:"h-4 w-4 text-white"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Order Delivered"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Successfully delivered"})]})]}),"cancelled"===e.status&&(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-red-500 rounded-full flex items-center justify-center",children:(0,t.jsx)(y,{className:"h-4 w-4 text-white"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Order Cancelled"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Order has been cancelled"})]})]})]})})]})]})},U=({order:e,onStatusUpdate:s,onDelete:r,onViewDetails:a})=>{let d;return e?(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex-1 space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{className:"font-semibold text-lg",children:["Order #",e._id.slice(-8)]}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground flex items-center",children:[(0,t.jsx)(w.A,{className:"h-3 w-3 mr-1"}),l.Qo.formatOrderDate(e.createdAt)]})]}),(0,t.jsxs)(c.E,{className:`${(e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"shipped":return"bg-blue-100 text-blue-800";case"delivered":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(e.status)} flex items-center space-x-1`,children:[(e=>{switch(e){case"pending":return(0,t.jsx)(h.A,{className:"h-4 w-4"});case"shipped":return(0,t.jsx)(N.A,{className:"h-4 w-4"});case"delivered":return(0,t.jsx)(u.A,{className:"h-4 w-4"});case"cancelled":return(0,t.jsx)(y,{className:"h-4 w-4"});default:return(0,t.jsx)(m.A,{className:"h-4 w-4"})}})(e.status),(0,t.jsx)("span",{className:"capitalize",children:e.status})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Customer"}),(0,t.jsxs)("p",{className:"font-medium flex items-center",children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-1"}),e.user.fullName]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.user.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Items"}),(0,t.jsxs)("p",{className:"font-medium",children:[e.orderItems.length," item(s)"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total"}),(0,t.jsx)("p",{className:"font-bold text-lg text-primary",children:(d=e.totalRevenue,new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(d))})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(i.$,{variant:"outline",size:"sm",onClick:a,children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-1"}),"View"]}),(0,t.jsxs)(P.rI,{children:[(0,t.jsx)(P.ty,{asChild:!0,children:(0,t.jsx)(i.$,{variant:"outline",size:"sm",children:(0,t.jsx)(k.A,{className:"h-4 w-4"})})}),(0,t.jsxs)(P.SQ,{align:"end",children:[(0,t.jsx)(P._2,{onClick:()=>s(e._id,"pending"),children:"Mark as Pending"}),(0,t.jsx)(P._2,{onClick:()=>s(e._id,"shipped"),children:"Mark as Shipped"}),(0,t.jsx)(P._2,{onClick:()=>s(e._id,"delivered"),children:"Mark as Delivered"}),(0,t.jsx)(P._2,{onClick:()=>s(e._id,"cancelled"),children:"Mark as Cancelled"}),(0,t.jsx)(P._2,{onClick:r,className:"text-destructive",children:"Delete Order"})]})]})]})]})})}):(0,t.jsx)("div",{children:"No orders found"})},z=()=>{let[e,s]=(0,a.useState)([]),[r,c]=(0,a.useState)(!0),[N,v]=(0,a.useState)(""),[y,w]=(0,a.useState)(""),[b,A]=(0,a.useState)(""),[k,P]=(0,a.useState)(null),[S,_]=(0,a.useState)(!1),[R,D]=(0,a.useState)(0),O=async()=>{try{c(!0);let e=await l.ZJ.getAllOrders();e.success&&e.data?(s(e.data.orders),D(e.data.totalSales)):v(e.message||"Failed to load orders")}catch(s){let e=s.response?.data?.message||"An error occurred while loading orders";v(e),C.oR.error(e)}finally{c(!1)}};(0,a.useEffect)(()=>{O()},[]);let q=async(e,s)=>{try{let r=await l.ZJ.changeDeliveryStatus({orderId:e,status:s});r.success?(C.oR.success("Order status updated successfully"),O()):C.oR.error(r.message||"Failed to update order status")}catch(s){let e=s.response?.data?.message||"An error occurred while updating order status";C.oR.error(e)}},M=async e=>{if(confirm("Are you sure you want to delete this order?"))try{let s=await l.ZJ.deleteOrder(e,"Order deleted by admin");s.success?(C.oR.success("Order deleted successfully"),O()):C.oR.error(s.message||"Failed to delete order")}catch(s){let e=s.response?.data?.message||"An error occurred while deleting order";C.oR.error(e)}},Z=e=>{P(e),_(!0)},I=e.filter(e=>{let s=e._id.toLowerCase().includes(y.toLowerCase())||e.user.fullName.toLowerCase().includes(y.toLowerCase())||e.user.email.toLowerCase().includes(y.toLowerCase()),r=!b||e.status===b;return s&&r}),z=l.Qo.getOrderStats(e);return S&&k?(0,t.jsx)(L,{order:k,onBack:()=>{_(!1),P(null)},onOrderUpdate:()=>{O(),_(!1),P(null)}}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Order Management"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"View and manage customer orders, update order status, and handle order-related tasks."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Orders"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:z.total})]}),(0,t.jsx)(m.A,{className:"h-8 w-8 text-muted-foreground"})]})})}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Pending Orders"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:z.pending})]}),(0,t.jsx)(h.A,{className:"h-8 w-8 text-yellow-600"})]})})}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Delivered Orders"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-green-600",children:z.delivered})]}),(0,t.jsx)(u.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Revenue"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-primary",children:new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(R)})]}),(0,t.jsx)(p.A,{className:"h-8 w-8 text-primary"})]})})})]}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"pt-6",children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,t.jsxs)("div",{className:"relative flex-1",children:[(0,t.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(d.p,{placeholder:"Search orders by ID, customer name, or email...",value:y,onChange:e=>w(e.target.value),className:"pl-10"})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("select",{value:b,onChange:e=>A(e.target.value),className:"px-3 py-2 border border-border rounded-md bg-background text-foreground",children:[(0,t.jsx)("option",{value:"",children:"All Statuses"}),(0,t.jsx)("option",{value:"pending",children:"Pending"}),(0,t.jsx)("option",{value:"shipped",children:"Shipped"}),(0,t.jsx)("option",{value:"delivered",children:"Delivered"}),(0,t.jsx)("option",{value:"cancelled",children:"Cancelled"})]}),(0,t.jsx)(i.$,{variant:"outline",size:"icon",children:(0,t.jsx)(f.A,{className:"h-4 w-4"})})]})]})})}),N&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(g.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{children:"Error loading orders"}),(0,t.jsx)("p",{children:N})]})]}),r&&(0,t.jsx)("div",{className:"space-y-4",children:Array.from({length:5}).map((e,s)=>(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.E,{className:"h-4 w-32"}),(0,t.jsx)(x.E,{className:"h-4 w-48"}),(0,t.jsx)(x.E,{className:"h-4 w-24"})]}),(0,t.jsx)(x.E,{className:"h-8 w-20"})]})})},s))}),!r&&!N&&(0,t.jsx)(t.Fragment,{children:0===I.length?(0,t.jsx)(n.Zp,{children:(0,t.jsxs)(n.Wu,{className:"p-8 text-center",children:[(0,t.jsx)(m.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No orders found"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:y||b?"No orders match your current filters.":"No orders have been placed yet."})]})}):(0,t.jsx)("div",{className:"space-y-4",children:I.map(e=>(0,t.jsx)(U,{order:e,onStatusUpdate:q,onDelete:()=>M(e._id),onViewDetails:()=>Z(e)},e._id))})})]})}},5336:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},7956:(e,s,r)=>{"use strict";r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminProtectedRoute.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminProtectedRoute.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23928:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},25525:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var t=r(65239),a=r(48088),l=r(88170),i=r.n(l),d=r(30893),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);r.d(s,n);let c={children:["",{children:["admin",{children:["orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,58322)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\orders\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,99111)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\orders\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/orders/page",pathname:"/admin/orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},25797:(e,s,r)=>{"use strict";r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\components\\\\admin\\\\OrderManagement.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\OrderManagement.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},28776:(e,s,r)=>{Promise.resolve().then(r.bind(r,73482)),Promise.resolve().then(r.bind(r,5220))},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40228:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},48730:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58322:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i});var t=r(37413),a=r(7956),l=r(25797);function i(){return(0,t.jsx)(a.default,{children:(0,t.jsx)(l.default,{})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},65224:(e,s,r)=>{Promise.resolve().then(r.bind(r,7956)),Promise.resolve().then(r.bind(r,25797))},73482:(e,s,r)=>{"use strict";r.d(s,{default:()=>c});var t=r(60687);r(43210);var a=r(58873),l=r(91821),i=r(93613),d=r(99891);let n=(e,s)=>"admin"===s?"admin"===e.role||"superAdmin"===e.role:"superAdmin"===s&&"superAdmin"===e.role,c=({children:e,requiredRole:s,fallback:r})=>{let{admin:c,isAuthenticated:o,isLoading:x}=(0,a.b)();return x?r||(0,t.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})}):o&&c?s&&!n(c,s)?r||(0,t.jsxs)(l.Fc,{variant:"destructive",children:[(0,t.jsx)(d.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{children:"Insufficient Permissions"}),(0,t.jsxs)("p",{children:["You need ",s," privileges to access this page. Your current role is: ",c.role]})]})]}):(0,t.jsx)(t.Fragment,{children:e}):r||(0,t.jsxs)(l.Fc,{variant:"destructive",children:[(0,t.jsx)(i.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{children:"Access Denied"}),(0,t.jsx)("p",{children:"You must be logged in as an admin to access this page."})]})]})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80462:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},93661:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,162,658,598,367,10],()=>r(25525));module.exports=t})();