(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1093,6543,7122],{1274:(e,t,r)=>{Promise.resolve().then(r.bind(r,4368))},3779:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>c,b:()=>n});var s=r(5155),a=r(2115),l=r(5654);let i=(0,a.createContext)(void 0),n=()=>{let e=(0,a.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},c=e=>{let{children:t}=e,[r,n]=(0,a.useState)(null),[c,d]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(async()=>{if(localStorage.getItem("access_token"))try{let e=await l.ZJ.getAdmin();e.success&&e.data&&n(e.data)}catch(e){e.response&&(console.error("Error checking auth status:",e.response.data.message),localStorage.removeItem("access_token"))}d(!1)})()},[]);let o=async()=>{try{await l.ZJ.logout()}catch(e){console.error("Error during logout:",e)}finally{localStorage.removeItem("access_token"),n(null)}};return(0,s.jsx)(i.Provider,{value:{admin:r,isLoading:c,isAuthenticated:!!r,login:(e,t)=>{localStorage.setItem("access_token",t),n(e)},logout:o},children:t})}},4368:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var s=r(5155);r(2115);var a=r(3779),l=r(5365),i=r(5339),n=r(5525);let c=(e,t)=>"admin"===t?"admin"===e.role||"superAdmin"===e.role:"superAdmin"===t&&"superAdmin"===e.role,d=e=>{let{children:t,requiredRole:r,fallback:d}=e,{admin:o,isAuthenticated:u,isLoading:m}=(0,a.b)();return m?d||(0,s.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})}):u&&o?r&&!c(o,r)?d||(0,s.jsxs)(l.Fc,{variant:"destructive",children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{children:"Insufficient Permissions"}),(0,s.jsxs)("p",{children:["You need ",r," privileges to access this page. Your current role is: ",o.role]})]})]}):(0,s.jsx)(s.Fragment,{children:t}):d||(0,s.jsxs)(l.Fc,{variant:"destructive",children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{children:"Access Denied"}),(0,s.jsx)("p",{children:"You must be logged in as an admin to access this page."})]})]})}},5339:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5365:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>c,TN:()=>d});var s=r(5155),a=r(2115),l=r(2085),i=r(9434);let n=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=a.forwardRef((e,t)=>{let{className:r,variant:a,...l}=e;return(0,s.jsx)("div",{ref:t,role:"alert",className:(0,i.cn)(n({variant:a}),r),...l})});c.displayName="Alert",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h5",{ref:t,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",r),...a})}).displayName="AlertTitle";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",r),...a})});d.displayName="AlertDescription"},5525:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,9078,7389,8441,1684,7358],()=>t(1274)),_N_E=e.O()}]);