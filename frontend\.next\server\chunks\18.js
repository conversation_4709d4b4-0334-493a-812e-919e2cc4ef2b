"use strict";exports.id=18,exports.ids=[18],exports.modules={16018:(e,a,s)=>{s.r(a),s.d(a,{default:()=>M});var t=s(60687),r=s(43210),i=s.n(r),l=s(85814),c=s.n(l),o=s(44493),d=s(29523),n=s(96834),h=s(26001),m=s(62688);let x=(0,m.A)("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]),u=(0,m.A)("shirt",[["path",{d:"M20.38 3.46 16 2a4 4 0 0 1-8 0L3.62 3.46a2 2 0 0 0-1.34 2.23l.58 3.47a1 1 0 0 0 .99.84H6v10c0 1.1.9 2 2 2h8a2 2 0 0 0 2-2V10h2.15a1 1 0 0 0 .99-.84l.58-3.47a2 2 0 0 0-1.34-2.23z",key:"1wgbhj"}]]);var p=s(32192);let y=(0,m.A)("gamepad-2",[["line",{x1:"6",x2:"10",y1:"11",y2:"11",key:"1gktln"}],["line",{x1:"8",x2:"8",y1:"9",y2:"13",key:"qnk9ow"}],["line",{x1:"15",x2:"15.01",y1:"12",y2:"12",key:"krot7o"}],["line",{x1:"18",x2:"18.01",y1:"10",y2:"10",key:"1lcuu1"}],["path",{d:"M17.32 5H6.68a4 4 0 0 0-3.978 3.59c-.006.052-.01.101-.017.152C2.604 9.416 2 14.456 2 16a3 3 0 0 0 3 3c1 0 1.5-.5 2-1l1.414-1.414A2 2 0 0 1 9.828 16h4.344a2 2 0 0 1 1.414.586L17 18c.5.5 1 1 2 1a3 3 0 0 0 3-3c0-1.545-.604-6.584-.685-7.258-.007-.05-.011-.1-.017-.151A4 4 0 0 0 17.32 5z",key:"mfqc10"}]]),g=(0,m.A)("book",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]]),f=(0,m.A)("dumbbell",[["path",{d:"M17.596 12.768a2 2 0 1 0 2.829-2.829l-1.768-1.767a2 2 0 0 0 2.828-2.829l-2.828-2.828a2 2 0 0 0-2.829 2.828l-1.767-1.768a2 2 0 1 0-2.829 2.829z",key:"9m4mmf"}],["path",{d:"m2.5 21.5 1.4-1.4",key:"17g3f0"}],["path",{d:"m20.1 3.9 1.4-1.4",key:"1qn309"}],["path",{d:"M5.343 21.485a2 2 0 1 0 2.829-2.828l1.767 1.768a2 2 0 1 0 2.829-2.829l-6.364-6.364a2 2 0 1 0-2.829 2.829l1.768 1.767a2 2 0 0 0-2.828 2.829z",key:"1t2c92"}],["path",{d:"m9.6 14.4 4.8-4.8",key:"6umqxw"}]]),j=(0,m.A)("car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]]),v=(0,m.A)("baby",[["path",{d:"M10 16c.5.3 1.2.5 2 .5s1.5-.2 2-.5",key:"1u7htd"}],["path",{d:"M15 12h.01",key:"1k8ypt"}],["path",{d:"M19.38 6.813A9 9 0 0 1 20.8 10.2a2 2 0 0 1 0 3.6 9 9 0 0 1-17.6 0 2 2 0 0 1 0-3.6A9 9 0 0 1 12 3c2 0 3.5 1.1 3.5 2.5s-.9 2.5-2 2.5c-.8 0-1.5-.4-1.5-1",key:"11xh7x"}],["path",{d:"M9 12h.01",key:"157uk2"}]]),N=(0,m.A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]),w=(0,m.A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]);var k=s(70334);let b={electronics:x,fashion:u,home:p.A,gaming:y,books:g,sports:f,automotive:j,baby:v,art:N,default:w},A=["from-blue-500/20 to-blue-600/20","from-purple-500/20 to-purple-600/20","from-green-500/20 to-green-600/20","from-orange-500/20 to-orange-600/20","from-pink-500/20 to-pink-600/20","from-indigo-500/20 to-indigo-600/20","from-red-500/20 to-red-600/20","from-teal-500/20 to-teal-600/20"],M=i().memo(({categories:e,isLoading:a})=>{let s=e=>({electronics:1250,fashion:890,home:650,gaming:420,books:780,sports:340,automotive:290,baby:180})[e.toLowerCase()]||Math.floor(500*Math.random())+100,r=e=>b[e.toLowerCase()]||b.default;return a?(0,t.jsxs)("div",{className:"space-y-12",children:[(0,t.jsxs)("div",{className:"text-center space-y-4",children:[(0,t.jsx)("div",{className:"h-10 bg-muted rounded animate-pulse w-64 mx-auto"}),(0,t.jsx)("div",{className:"h-6 bg-muted rounded animate-pulse w-96 mx-auto"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:Array.from({length:8}).map((e,a)=>(0,t.jsxs)(o.Zp,{className:"overflow-hidden",children:[(0,t.jsx)("div",{className:"aspect-[4/3] bg-muted animate-pulse"}),(0,t.jsxs)(o.Wu,{className:"p-4 space-y-2",children:[(0,t.jsx)("div",{className:"h-6 bg-muted rounded animate-pulse w-full"}),(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-24"})]})]},a))})]}):0===e.length?(0,t.jsx)("div",{className:"text-center py-16",children:(0,t.jsxs)("div",{className:"max-w-md mx-auto space-y-4",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto",children:(0,t.jsx)(w,{className:"w-8 h-8 text-muted-foreground"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold",children:"No Categories Available"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"We're currently updating our categories. Check back soon!"}),(0,t.jsx)(d.$,{asChild:!0,children:(0,t.jsx)(c(),{href:"/shop",children:"Browse All Products"})})]})}):(0,t.jsxs)("div",{className:"space-y-12",children:[(0,t.jsxs)(h.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center space-y-4",children:[(0,t.jsx)("h2",{className:"text-3xl md:text-4xl font-bold",children:"Shop by Category"}),(0,t.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"Explore our diverse range of product categories, each carefully curated to meet your specific needs and preferences."})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:e.slice(0,8).map((e,a)=>{let i=r(e),l=A[a%A.length],d=s(e);return(0,t.jsx)(h.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1*a},viewport:{once:!0},children:(0,t.jsx)(c(),{href:`/shop?category=${encodeURIComponent(e)}`,children:(0,t.jsxs)(o.Zp,{className:"group hover:shadow-xl transition-all duration-300 cursor-pointer overflow-hidden",children:[(0,t.jsxs)("div",{className:`aspect-[4/3] bg-gradient-to-br ${l} relative overflow-hidden`,children:[(0,t.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center space-y-4",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300",children:(0,t.jsx)(i,{className:"w-8 h-8 text-foreground"})}),(0,t.jsxs)(n.E,{variant:"secondary",className:"bg-white/90 text-foreground hover:bg-white",children:[d,"+ Products"]})]})}),(0,t.jsx)("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300"}),(0,t.jsx)("div",{className:"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,t.jsx)("div",{className:"w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center",children:(0,t.jsx)(k.A,{className:"w-4 h-4 text-foreground"})})})]}),(0,t.jsx)(o.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg capitalize group-hover:text-primary transition-colors",children:e}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Discover amazing ",e.toLowerCase()," products"]})]})})]})})},e)})}),(0,t.jsxs)(h.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},viewport:{once:!0},className:"text-center",children:[(0,t.jsx)(d.$,{asChild:!0,size:"lg",variant:"outline",className:"text-lg px-8 py-6 h-auto",children:(0,t.jsxs)(c(),{href:"/shop",className:"flex items-center gap-2",children:["View All Categories",(0,t.jsx)(k.A,{className:"w-5 h-5"})]})}),e.length>8&&(0,t.jsxs)("p",{className:"text-sm text-muted-foreground mt-4",children:["Showing 8 of ",e.length," categories"]})]}),(0,t.jsx)(h.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"bg-muted/50 rounded-2xl p-8",children:(0,t.jsxs)("div",{className:"text-center space-y-6",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold",children:"Why Shop by Category?"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 text-sm",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center mx-auto",children:(0,t.jsx)(w,{className:"w-4 h-4 text-primary"})}),(0,t.jsx)("p",{className:"font-medium",children:"Organized Shopping"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Find exactly what you need quickly"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center mx-auto",children:(0,t.jsx)(k.A,{className:"w-4 h-4 text-primary"})}),(0,t.jsx)("p",{className:"font-medium",children:"Easy Navigation"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Browse products by your interests"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center mx-auto",children:(0,t.jsx)(x,{className:"w-4 h-4 text-primary"})}),(0,t.jsx)("p",{className:"font-medium",children:"Curated Selection"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Quality products in every category"})]})]})]})})]})})}};