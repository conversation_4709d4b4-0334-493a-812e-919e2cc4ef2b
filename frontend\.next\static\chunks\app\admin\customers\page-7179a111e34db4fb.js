(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1016],{285:(e,s,t)=>{"use strict";t.d(s,{$:()=>l});var r=t(5155);t(2115);var a=t(9708),i=t(2085),d=t(9434);let n=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:s,variant:t,size:i,asChild:l=!1,...c}=e,o=l?a.DX:"button";return(0,r.jsx)(o,{"data-slot":"button",className:(0,d.cn)(n({variant:t,size:i,className:s})),...c})}},1264:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},1497:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},2523:(e,s,t)=>{"use strict";t.d(s,{p:()=>i});var r=t(5155);t(2115);var a=t(9434);function i(e){let{className:s,type:t,...i}=e;return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...i})}},2657:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3169:(e,s,t)=>{"use strict";t.d(s,{default:()=>E});var r=t(5155),a=t(2115),i=t(6766),d=t(5654),n=t(285),l=t(2523),c=t(6695),o=t(6126),u=t(5365),m=t(8856),x=t(7580),h=t(9946);let f=(0,h.A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),v=(0,h.A)("user-x",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]);var p=t(6151),g=t(7924),j=t(6932),y=t(5339),N=t(1264),b=t(9420),w=t(9074),k=t(4516),A=t(2657),_=t(5623),C=t(1497),M=t(8543),S=t(4838);let z=e=>{let{customer:s,onView:t,onSendMessage:a}=e;return(0,r.jsx)(c.Zp,{children:(0,r.jsx)(c.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center",children:s.avatar?(0,r.jsx)(i.default,{src:s.avatar,alt:s.fullName,width:48,height:48,className:"rounded-full object-cover"}):(0,r.jsx)(x.A,{className:"h-6 w-6 text-primary"})}),(0,r.jsxs)("div",{className:"flex-1 space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("h3",{className:"font-semibold text-lg",children:s.fullName}),(0,r.jsx)(o.E,{variant:s.isVerified?"default":"secondary",children:s.isVerified?"Verified":"Unverified"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-muted-foreground",children:[(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(N.A,{className:"h-3 w-3 mr-1"}),s.email]}),s.phoneNumber&&(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(b.A,{className:"h-3 w-3 mr-1"}),s.phoneNumber]}),(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(w.A,{className:"h-3 w-3 mr-1"}),"Joined ",new Date(s.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})]})]}),s.address&&(0,r.jsxs)("p",{className:"text-sm text-muted-foreground flex items-center",children:[(0,r.jsx)(k.A,{className:"h-3 w-3 mr-1"}),s.address.city,", ",s.address.state]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(n.$,{variant:"outline",size:"sm",onClick:t,children:[(0,r.jsx)(A.A,{className:"h-4 w-4 mr-1"}),"View"]}),(0,r.jsxs)(S.rI,{children:[(0,r.jsx)(S.ty,{asChild:!0,children:(0,r.jsx)(n.$,{variant:"outline",size:"sm",children:(0,r.jsx)(_.A,{className:"h-4 w-4"})})}),(0,r.jsxs)(S.SQ,{align:"end",children:[(0,r.jsxs)(S._2,{onClick:a,children:[(0,r.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"Send Message"]}),(0,r.jsxs)(S._2,{onClick:t,children:[(0,r.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"View Details"]})]})]})]})]})})})},Z=e=>{let{customer:s,orders:t,loadingOrders:a,onBack:d,onSendMessage:l}=e,u=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),h=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),f=t.reduce((e,s)=>e+s.totalRevenue+s.codCharges,0);return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(n.$,{variant:"ghost",onClick:d,children:"← Back to Customers"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:s.fullName}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Customer Details"})]})]}),(0,r.jsxs)(n.$,{onClick:l,children:[(0,r.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"Send Message"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(c.Zp,{children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{children:"Personal Information"})}),(0,r.jsxs)(c.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center",children:s.avatar?(0,r.jsx)(i.default,{src:s.avatar,alt:s.fullName,width:64,height:64,className:"rounded-full object-cover"}):(0,r.jsx)(x.A,{className:"h-8 w-8 text-primary"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-xl",children:s.fullName}),(0,r.jsx)(o.E,{variant:s.isVerified?"default":"secondary",children:s.isVerified?"Verified Account":"Unverified Account"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Email"}),(0,r.jsx)("p",{className:"font-medium",children:s.email})]}),s.phoneNumber&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Phone"}),(0,r.jsx)("p",{className:"font-medium",children:s.phoneNumber})]}),s.dateOfBirth&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Date of Birth"}),(0,r.jsx)("p",{className:"font-medium",children:h(s.dateOfBirth)})]}),s.gender&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Gender"}),(0,r.jsx)("p",{className:"font-medium capitalize",children:s.gender.replace("-"," ")})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Member Since"}),(0,r.jsx)("p",{className:"font-medium",children:h(s.createdAt)})]})]})]})]}),(0,r.jsxs)(c.Zp,{children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{children:"Address Information"})}),(0,r.jsx)(c.Wu,{children:s.address?(0,r.jsxs)("div",{className:"space-y-3",children:[s.address.street&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Street Address"}),(0,r.jsx)("p",{className:"font-medium",children:s.address.street})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[s.address.city&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"City"}),(0,r.jsx)("p",{className:"font-medium",children:s.address.city})]}),s.address.state&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"State"}),(0,r.jsx)("p",{className:"font-medium",children:s.address.state})]}),s.address.zipCode&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"ZIP Code"}),(0,r.jsx)("p",{className:"font-medium",children:s.address.zipCode})]}),s.address.country&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Country"}),(0,r.jsx)("p",{className:"font-medium",children:s.address.country})]})]})]}):(0,r.jsx)("p",{className:"text-muted-foreground",children:"No address information available"})})]})]}),(0,r.jsxs)(c.Zp,{children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{children:"Order History"}),(0,r.jsxs)(c.BT,{children:[t.length," orders • Total spent: ",u(f)]})]}),(0,r.jsx)(c.Wu,{children:a?(0,r.jsx)("div",{className:"space-y-3",children:Array.from({length:3}).map((e,s)=>(0,r.jsx)(m.E,{className:"h-16 w-full"},s))}):0===t.length?(0,r.jsx)("p",{className:"text-muted-foreground text-center py-8",children:"No orders found for this customer."}):(0,r.jsx)("div",{className:"space-y-3",children:t.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border border-border rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"font-medium",children:["Order #",e._id.slice(-8)]}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[h(e.createdAt)," • ",e.orderItems.length," items"]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("p",{className:"font-medium",children:u(e.totalRevenue+e.codCharges)}),(0,r.jsx)(o.E,{variant:"delivered"===e.status?"default":"secondary",children:e.status})]})]},e._id))})})]})]})},E=()=>{let[e,s]=(0,a.useState)([]),[t,i]=(0,a.useState)(!0),[o,h]=(0,a.useState)(""),[N,b]=(0,a.useState)(""),[w,k]=(0,a.useState)(null),[A,_]=(0,a.useState)([]),[C,S]=(0,a.useState)(!1),E=async()=>{try{i(!0);let e=await d.ZJ.getAllUsers();e.success&&e.data?s(e.data.users):h(e.message||"Failed to load customers")}catch(r){var e,t;let s=(null==(t=r.response)||null==(e=t.data)?void 0:e.message)||"An error occurred while loading customers";h(s),M.oR.error(s)}finally{i(!1)}};(0,a.useEffect)(()=>{E()},[]);let V=async e=>{try{S(!0);let s=await d.ZJ.getUserOrders(e);s.success&&s.data?_(s.data):(_([]),M.oR.error(s.message||"Failed to load customer orders"))}catch(r){var s,t;_([]);let e=(null==(t=r.response)||null==(s=t.data)?void 0:s.message)||"An error occurred while loading customer orders";M.oR.error(e)}finally{S(!1)}},R=e=>{k(e),e._id&&V(e._id)},O=async e=>{let s=prompt("Enter your message to the customer:");if(s&&s.trim())try{let t=await d.ZJ.sendMessageToUser(e,s.trim());t.success?M.oR.success("Message sent successfully"):M.oR.error(t.message||"Failed to send message")}catch(s){var t,r;let e=(null==(r=s.response)||null==(t=r.data)?void 0:t.message)||"An error occurred while sending message";M.oR.error(e)}},F=e.filter(e=>{let s=N.toLowerCase();return e.fullName.toLowerCase().includes(s)||e.email.toLowerCase().includes(s)||e.phoneNumber&&e.phoneNumber.toLowerCase().includes(s)}),q={total:e.length,verified:e.filter(e=>e.isVerified).length,unverified:e.filter(e=>!e.isVerified).length,withOrders:e.filter(e=>e._id).length};return w?(0,r.jsx)(Z,{customer:w,orders:A,loadingOrders:C,onBack:()=>k(null),onSendMessage:()=>w._id&&O(w._id)}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Customer Management"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"View and manage customer accounts, orders, and support requests."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsx)(c.Zp,{children:(0,r.jsx)(c.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Customers"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:q.total})]}),(0,r.jsx)(x.A,{className:"h-8 w-8 text-muted-foreground"})]})})}),(0,r.jsx)(c.Zp,{children:(0,r.jsx)(c.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Verified Accounts"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-green-600",children:q.verified})]}),(0,r.jsx)(f,{className:"h-8 w-8 text-green-600"})]})})}),(0,r.jsx)(c.Zp,{children:(0,r.jsx)(c.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Unverified Accounts"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:q.unverified})]}),(0,r.jsx)(v,{className:"h-8 w-8 text-yellow-600"})]})})}),(0,r.jsx)(c.Zp,{children:(0,r.jsx)(c.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Active Customers"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:q.withOrders})]}),(0,r.jsx)(p.A,{className:"h-8 w-8 text-blue-600"})]})})})]}),(0,r.jsx)(c.Zp,{children:(0,r.jsx)(c.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(l.p,{placeholder:"Search customers by name, email, or phone...",value:N,onChange:e=>b(e.target.value),className:"pl-10"})]}),(0,r.jsx)("div",{className:"flex gap-2",children:(0,r.jsx)(n.$,{variant:"outline",size:"icon",children:(0,r.jsx)(j.A,{className:"h-4 w-4"})})})]})})}),o&&(0,r.jsxs)(u.Fc,{variant:"destructive",children:[(0,r.jsx)(y.A,{className:"h-4 w-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{children:"Error loading customers"}),(0,r.jsx)("p",{children:o})]})]}),t&&(0,r.jsx)("div",{className:"space-y-4",children:Array.from({length:5}).map((e,s)=>(0,r.jsx)(c.Zp,{children:(0,r.jsx)(c.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(m.E,{className:"h-4 w-32"}),(0,r.jsx)(m.E,{className:"h-4 w-48"}),(0,r.jsx)(m.E,{className:"h-4 w-24"})]}),(0,r.jsx)(m.E,{className:"h-8 w-20"})]})})},s))}),!t&&!o&&(0,r.jsx)(r.Fragment,{children:0===F.length?(0,r.jsx)(c.Zp,{children:(0,r.jsxs)(c.Wu,{className:"p-8 text-center",children:[(0,r.jsx)(x.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No customers found"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:N?"No customers match your search criteria.":"No customers have registered yet."})]})}):(0,r.jsx)("div",{className:"space-y-4",children:F.map(e=>(0,r.jsx)(z,{customer:e,onView:()=>R(e),onSendMessage:()=>e._id&&O(e._id)},e._id||e.email))})})]})}},3779:(e,s,t)=>{"use strict";t.d(s,{AuthProvider:()=>l,b:()=>n});var r=t(5155),a=t(2115),i=t(5654);let d=(0,a.createContext)(void 0),n=()=>{let e=(0,a.useContext)(d);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},l=e=>{let{children:s}=e,[t,n]=(0,a.useState)(null),[l,c]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(async()=>{if(localStorage.getItem("access_token"))try{let e=await i.ZJ.getAdmin();e.success&&e.data&&n(e.data)}catch(e){e.response&&(console.error("Error checking auth status:",e.response.data.message),localStorage.removeItem("access_token"))}c(!1)})()},[]);let o=async()=>{try{await i.ZJ.logout()}catch(e){console.error("Error during logout:",e)}finally{localStorage.removeItem("access_token"),n(null)}};return(0,r.jsx)(d.Provider,{value:{admin:t,isLoading:l,isAuthenticated:!!t,login:(e,s)=>{localStorage.setItem("access_token",s),n(e)},logout:o},children:s})}},4368:(e,s,t)=>{"use strict";t.d(s,{default:()=>c});var r=t(5155);t(2115);var a=t(3779),i=t(5365),d=t(5339),n=t(5525);let l=(e,s)=>"admin"===s?"admin"===e.role||"superAdmin"===e.role:"superAdmin"===s&&"superAdmin"===e.role,c=e=>{let{children:s,requiredRole:t,fallback:c}=e,{admin:o,isAuthenticated:u,isLoading:m}=(0,a.b)();return m?c||(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})}):u&&o?t&&!l(o,t)?c||(0,r.jsxs)(i.Fc,{variant:"destructive",children:[(0,r.jsx)(n.A,{className:"h-4 w-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{children:"Insufficient Permissions"}),(0,r.jsxs)("p",{children:["You need ",t," privileges to access this page. Your current role is: ",o.role]})]})]}):(0,r.jsx)(r.Fragment,{children:s}):c||(0,r.jsxs)(i.Fc,{variant:"destructive",children:[(0,r.jsx)(d.A,{className:"h-4 w-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{children:"Access Denied"}),(0,r.jsx)("p",{children:"You must be logged in as an admin to access this page."})]})]})}},4516:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4838:(e,s,t)=>{"use strict";t.d(s,{SQ:()=>l,_2:()=>c,mB:()=>o,rI:()=>d,ty:()=>n});var r=t(5155);t(2115);var a=t(9449),i=t(9434);function d(e){let{...s}=e;return(0,r.jsx)(a.bL,{"data-slot":"dropdown-menu",...s})}function n(e){let{...s}=e;return(0,r.jsx)(a.l9,{"data-slot":"dropdown-menu-trigger",...s})}function l(e){let{className:s,sideOffset:t=4,...d}=e;return(0,r.jsx)(a.ZL,{children:(0,r.jsx)(a.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",s),...d})})}function c(e){let{className:s,inset:t,variant:d="default",...n}=e;return(0,r.jsx)(a.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":d,className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...n})}function o(e){let{className:s,...t}=e;return(0,r.jsx)(a.wv,{"data-slot":"dropdown-menu-separator",className:(0,i.cn)("bg-border -mx-1 my-1 h-px",s),...t})}},5339:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5365:(e,s,t)=>{"use strict";t.d(s,{Fc:()=>l,TN:()=>c});var r=t(5155),a=t(2115),i=t(2085),d=t(9434);let n=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),l=a.forwardRef((e,s)=>{let{className:t,variant:a,...i}=e;return(0,r.jsx)("div",{ref:s,role:"alert",className:(0,d.cn)(n({variant:a}),t),...i})});l.displayName="Alert",a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("h5",{ref:s,className:(0,d.cn)("mb-1 font-medium leading-none tracking-tight",t),...a})}).displayName="AlertTitle";let c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,d.cn)("text-sm [&_p]:leading-relaxed",t),...a})});c.displayName="AlertDescription"},5525:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5623:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},6126:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var r=t(5155);t(2115);var a=t(9708),i=t(2085),d=t(9434);let n=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:t,asChild:i=!1,...l}=e,c=i?a.DX:"span";return(0,r.jsx)(c,{"data-slot":"badge",className:(0,d.cn)(n({variant:t}),s),...l})}},6151:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("shopping-bag",[["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}],["path",{d:"M3.103 6.034h17.794",key:"awc11p"}],["path",{d:"M3.4 5.467a2 2 0 0 0-.4 1.2V20a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6.667a2 2 0 0 0-.4-1.2l-2-2.667A2 2 0 0 0 17 2H7a2 2 0 0 0-1.6.8z",key:"o988cm"}]])},6654:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"useMergedRef",{enumerable:!0,get:function(){return a}});let r=t(2115);function a(e,s){let t=(0,r.useRef)(null),a=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=t.current;e&&(t.current=null,e());let s=a.current;s&&(a.current=null,s())}else e&&(t.current=i(e,r)),s&&(a.current=i(s,r))},[e,s])}function i(e,s){if("function"!=typeof e)return e.current=s,()=>{e.current=null};{let t=e(s);return"function"==typeof t?t:()=>e(null)}}("function"==typeof s.default||"object"==typeof s.default&&null!==s.default)&&void 0===s.default.__esModule&&(Object.defineProperty(s.default,"__esModule",{value:!0}),Object.assign(s.default,s),e.exports=s.default)},6695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>l,Wu:()=>c,ZB:()=>n,Zp:()=>i,aR:()=>d,wL:()=>o});var r=t(5155);t(2115);var a=t(9434);function i(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...t})}function d(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...t})}function n(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",s),...t})}function l(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",s),...t})}function c(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",s),...t})}function o(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",s),...t})}},6932:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},7580:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},7924:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8424:(e,s,t)=>{Promise.resolve().then(t.bind(t,4368)),Promise.resolve().then(t.bind(t,3169))},8856:(e,s,t)=>{"use strict";t.d(s,{E:()=>i});var r=t(5155),a=t(9434);function i(e){let{className:s,...t}=e;return(0,r.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",s),...t})}},9074:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9420:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,9078,8543,6766,3888,9449,7389,8441,1684,7358],()=>s(8424)),_N_E=e.O()}]);