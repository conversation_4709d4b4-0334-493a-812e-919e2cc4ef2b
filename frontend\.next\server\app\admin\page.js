(()=>{var e={};e.id=698,e.ids=[698],e.modules={1132:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l});var t=r(37413),a=r(65537);function l(){return(0,t.jsx)(a.default,{})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12525:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>o});var t=r(65239),a=r(48088),l=r(88170),i=r.n(l),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(s,d);let o={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1132)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,99111)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},16273:(e,s,r)=>{Promise.resolve().then(r.bind(r,65537))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23928:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},25541:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40228:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65537:(e,s,r)=>{"use strict";r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminDashboard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminDashboard.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80299:(e,s,r)=>{"use strict";r.d(s,{default:()=>A});var t=r(60687),a=r(43210),l=r(30474),i=r(58376),n=r(44493),d=r(85726),o=r(91821),c=r(25541),u=r(93613),m=r(41312),x=r(19080),h=r(28561),p=r(23928);let j=(0,r(62688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);var g=r(40228),v=r(93853);let b=({title:e,description:s,data:r,height:a=200})=>{let l=Math.max(...r.map(e=>e.value));return(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{children:e}),s&&(0,t.jsx)(n.BT,{children:s})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)("div",{className:"space-y-3",style:{height:a},children:r.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-20 text-sm text-muted-foreground truncate",children:e.label}),(0,t.jsxs)("div",{className:"flex-1 bg-muted rounded-full h-6 relative overflow-hidden",children:[(0,t.jsx)("div",{className:`h-full rounded-full transition-all duration-500 ${e.color||"bg-primary"}`,style:{width:`${e.value/l*100}%`}}),(0,t.jsx)("div",{className:"absolute inset-0 flex items-center justify-end pr-2",children:(0,t.jsx)("span",{className:"text-xs font-medium text-foreground",children:e.value.toLocaleString()})})]})]},s))})})]})},f=({title:e,description:s,data:r})=>{let a=r.reduce((e,s)=>e+s.value,0);return(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{children:e}),s&&(0,t.jsx)(n.BT,{children:s})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:r.map((e,s)=>{let r=a>0?e.value/a*100:0;return(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-foreground",children:e.label}),(0,t.jsxs)("span",{className:"text-muted-foreground",children:[e.value.toLocaleString()," (",r.toFixed(1),"%)"]})]}),(0,t.jsx)("div",{className:"w-full bg-muted rounded-full h-2",children:(0,t.jsx)("div",{className:`h-2 rounded-full transition-all duration-500 ${e.color||"bg-primary"}`,style:{width:`${r}%`}})})]},s)})})})]})},y=({title:e,description:s,data:r,height:a=200})=>{let l=Math.max(...r.map(e=>e.value)),i=Math.min(...r.map(e=>e.value)),d=l-i;return(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{children:e}),s&&(0,t.jsx)(n.BT,{children:s})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"relative",style:{height:a},children:[(0,t.jsxs)("svg",{width:"100%",height:"100%",viewBox:"0 0 400 200",className:"overflow-visible",children:[[0,1,2,3,4].map(e=>(0,t.jsx)("line",{x1:"0",y1:40*e,x2:"400",y2:40*e,stroke:"currentColor",strokeWidth:"0.5",className:"text-border"},e)),(0,t.jsx)("polyline",{fill:"none",stroke:"currentColor",strokeWidth:"2",className:"text-primary",points:r.map((e,s)=>{let t=s/(r.length-1)*400,a=d>0?200-(e.value-i)/d*200:100;return`${t},${a}`}).join(" ")}),r.map((e,s)=>{let a=s/(r.length-1)*400,l=d>0?200-(e.value-i)/d*200:100;return(0,t.jsx)("circle",{cx:a,cy:l,r:"4",fill:"currentColor",className:"text-primary"},s)})]}),(0,t.jsx)("div",{className:"absolute bottom-0 left-0 right-0 flex justify-between text-xs text-muted-foreground",children:r.map((e,s)=>(0,t.jsx)("span",{className:"truncate max-w-16",children:e.label},s))})]})})]})};var N=r(85814),w=r.n(N);let P=({title:e,value:s,description:r,icon:a,trend:l})=>(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(n.ZB,{className:"text-sm font-medium",children:e}),(0,t.jsx)(a,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(n.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:s}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:r}),l&&(0,t.jsxs)("div",{className:`flex items-center text-xs mt-1 ${l.isPositive?"text-green-600":"text-red-600"}`,children:[(0,t.jsx)(c.A,{className:"h-3 w-3 mr-1"}),l.isPositive?"+":"",l.value,"% from last month"]})]})]}),A=()=>{let[e,s]=(0,a.useState)(null),[r,c]=(0,a.useState)(!0),[N,A]=(0,a.useState)("");if((0,a.useEffect)(()=>{(async()=>{try{c(!0);let e=await i.ZJ.getDashboard();e.success&&e.data?s(e.data):A(e.message||"Failed to load dashboard data")}catch(s){let e=s.response?.data?.message||"An error occurred while loading dashboard";A(e),v.oR.error(e)}finally{c(!1)}})()},[]),r)return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(d.E,{className:"h-8 w-64 mb-2"}),(0,t.jsx)(d.E,{className:"h-4 w-96"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:Array.from({length:4}).map((e,s)=>(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(d.E,{className:"h-4 w-24"})}),(0,t.jsxs)(n.Wu,{children:[(0,t.jsx)(d.E,{className:"h-8 w-16 mb-2"}),(0,t.jsx)(d.E,{className:"h-3 w-32"})]})]},s))}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(d.E,{className:"h-6 w-48"})}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)(d.E,{className:"h-64 w-full"})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(d.E,{className:"h-6 w-48"})}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)(d.E,{className:"h-64 w-full"})})]})]})]});if(N)return(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(u.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{children:"Error loading dashboard"}),(0,t.jsx)("p",{children:N})]})]});if(!e)return(0,t.jsxs)(o.Fc,{children:[(0,t.jsx)(u.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{children:"No data available"}),(0,t.jsx)("p",{children:"Dashboard statistics are not available at the moment."})]})]});let k=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e);return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Dashboard Overview"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Welcome back! Here's what's happening with your store today."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsx)(P,{title:"Total Users",value:e.totalUsers.toLocaleString(),description:"Registered customers",icon:m.A}),(0,t.jsx)(P,{title:"Total Products",value:e.totalProducts.toLocaleString(),description:"Products in inventory",icon:x.A}),(0,t.jsx)(P,{title:"Total Orders",value:e.totalOrders.toLocaleString(),description:"Orders processed",icon:h.A}),(0,t.jsx)(P,{title:"Total Revenue",value:k(e.totalSales),description:"Total sales revenue",icon:p.A})]}),e.topSellingProduct&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{children:"Top Selling Product"}),(0,t.jsx)(n.BT,{children:"Best performing product this period"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[e.topSellingProduct.images?.[0]?.url&&(0,t.jsx)(l.default,{src:e.topSellingProduct.images[0].url,alt:e.topSellingProduct.title,width:64,height:64,className:"object-cover rounded-lg"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg",children:e.topSellingProduct.title}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Quantity Sold"}),(0,t.jsx)("p",{className:"font-medium",children:e.topSellingProduct.totalQuantity})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Revenue Generated"}),(0,t.jsx)("p",{className:"font-medium",children:k(e.topSellingProduct.totalRevenue)})]})]})]})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsx)(y,{title:"Sales Trend",description:"Sales performance over the last 7 days",data:[{label:"Mon",value:1200},{label:"Tue",value:1800},{label:"Wed",value:1600},{label:"Thu",value:2200},{label:"Fri",value:2800},{label:"Sat",value:3200},{label:"Sun",value:2400}]}),(0,t.jsx)(f,{title:"Order Status Distribution",description:"Current order status breakdown",data:[{label:"Delivered",value:.6*e.totalOrders,color:"bg-green-500"},{label:"Shipped",value:.2*e.totalOrders,color:"bg-blue-500"},{label:"Processing",value:.15*e.totalOrders,color:"bg-yellow-500"},{label:"Pending",value:.05*e.totalOrders,color:"bg-red-500"}]})]}),(0,t.jsx)(b,{title:"Category Performance",description:"Sales by product category",data:[{label:"Electronics",value:45e3,color:"bg-blue-500"},{label:"Clothing",value:32e3,color:"bg-green-500"},{label:"Home & Garden",value:28e3,color:"bg-purple-500"},{label:"Sports",value:22e3,color:"bg-orange-500"},{label:"Books",value:15e3,color:"bg-pink-500"}]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center",children:[(0,t.jsx)(j,{className:"h-5 w-5 mr-2"}),"Recent Activity"]}),(0,t.jsx)(n.BT,{children:"Latest system activities and updates"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:[{action:"New order received",time:"2 minutes ago",type:"order"},{action:"Product inventory updated",time:"15 minutes ago",type:"product"},{action:"Customer support ticket resolved",time:"1 hour ago",type:"support"},{action:"New user registration",time:"2 hours ago",type:"user"},{action:"Payment processed successfully",time:"3 hours ago",type:"payment"}].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-lg bg-muted/50",children:[(0,t.jsx)("div",{className:`w-2 h-2 rounded-full ${"order"===e.type?"bg-green-500":"product"===e.type?"bg-blue-500":"support"===e.type?"bg-yellow-500":"user"===e.type?"bg-purple-500":"bg-orange-500"}`}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"text-sm font-medium",children:e.action}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground flex items-center",children:[(0,t.jsx)(g.A,{className:"h-3 w-3 mr-1"}),e.time]})]})]},s))})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{children:"Quick Actions"}),(0,t.jsx)(n.BT,{children:"Common administrative tasks"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsx)(w(),{href:"/admin/products",children:(0,t.jsxs)("div",{className:"p-4 border border-border rounded-lg hover:bg-accent cursor-pointer transition-colors",children:[(0,t.jsx)(x.A,{className:"h-8 w-8 text-primary mb-2"}),(0,t.jsx)("h3",{className:"font-medium",children:"Manage Products"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Manage inventory"})]})}),(0,t.jsx)(w(),{href:"/admin/orders",children:(0,t.jsxs)("div",{className:"p-4 border border-border rounded-lg hover:bg-accent cursor-pointer transition-colors",children:[(0,t.jsx)(h.A,{className:"h-8 w-8 text-primary mb-2"}),(0,t.jsx)("h3",{className:"font-medium",children:"Manage Orders"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Order management"})]})}),(0,t.jsx)(w(),{href:"/admin/customers",children:(0,t.jsxs)("div",{className:"p-4 border border-border rounded-lg hover:bg-accent cursor-pointer transition-colors",children:[(0,t.jsx)(m.A,{className:"h-8 w-8 text-primary mb-2"}),(0,t.jsx)("h3",{className:"font-medium",children:"Manage Customers"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Customer management"})]})})]})})]})]})}},81425:(e,s,r)=>{Promise.resolve().then(r.bind(r,80299))},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,162,658,598,367,10],()=>r(12525));module.exports=t})();