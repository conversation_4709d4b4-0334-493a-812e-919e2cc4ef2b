(()=>{var e={};e.id=0,e.ids=[0],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,s,t)=>{let{createProxy:r}=t(39844);e.exports=r("C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\node_modules\\next\\dist\\client\\app-dir\\link.js")},5748:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11614:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\components\\\\product\\\\ProductDetailsSkeleton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\product\\ProductDetailsSkeleton.tsx","default")},12412:e=>{"use strict";e.exports=require("assert")},14952:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},15079:(e,s,t)=>{"use strict";t.d(s,{bq:()=>u,eb:()=>f,gC:()=>x,l6:()=>c,yv:()=>m});var r=t(60687),a=t(43210),l=t(25911),i=t(78272),n=t(3589),o=t(13964),d=t(4780);let c=l.bL;l.YJ;let m=l.WT,u=a.forwardRef(({className:e,children:s,...t},a)=>(0,r.jsxs)(l.l9,{ref:a,className:(0,d.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[s,(0,r.jsx)(l.In,{asChild:!0,children:(0,r.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]}));u.displayName=l.l9.displayName;let p=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(l.PP,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,r.jsx)(n.A,{className:"h-4 w-4"})}));p.displayName=l.PP.displayName;let h=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(l.wn,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,r.jsx)(i.A,{className:"h-4 w-4"})}));h.displayName=l.wn.displayName;let x=a.forwardRef(({className:e,children:s,position:t="popper",...a},i)=>(0,r.jsx)(l.ZL,{children:(0,r.jsxs)(l.UC,{ref:i,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...a,children:[(0,r.jsx)(p,{}),(0,r.jsx)(l.LM,{className:(0,d.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,r.jsx)(h,{})]})}));x.displayName=l.UC.displayName,a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(l.JU,{ref:t,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",e),...s})).displayName=l.JU.displayName;let f=a.forwardRef(({className:e,children:s,...t},a)=>(0,r.jsxs)(l.q7,{ref:a,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[(0,r.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(l.VF,{children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})}),(0,r.jsx)(l.p4,{children:s})]}));f.displayName=l.q7.displayName,a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(l.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=l.wv.displayName},18137:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(37413),a=t(11614);function l(){return(0,r.jsx)(a.default,{})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29789:(e,s,t)=>{Promise.resolve().then(t.bind(t,84677))},30439:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\app\\\\product\\\\[id]\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\product\\[id]\\error.tsx","default")},33086:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,4536,23))},33873:e=>{"use strict";e.exports=require("path")},44493:(e,s,t)=>{"use strict";t.d(s,{BT:()=>o,Wu:()=>d,ZB:()=>n,Zp:()=>l,aR:()=>i,wL:()=>c});var r=t(60687);t(43210);var a=t(4780);function l({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...s})}function i({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...s})}function n({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...s})}function o({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...s})}function d({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...s})}function c({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...s})}},45589:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=t(65239),a=t(48088),l=t(88170),i=t.n(l),n=t(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let d={children:["",{children:["product",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,85948)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\product\\[id]\\page.tsx"]}]},{error:[()=>Promise.resolve().then(t.bind(t,30439)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\product\\[id]\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,18137)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\product\\[id]\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,75421)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\product\\[id]\\not-found.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\product\\[id]\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/product/[id]/page",pathname:"/product/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},45648:(e,s,t)=>{"use strict";t.d(s,{default:()=>l});var r=t(60687);t(43210);var a=t(44493);let l=()=>(0,r.jsx)("div",{className:"min-h-screen bg-background",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-12"}),(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-1"}),(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-12"}),(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-1"}),(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-20"}),(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-1"}),(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-32"})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 mb-16",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"aspect-square bg-muted rounded-lg animate-pulse"}),(0,r.jsx)("div",{className:"flex gap-2",children:Array.from({length:4}).map((e,s)=>(0,r.jsx)("div",{className:"w-20 h-20 bg-muted rounded-md animate-pulse"},s))})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-24"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"h-8 bg-muted rounded animate-pulse w-full"}),(0,r.jsx)("div",{className:"h-8 bg-muted rounded animate-pulse w-3/4"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("div",{className:"flex gap-1",children:Array.from({length:5}).map((e,s)=>(0,r.jsx)("div",{className:"w-5 h-5 bg-muted rounded animate-pulse"},s))}),(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-20"})]}),(0,r.jsx)("div",{className:"h-10 bg-muted rounded animate-pulse w-32"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-full"}),(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-full"}),(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-2/3"})]}),(0,r.jsx)("div",{className:"h-6 bg-muted rounded animate-pulse w-24"}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("div",{className:"h-10 bg-muted rounded animate-pulse w-24"}),(0,r.jsx)("div",{className:"h-10 bg-muted rounded animate-pulse w-40"})]}),(0,r.jsx)("div",{className:"flex gap-2",children:Array.from({length:4}).map((e,s)=>(0,r.jsx)("div",{className:"w-10 h-10 bg-muted rounded animate-pulse"},s))})]})]}),(0,r.jsx)("div",{className:"mb-16",children:(0,r.jsx)(a.Zp,{children:(0,r.jsxs)(a.Wu,{className:"p-6",children:[(0,r.jsx)("div",{className:"flex gap-6 mb-6",children:Array.from({length:3}).map((e,s)=>(0,r.jsx)("div",{className:"h-6 bg-muted rounded animate-pulse w-20"},s))}),(0,r.jsx)("div",{className:"space-y-4",children:Array.from({length:5}).map((e,s)=>(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-full"},s))})]})})}),(0,r.jsxs)("div",{className:"mb-16",children:[(0,r.jsx)("div",{className:"h-8 bg-muted rounded animate-pulse w-48 mb-6"}),(0,r.jsx)("div",{className:"space-y-4",children:Array.from({length:3}).map((e,s)=>(0,r.jsx)(a.Zp,{children:(0,r.jsx)(a.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-start gap-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-muted rounded-full animate-pulse"}),(0,r.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-32"}),(0,r.jsx)("div",{className:"flex gap-1",children:Array.from({length:5}).map((e,s)=>(0,r.jsx)("div",{className:"w-4 h-4 bg-muted rounded animate-pulse"},s))}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-full"}),(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-3/4"})]})]})]})})},s))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-8 bg-muted rounded animate-pulse w-48 mb-6"}),(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:Array.from({length:4}).map((e,s)=>(0,r.jsxs)(a.Zp,{children:[(0,r.jsx)("div",{className:"aspect-square bg-muted animate-pulse"}),(0,r.jsxs)(a.Wu,{className:"p-4 space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-full"}),(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-3/4"}),(0,r.jsx)("div",{className:"h-6 bg-muted rounded animate-pulse w-20"})]})]},s))})]})]})})},47033:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},54300:(e,s,t)=>{"use strict";t.d(s,{J:()=>o});var r=t(60687),a=t(43210),l=t(14163),i=a.forwardRef((e,s)=>(0,r.jsx)(l.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));i.displayName="Label";var n=t(4780);function o({className:e,...s}){return(0,r.jsx)(i,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...s})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58887:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},58964:(e,s,t)=>{Promise.resolve().then(t.bind(t,45648))},59645:(e,s,t)=>{Promise.resolve().then(t.bind(t,30439))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},66981:(e,s,t)=>{"use strict";t.d(s,{default:()=>m});var r=t(60687),a=t(43210),l=t(29523),i=t(44493),n=t(43649),o=t(78122),d=t(32192);class c extends a.Component{constructor(e){super(e),this.handleRetry=()=>{this.setState({hasError:!1,error:void 0})},this.handleGoHome=()=>{window.location.href="/"},this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,s){console.error("Shop Error Boundary caught an error:",e,s)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,r.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center p-4",children:(0,r.jsx)(i.Zp,{className:"w-full max-w-lg",children:(0,r.jsxs)(i.Wu,{className:"flex flex-col items-center justify-center py-12 text-center space-y-6",children:[(0,r.jsx)("div",{className:"w-20 h-20 text-destructive",children:(0,r.jsx)(n.A,{className:"w-full h-full"})}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-foreground",children:"Something went wrong"}),(0,r.jsx)("p",{className:"text-muted-foreground max-w-md",children:"We encountered an unexpected error while loading the shop. This might be a temporary issue."}),!1]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,r.jsxs)(l.$,{onClick:this.handleRetry,className:"flex items-center gap-2",size:"lg",children:[(0,r.jsx)(o.A,{className:"w-4 h-4"}),"Try Again"]}),(0,r.jsxs)(l.$,{onClick:this.handleGoHome,variant:"outline",className:"flex items-center gap-2",size:"lg",children:[(0,r.jsx)(d.A,{className:"w-4 h-4"}),"Go Home"]})]})]})})}):this.props.children}}let m=c},67760:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},70334:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73254:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,85814,23))},74075:e=>{"use strict";e.exports=require("zlib")},75421:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>e_});var r=t(37413),a=t(4536),l=t.n(a),i=t(61120);function n(e,s){if("function"==typeof e)return e(s);null!=e&&(e.current=s)}var o=function(e){let s=function(e){let s=i.forwardRef((e,s)=>{let{children:t,...r}=e;if(i.isValidElement(t)){var a;let e,l,o=(a=t,(l=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(l=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),d=function(e,s){let t={...s};for(let r in s){let a=e[r],l=s[r];/^on[A-Z]/.test(r)?a&&l?t[r]=(...e)=>{let s=l(...e);return a(...e),s}:a&&(t[r]=a):"style"===r?t[r]={...a,...l}:"className"===r&&(t[r]=[a,l].filter(Boolean).join(" "))}return{...e,...t}}(r,t.props);return t.type!==i.Fragment&&(d.ref=s?function(...e){return s=>{let t=!1,r=e.map(e=>{let r=n(e,s);return t||"function"!=typeof r||(t=!0),r});if(t)return()=>{for(let s=0;s<r.length;s++){let t=r[s];"function"==typeof t?t():n(e[s],null)}}}}(s,o):o),i.cloneElement(t,d)}return i.Children.count(t)>1?i.Children.only(null):null});return s.displayName=`${e}.SlotClone`,s}(e),t=i.forwardRef((e,t)=>{let{children:a,...l}=e,n=i.Children.toArray(a),o=n.find(c);if(o){let e=o.props.children,a=n.map(s=>s!==o?s:i.Children.count(e)>1?i.Children.only(null):i.isValidElement(e)?e.props.children:null);return(0,r.jsx)(s,{...l,ref:t,children:i.isValidElement(e)?i.cloneElement(e,void 0,a):null})}return(0,r.jsx)(s,{...l,ref:t,children:a})});return t.displayName=`${e}.Slot`,t}("Slot"),d=Symbol("radix.slottable");function c(e){return i.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}function m(){for(var e,s,t=0,r="",a=arguments.length;t<a;t++)(e=arguments[t])&&(s=function e(s){var t,r,a="";if("string"==typeof s||"number"==typeof s)a+=s;else if("object"==typeof s)if(Array.isArray(s)){var l=s.length;for(t=0;t<l;t++)s[t]&&(r=e(s[t]))&&(a&&(a+=" "),a+=r)}else for(r in s)s[r]&&(a&&(a+=" "),a+=r);return a}(e))&&(r&&(r+=" "),r+=s);return r}let u=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,p=e=>{let s=g(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),h(t,s)||f(e)},getConflictingClassGroupIds:(e,s)=>{let a=t[e]||[];return s&&r[e]?[...a,...r[e]]:a}}},h=(e,s)=>{if(0===e.length)return s.classGroupId;let t=e[0],r=s.nextPart.get(t),a=r?h(e.slice(1),r):void 0;if(a)return a;if(0===s.validators.length)return;let l=e.join("-");return s.validators.find(({validator:e})=>e(l))?.classGroupId},x=/^\[(.+)\]$/,f=e=>{if(x.test(e)){let s=x.exec(e)[1],t=s?.substring(0,s.indexOf(":"));if(t)return"arbitrary.."+t}},g=e=>{let{theme:s,classGroups:t}=e,r={nextPart:new Map,validators:[]};for(let e in t)b(t[e],r,e,s);return r},b=(e,s,t,r)=>{e.forEach(e=>{if("string"==typeof e){(""===e?s:v(s,e)).classGroupId=t;return}if("function"==typeof e)return j(e)?void b(e(r),s,t,r):void s.validators.push({validator:e,classGroupId:t});Object.entries(e).forEach(([e,a])=>{b(a,v(s,e),t,r)})})},v=(e,s)=>{let t=e;return s.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},j=e=>e.isThemeGetter,w=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let s=0,t=new Map,r=new Map,a=(a,l)=>{t.set(a,l),++s>e&&(s=0,r=t,t=new Map)};return{get(e){let s=t.get(e);return void 0!==s?s:void 0!==(s=r.get(e))?(a(e,s),s):void 0},set(e,s){t.has(e)?t.set(e,s):a(e,s)}}},y=e=>{let{prefix:s,experimentalParseClassName:t}=e,r=e=>{let s,t=[],r=0,a=0,l=0;for(let i=0;i<e.length;i++){let n=e[i];if(0===r&&0===a){if(":"===n){t.push(e.slice(l,i)),l=i+1;continue}if("/"===n){s=i;continue}}"["===n?r++:"]"===n?r--:"("===n?a++:")"===n&&a--}let i=0===t.length?e:e.substring(l),n=N(i);return{modifiers:t,hasImportantModifier:n!==i,baseClassName:n,maybePostfixModifierPosition:s&&s>l?s-l:void 0}};if(s){let e=s+":",t=r;r=s=>s.startsWith(e)?t(s.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:s,maybePostfixModifierPosition:void 0}}if(t){let e=r;r=s=>t({className:s,parseClassName:e})}return r},N=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,k=e=>{let s=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let t=[],r=[];return e.forEach(e=>{"["===e[0]||s[e]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t}},A=e=>({cache:w(e.cacheSize),parseClassName:y(e),sortModifiers:k(e),...p(e)}),C=/\s+/,z=(e,s)=>{let{parseClassName:t,getClassGroupId:r,getConflictingClassGroupIds:a,sortModifiers:l}=s,i=[],n=e.trim().split(C),o="";for(let e=n.length-1;e>=0;e-=1){let s=n[e],{isExternal:d,modifiers:c,hasImportantModifier:m,baseClassName:u,maybePostfixModifierPosition:p}=t(s);if(d){o=s+(o.length>0?" "+o:o);continue}let h=!!p,x=r(h?u.substring(0,p):u);if(!x){if(!h||!(x=r(u))){o=s+(o.length>0?" "+o:o);continue}h=!1}let f=l(c).join(":"),g=m?f+"!":f,b=g+x;if(i.includes(b))continue;i.push(b);let v=a(x,h);for(let e=0;e<v.length;++e){let s=v[e];i.push(g+s)}o=s+(o.length>0?" "+o:o)}return o};function R(){let e,s,t=0,r="";for(;t<arguments.length;)(e=arguments[t++])&&(s=P(e))&&(r&&(r+=" "),r+=s);return r}let P=e=>{let s;if("string"==typeof e)return e;let t="";for(let r=0;r<e.length;r++)e[r]&&(s=P(e[r]))&&(t&&(t+=" "),t+=s);return t},S=e=>{let s=s=>s[e]||[];return s.isThemeGetter=!0,s},$=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,M=/^\((?:(\w[\w-]*):)?(.+)\)$/i,E=/^\d+\/\d+$/,L=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,q=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,_=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,H=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,I=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,W=e=>E.test(e),G=e=>!!e&&!Number.isNaN(Number(e)),U=e=>!!e&&Number.isInteger(Number(e)),D=e=>e.endsWith("%")&&G(e.slice(0,-1)),Z=e=>L.test(e),B=()=>!0,F=e=>q.test(e)&&!_.test(e),V=()=>!1,O=e=>H.test(e),T=e=>I.test(e),J=e=>!K(e)&&!er(e),X=e=>ec(e,eh,V),K=e=>$.test(e),Q=e=>ec(e,ex,F),Y=e=>ec(e,ef,G),ee=e=>ec(e,eu,V),es=e=>ec(e,ep,T),et=e=>ec(e,eb,O),er=e=>M.test(e),ea=e=>em(e,ex),el=e=>em(e,eg),ei=e=>em(e,eu),en=e=>em(e,eh),eo=e=>em(e,ep),ed=e=>em(e,eb,!0),ec=(e,s,t)=>{let r=$.exec(e);return!!r&&(r[1]?s(r[1]):t(r[2]))},em=(e,s,t=!1)=>{let r=M.exec(e);return!!r&&(r[1]?s(r[1]):t)},eu=e=>"position"===e||"percentage"===e,ep=e=>"image"===e||"url"===e,eh=e=>"length"===e||"size"===e||"bg-size"===e,ex=e=>"length"===e,ef=e=>"number"===e,eg=e=>"family-name"===e,eb=e=>"shadow"===e;Symbol.toStringTag;let ev=function(e,...s){let t,r,a,l=function(n){return r=(t=A(s.reduce((e,s)=>s(e),e()))).cache.get,a=t.cache.set,l=i,i(n)};function i(e){let s=r(e);if(s)return s;let l=z(e,t);return a(e,l),l}return function(){return l(R.apply(null,arguments))}}(()=>{let e=S("color"),s=S("font"),t=S("text"),r=S("font-weight"),a=S("tracking"),l=S("leading"),i=S("breakpoint"),n=S("container"),o=S("spacing"),d=S("radius"),c=S("shadow"),m=S("inset-shadow"),u=S("text-shadow"),p=S("drop-shadow"),h=S("blur"),x=S("perspective"),f=S("aspect"),g=S("ease"),b=S("animate"),v=()=>["auto","avoid","all","avoid-page","page","left","right","column"],j=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],w=()=>[...j(),er,K],y=()=>["auto","hidden","clip","visible","scroll"],N=()=>["auto","contain","none"],k=()=>[er,K,o],A=()=>[W,"full","auto",...k()],C=()=>[U,"none","subgrid",er,K],z=()=>["auto",{span:["full",U,er,K]},U,er,K],R=()=>[U,"auto",er,K],P=()=>["auto","min","max","fr",er,K],$=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],M=()=>["start","end","center","stretch","center-safe","end-safe"],E=()=>["auto",...k()],L=()=>[W,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...k()],q=()=>[e,er,K],_=()=>[...j(),ei,ee,{position:[er,K]}],H=()=>["no-repeat",{repeat:["","x","y","space","round"]}],I=()=>["auto","cover","contain",en,X,{size:[er,K]}],F=()=>[D,ea,Q],V=()=>["","none","full",d,er,K],O=()=>["",G,ea,Q],T=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],em=()=>[G,D,ei,ee],eu=()=>["","none",h,er,K],ep=()=>["none",G,er,K],eh=()=>["none",G,er,K],ex=()=>[G,er,K],ef=()=>[W,"full",...k()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Z],breakpoint:[Z],color:[B],container:[Z],"drop-shadow":[Z],ease:["in","out","in-out"],font:[J],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Z],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Z],shadow:[Z],spacing:["px",G],text:[Z],"text-shadow":[Z],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",W,K,er,f]}],container:["container"],columns:[{columns:[G,K,er,n]}],"break-after":[{"break-after":v()}],"break-before":[{"break-before":v()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:w()}],overflow:[{overflow:y()}],"overflow-x":[{"overflow-x":y()}],"overflow-y":[{"overflow-y":y()}],overscroll:[{overscroll:N()}],"overscroll-x":[{"overscroll-x":N()}],"overscroll-y":[{"overscroll-y":N()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:A()}],"inset-x":[{"inset-x":A()}],"inset-y":[{"inset-y":A()}],start:[{start:A()}],end:[{end:A()}],top:[{top:A()}],right:[{right:A()}],bottom:[{bottom:A()}],left:[{left:A()}],visibility:["visible","invisible","collapse"],z:[{z:[U,"auto",er,K]}],basis:[{basis:[W,"full","auto",n,...k()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[G,W,"auto","initial","none",K]}],grow:[{grow:["",G,er,K]}],shrink:[{shrink:["",G,er,K]}],order:[{order:[U,"first","last","none",er,K]}],"grid-cols":[{"grid-cols":C()}],"col-start-end":[{col:z()}],"col-start":[{"col-start":R()}],"col-end":[{"col-end":R()}],"grid-rows":[{"grid-rows":C()}],"row-start-end":[{row:z()}],"row-start":[{"row-start":R()}],"row-end":[{"row-end":R()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":P()}],"auto-rows":[{"auto-rows":P()}],gap:[{gap:k()}],"gap-x":[{"gap-x":k()}],"gap-y":[{"gap-y":k()}],"justify-content":[{justify:[...$(),"normal"]}],"justify-items":[{"justify-items":[...M(),"normal"]}],"justify-self":[{"justify-self":["auto",...M()]}],"align-content":[{content:["normal",...$()]}],"align-items":[{items:[...M(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...M(),{baseline:["","last"]}]}],"place-content":[{"place-content":$()}],"place-items":[{"place-items":[...M(),"baseline"]}],"place-self":[{"place-self":["auto",...M()]}],p:[{p:k()}],px:[{px:k()}],py:[{py:k()}],ps:[{ps:k()}],pe:[{pe:k()}],pt:[{pt:k()}],pr:[{pr:k()}],pb:[{pb:k()}],pl:[{pl:k()}],m:[{m:E()}],mx:[{mx:E()}],my:[{my:E()}],ms:[{ms:E()}],me:[{me:E()}],mt:[{mt:E()}],mr:[{mr:E()}],mb:[{mb:E()}],ml:[{ml:E()}],"space-x":[{"space-x":k()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":k()}],"space-y-reverse":["space-y-reverse"],size:[{size:L()}],w:[{w:[n,"screen",...L()]}],"min-w":[{"min-w":[n,"screen","none",...L()]}],"max-w":[{"max-w":[n,"screen","none","prose",{screen:[i]},...L()]}],h:[{h:["screen","lh",...L()]}],"min-h":[{"min-h":["screen","lh","none",...L()]}],"max-h":[{"max-h":["screen","lh",...L()]}],"font-size":[{text:["base",t,ea,Q]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,er,Y]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",D,K]}],"font-family":[{font:[el,K,s]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,er,K]}],"line-clamp":[{"line-clamp":[G,"none",er,Y]}],leading:[{leading:[l,...k()]}],"list-image":[{"list-image":["none",er,K]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",er,K]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:q()}],"text-color":[{text:q()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...T(),"wavy"]}],"text-decoration-thickness":[{decoration:[G,"from-font","auto",er,Q]}],"text-decoration-color":[{decoration:q()}],"underline-offset":[{"underline-offset":[G,"auto",er,K]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:k()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",er,K]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",er,K]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:_()}],"bg-repeat":[{bg:H()}],"bg-size":[{bg:I()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},U,er,K],radial:["",er,K],conic:[U,er,K]},eo,es]}],"bg-color":[{bg:q()}],"gradient-from-pos":[{from:F()}],"gradient-via-pos":[{via:F()}],"gradient-to-pos":[{to:F()}],"gradient-from":[{from:q()}],"gradient-via":[{via:q()}],"gradient-to":[{to:q()}],rounded:[{rounded:V()}],"rounded-s":[{"rounded-s":V()}],"rounded-e":[{"rounded-e":V()}],"rounded-t":[{"rounded-t":V()}],"rounded-r":[{"rounded-r":V()}],"rounded-b":[{"rounded-b":V()}],"rounded-l":[{"rounded-l":V()}],"rounded-ss":[{"rounded-ss":V()}],"rounded-se":[{"rounded-se":V()}],"rounded-ee":[{"rounded-ee":V()}],"rounded-es":[{"rounded-es":V()}],"rounded-tl":[{"rounded-tl":V()}],"rounded-tr":[{"rounded-tr":V()}],"rounded-br":[{"rounded-br":V()}],"rounded-bl":[{"rounded-bl":V()}],"border-w":[{border:O()}],"border-w-x":[{"border-x":O()}],"border-w-y":[{"border-y":O()}],"border-w-s":[{"border-s":O()}],"border-w-e":[{"border-e":O()}],"border-w-t":[{"border-t":O()}],"border-w-r":[{"border-r":O()}],"border-w-b":[{"border-b":O()}],"border-w-l":[{"border-l":O()}],"divide-x":[{"divide-x":O()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":O()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...T(),"hidden","none"]}],"divide-style":[{divide:[...T(),"hidden","none"]}],"border-color":[{border:q()}],"border-color-x":[{"border-x":q()}],"border-color-y":[{"border-y":q()}],"border-color-s":[{"border-s":q()}],"border-color-e":[{"border-e":q()}],"border-color-t":[{"border-t":q()}],"border-color-r":[{"border-r":q()}],"border-color-b":[{"border-b":q()}],"border-color-l":[{"border-l":q()}],"divide-color":[{divide:q()}],"outline-style":[{outline:[...T(),"none","hidden"]}],"outline-offset":[{"outline-offset":[G,er,K]}],"outline-w":[{outline:["",G,ea,Q]}],"outline-color":[{outline:q()}],shadow:[{shadow:["","none",c,ed,et]}],"shadow-color":[{shadow:q()}],"inset-shadow":[{"inset-shadow":["none",m,ed,et]}],"inset-shadow-color":[{"inset-shadow":q()}],"ring-w":[{ring:O()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:q()}],"ring-offset-w":[{"ring-offset":[G,Q]}],"ring-offset-color":[{"ring-offset":q()}],"inset-ring-w":[{"inset-ring":O()}],"inset-ring-color":[{"inset-ring":q()}],"text-shadow":[{"text-shadow":["none",u,ed,et]}],"text-shadow-color":[{"text-shadow":q()}],opacity:[{opacity:[G,er,K]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[G]}],"mask-image-linear-from-pos":[{"mask-linear-from":em()}],"mask-image-linear-to-pos":[{"mask-linear-to":em()}],"mask-image-linear-from-color":[{"mask-linear-from":q()}],"mask-image-linear-to-color":[{"mask-linear-to":q()}],"mask-image-t-from-pos":[{"mask-t-from":em()}],"mask-image-t-to-pos":[{"mask-t-to":em()}],"mask-image-t-from-color":[{"mask-t-from":q()}],"mask-image-t-to-color":[{"mask-t-to":q()}],"mask-image-r-from-pos":[{"mask-r-from":em()}],"mask-image-r-to-pos":[{"mask-r-to":em()}],"mask-image-r-from-color":[{"mask-r-from":q()}],"mask-image-r-to-color":[{"mask-r-to":q()}],"mask-image-b-from-pos":[{"mask-b-from":em()}],"mask-image-b-to-pos":[{"mask-b-to":em()}],"mask-image-b-from-color":[{"mask-b-from":q()}],"mask-image-b-to-color":[{"mask-b-to":q()}],"mask-image-l-from-pos":[{"mask-l-from":em()}],"mask-image-l-to-pos":[{"mask-l-to":em()}],"mask-image-l-from-color":[{"mask-l-from":q()}],"mask-image-l-to-color":[{"mask-l-to":q()}],"mask-image-x-from-pos":[{"mask-x-from":em()}],"mask-image-x-to-pos":[{"mask-x-to":em()}],"mask-image-x-from-color":[{"mask-x-from":q()}],"mask-image-x-to-color":[{"mask-x-to":q()}],"mask-image-y-from-pos":[{"mask-y-from":em()}],"mask-image-y-to-pos":[{"mask-y-to":em()}],"mask-image-y-from-color":[{"mask-y-from":q()}],"mask-image-y-to-color":[{"mask-y-to":q()}],"mask-image-radial":[{"mask-radial":[er,K]}],"mask-image-radial-from-pos":[{"mask-radial-from":em()}],"mask-image-radial-to-pos":[{"mask-radial-to":em()}],"mask-image-radial-from-color":[{"mask-radial-from":q()}],"mask-image-radial-to-color":[{"mask-radial-to":q()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":j()}],"mask-image-conic-pos":[{"mask-conic":[G]}],"mask-image-conic-from-pos":[{"mask-conic-from":em()}],"mask-image-conic-to-pos":[{"mask-conic-to":em()}],"mask-image-conic-from-color":[{"mask-conic-from":q()}],"mask-image-conic-to-color":[{"mask-conic-to":q()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:_()}],"mask-repeat":[{mask:H()}],"mask-size":[{mask:I()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",er,K]}],filter:[{filter:["","none",er,K]}],blur:[{blur:eu()}],brightness:[{brightness:[G,er,K]}],contrast:[{contrast:[G,er,K]}],"drop-shadow":[{"drop-shadow":["","none",p,ed,et]}],"drop-shadow-color":[{"drop-shadow":q()}],grayscale:[{grayscale:["",G,er,K]}],"hue-rotate":[{"hue-rotate":[G,er,K]}],invert:[{invert:["",G,er,K]}],saturate:[{saturate:[G,er,K]}],sepia:[{sepia:["",G,er,K]}],"backdrop-filter":[{"backdrop-filter":["","none",er,K]}],"backdrop-blur":[{"backdrop-blur":eu()}],"backdrop-brightness":[{"backdrop-brightness":[G,er,K]}],"backdrop-contrast":[{"backdrop-contrast":[G,er,K]}],"backdrop-grayscale":[{"backdrop-grayscale":["",G,er,K]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[G,er,K]}],"backdrop-invert":[{"backdrop-invert":["",G,er,K]}],"backdrop-opacity":[{"backdrop-opacity":[G,er,K]}],"backdrop-saturate":[{"backdrop-saturate":[G,er,K]}],"backdrop-sepia":[{"backdrop-sepia":["",G,er,K]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":k()}],"border-spacing-x":[{"border-spacing-x":k()}],"border-spacing-y":[{"border-spacing-y":k()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",er,K]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[G,"initial",er,K]}],ease:[{ease:["linear","initial",g,er,K]}],delay:[{delay:[G,er,K]}],animate:[{animate:["none",b,er,K]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[x,er,K]}],"perspective-origin":[{"perspective-origin":w()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:eh()}],"scale-x":[{"scale-x":eh()}],"scale-y":[{"scale-y":eh()}],"scale-z":[{"scale-z":eh()}],"scale-3d":["scale-3d"],skew:[{skew:ex()}],"skew-x":[{"skew-x":ex()}],"skew-y":[{"skew-y":ex()}],transform:[{transform:[er,K,"","none","gpu","cpu"]}],"transform-origin":[{origin:w()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:ef()}],"translate-x":[{"translate-x":ef()}],"translate-y":[{"translate-y":ef()}],"translate-z":[{"translate-z":ef()}],"translate-none":["translate-none"],accent:[{accent:q()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:q()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",er,K]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":k()}],"scroll-mx":[{"scroll-mx":k()}],"scroll-my":[{"scroll-my":k()}],"scroll-ms":[{"scroll-ms":k()}],"scroll-me":[{"scroll-me":k()}],"scroll-mt":[{"scroll-mt":k()}],"scroll-mr":[{"scroll-mr":k()}],"scroll-mb":[{"scroll-mb":k()}],"scroll-ml":[{"scroll-ml":k()}],"scroll-p":[{"scroll-p":k()}],"scroll-px":[{"scroll-px":k()}],"scroll-py":[{"scroll-py":k()}],"scroll-ps":[{"scroll-ps":k()}],"scroll-pe":[{"scroll-pe":k()}],"scroll-pt":[{"scroll-pt":k()}],"scroll-pr":[{"scroll-pr":k()}],"scroll-pb":[{"scroll-pb":k()}],"scroll-pl":[{"scroll-pl":k()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",er,K]}],fill:[{fill:["none",...q()]}],"stroke-w":[{stroke:[G,ea,Q,Y]}],stroke:[{stroke:["none",...q()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function ej(...e){return ev(m(e))}let ew=((e,s)=>t=>{var r;if((null==s?void 0:s.variants)==null)return m(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:a,defaultVariants:l}=s,i=Object.keys(a).map(e=>{let s=null==t?void 0:t[e],r=null==l?void 0:l[e];if(null===s)return null;let i=u(s)||u(r);return a[e][i]}),n=t&&Object.entries(t).reduce((e,s)=>{let[t,r]=s;return void 0===r||(e[t]=r),e},{});return m(e,i,null==s||null==(r=s.compoundVariants)?void 0:r.reduce((e,s)=>{let{class:t,className:r,...a}=s;return Object.entries(a).every(e=>{let[s,t]=e;return Array.isArray(t)?t.includes({...l,...n}[s]):({...l,...n})[s]===t})?[...e,t,r]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)})("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function ey({className:e,variant:s,size:t,asChild:a=!1,...l}){return(0,r.jsx)(a?o:"button",{"data-slot":"button",className:ej(ew({variant:s,size:t,className:e})),...l})}function eN({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card",className:ej("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...s})}function ek({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-content",className:ej("px-6",e),...s})}let eA=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),eC=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),ez=e=>{let s=eC(e);return s.charAt(0).toUpperCase()+s.slice(1)},eR=(...e)=>e.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim(),eP=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var eS={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let e$=(0,i.forwardRef)(({color:e="currentColor",size:s=24,strokeWidth:t=2,absoluteStrokeWidth:r,className:a="",children:l,iconNode:n,...o},d)=>(0,i.createElement)("svg",{ref:d,...eS,width:s,height:s,stroke:e,strokeWidth:r?24*Number(t)/Number(s):t,className:eR("lucide",a),...!l&&!eP(o)&&{"aria-hidden":"true"},...o},[...n.map(([e,s])=>(0,i.createElement)(e,s)),...Array.isArray(l)?l:[l]])),eM=(e,s)=>{let t=(0,i.forwardRef)(({className:t,...r},a)=>(0,i.createElement)(e$,{ref:a,iconNode:s,className:eR(`lucide-${eA(ez(e))}`,`lucide-${e}`,t),...r}));return t.displayName=ez(e),t},eE=eM("shopping-bag",[["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}],["path",{d:"M3.103 6.034h17.794",key:"awc11p"}],["path",{d:"M3.4 5.467a2 2 0 0 0-.4 1.2V20a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6.667a2 2 0 0 0-.4-1.2l-2-2.667A2 2 0 0 0 17 2H7a2 2 0 0 0-1.6.8z",key:"o988cm"}]]),eL=eM("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),eq=eM("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);function e_(){return(0,r.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center p-4",children:(0,r.jsx)(eN,{className:"w-full max-w-lg",children:(0,r.jsxs)(ek,{className:"flex flex-col items-center justify-center py-16 text-center space-y-6",children:[(0,r.jsx)("div",{className:"w-24 h-24 text-muted-foreground",children:(0,r.jsx)(eE,{className:"w-full h-full"})}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Product Not Found"}),(0,r.jsx)("p",{className:"text-muted-foreground max-w-md",children:"Sorry, we couldn't find the product you're looking for. It may have been removed or the link might be incorrect."})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 w-full max-w-sm",children:[(0,r.jsx)(ey,{asChild:!0,className:"flex-1",children:(0,r.jsxs)(l(),{href:"/shop",className:"flex items-center gap-2",children:[(0,r.jsx)(eL,{className:"w-4 h-4"}),"Browse Products"]})}),(0,r.jsx)(ey,{variant:"outline",asChild:!0,className:"flex-1",children:(0,r.jsxs)(l(),{href:"/",className:"flex items-center gap-2",children:[(0,r.jsx)(eq,{className:"w-4 h-4"}),"Go Home"]})})]}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,r.jsxs)("p",{children:["Need help? ",(0,r.jsx)(l(),{href:"/contact",className:"text-primary hover:underline",children:"Contact us"})]})})]})})})}},77560:(e,s,t)=>{Promise.resolve().then(t.bind(t,85948))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82932:(e,s,t)=>{Promise.resolve().then(t.bind(t,11614))},83997:e=>{"use strict";e.exports=require("tty")},84677:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var r=t(60687);t(43210);var a=t(85814),l=t.n(a),i=t(29523),n=t(44493),o=t(43649),d=t(78122),c=t(28559),m=t(32192);function u({error:e,reset:s}){return(0,r.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center p-4",children:(0,r.jsx)(n.Zp,{className:"w-full max-w-lg",children:(0,r.jsxs)(n.Wu,{className:"flex flex-col items-center justify-center py-16 text-center space-y-6",children:[(0,r.jsx)("div",{className:"w-20 h-20 text-destructive",children:(0,r.jsx)(o.A,{className:"w-full h-full"})}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-foreground",children:"Something went wrong!"}),(0,r.jsx)("p",{className:"text-muted-foreground max-w-md",children:"We encountered an error while loading this product page. This might be a temporary issue."}),!1]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 w-full max-w-sm",children:[(0,r.jsxs)(i.$,{onClick:s,className:"flex-1 flex items-center gap-2",size:"lg",children:[(0,r.jsx)(d.A,{className:"w-4 h-4"}),"Try Again"]}),(0,r.jsx)(i.$,{variant:"outline",asChild:!0,className:"flex-1 flex items-center gap-2",size:"lg",children:(0,r.jsxs)(l(),{href:"/shop",children:[(0,r.jsx)(c.A,{className:"w-4 h-4"}),"Back to Shop"]})})]}),(0,r.jsx)(i.$,{variant:"ghost",asChild:!0,className:"flex items-center gap-2",children:(0,r.jsxs)(l(),{href:"/",children:[(0,r.jsx)(m.A,{className:"w-4 h-4"}),"Go Home"]})})]})})})}},85948:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\app\\\\product\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\product\\[id]\\page.tsx","default")},87288:(e,s,t)=>{Promise.resolve().then(t.bind(t,95432))},88920:(e,s,t)=>{"use strict";t.d(s,{N:()=>b});var r=t(60687),a=t(43210),l=t(12157),i=t(72789),n=t(15124),o=t(21279),d=t(18171),c=t(32582);class m extends a.Component{getSnapshotBeforeUpdate(e){let s=this.props.childRef.current;if(s&&e.isPresent&&!this.props.isPresent){let e=s.offsetParent,t=(0,d.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=s.offsetHeight||0,r.width=s.offsetWidth||0,r.top=s.offsetTop,r.left=s.offsetLeft,r.right=t-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function u({children:e,isPresent:s,anchorX:t,root:l}){let i=(0,a.useId)(),n=(0,a.useRef)(null),o=(0,a.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:d}=(0,a.useContext)(c.Q);return(0,a.useInsertionEffect)(()=>{let{width:e,height:r,top:a,left:c,right:m}=o.current;if(s||!n.current||!e||!r)return;let u="left"===t?`left: ${c}`:`right: ${m}`;n.current.dataset.motionPopId=i;let p=document.createElement("style");d&&(p.nonce=d);let h=l??document.head;return h.appendChild(p),p.sheet&&p.sheet.insertRule(`
          [data-motion-pop-id="${i}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${r}px !important;
            ${u}px !important;
            top: ${a}px !important;
          }
        `),()=>{h.removeChild(p),h.contains(p)&&h.removeChild(p)}},[s]),(0,r.jsx)(m,{isPresent:s,childRef:n,sizeRef:o,children:a.cloneElement(e,{ref:n})})}let p=({children:e,initial:s,isPresent:t,onExitComplete:l,custom:n,presenceAffectsLayout:d,mode:c,anchorX:m,root:p})=>{let x=(0,i.M)(h),f=(0,a.useId)(),g=!0,b=(0,a.useMemo)(()=>(g=!1,{id:f,initial:s,isPresent:t,custom:n,onExitComplete:e=>{for(let s of(x.set(e,!0),x.values()))if(!s)return;l&&l()},register:e=>(x.set(e,!1),()=>x.delete(e))}),[t,x,l]);return d&&g&&(b={...b}),(0,a.useMemo)(()=>{x.forEach((e,s)=>x.set(s,!1))},[t]),a.useEffect(()=>{t||x.size||!l||l()},[t]),"popLayout"===c&&(e=(0,r.jsx)(u,{isPresent:t,anchorX:m,root:p,children:e})),(0,r.jsx)(o.t.Provider,{value:b,children:e})};function h(){return new Map}var x=t(86044);let f=e=>e.key||"";function g(e){let s=[];return a.Children.forEach(e,e=>{(0,a.isValidElement)(e)&&s.push(e)}),s}let b=({children:e,custom:s,initial:t=!0,onExitComplete:o,presenceAffectsLayout:d=!0,mode:c="sync",propagate:m=!1,anchorX:u="left",root:h})=>{let[b,v]=(0,x.xQ)(m),j=(0,a.useMemo)(()=>g(e),[e]),w=m&&!b?[]:j.map(f),y=(0,a.useRef)(!0),N=(0,a.useRef)(j),k=(0,i.M)(()=>new Map),[A,C]=(0,a.useState)(j),[z,R]=(0,a.useState)(j);(0,n.E)(()=>{y.current=!1,N.current=j;for(let e=0;e<z.length;e++){let s=f(z[e]);w.includes(s)?k.delete(s):!0!==k.get(s)&&k.set(s,!1)}},[z,w.length,w.join("-")]);let P=[];if(j!==A){let e=[...j];for(let s=0;s<z.length;s++){let t=z[s],r=f(t);w.includes(r)||(e.splice(s,0,t),P.push(t))}return"wait"===c&&P.length&&(e=P),R(g(e)),C(j),null}let{forceRender:S}=(0,a.useContext)(l.L);return(0,r.jsx)(r.Fragment,{children:z.map(e=>{let a=f(e),l=(!m||!!b)&&(j===z||w.includes(a));return(0,r.jsx)(p,{isPresent:l,initial:(!y.current||!!t)&&void 0,custom:s,presenceAffectsLayout:d,mode:c,root:h,onExitComplete:l?void 0:()=>{if(!k.has(a))return;k.set(a,!0);let e=!0;k.forEach(s=>{s||(e=!1)}),e&&(S?.(),R(N.current),m&&v?.(),o&&o())},anchorX:u,children:e},a)})})}},89667:(e,s,t)=>{"use strict";t.d(s,{p:()=>l});var r=t(60687);t(43210);var a=t(4780);function l({className:e,type:s,...t}){return(0,r.jsx)("input",{type:s,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},94735:e=>{"use strict";e.exports=require("events")},95432:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>eo});var r=t(60687),a=t(43210),l=t(54351),i=t(93853),n=t(30474),o=t(88920),d=t(26001),c=t(29523),m=t(44493),u=t(62688);let p=(0,u.A)("zoom-in",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),h=(0,u.A)("maximize-2",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"m21 3-7 7",key:"1l2asr"}],["path",{d:"m3 21 7-7",key:"tjx5ai"}],["path",{d:"M9 21H3v-6",key:"wtvkvv"}]]);var x=t(11860),f=t(47033),g=t(14952);let b=(0,a.memo)(({images:e,productTitle:s})=>{let[t,l]=(0,a.useState)(0),[i,u]=(0,a.useState)(!1),[b,v]=(0,a.useState)(!1),[j,w]=(0,a.useState)({x:0,y:0}),y=(0,a.useRef)(null),N=e[t],k=(0,a.useCallback)(e=>{l(e),v(!1)},[]),A=()=>{v(!b)},C=()=>{u(!0)},z=()=>{u(!1)},R=s=>{"prev"===s?l(s=>0===s?e.length-1:s-1):l(s=>s===e.length-1?0:s+1)};return e&&0!==e.length?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(m.Zp,{className:"relative overflow-hidden group",children:(0,r.jsxs)("div",{ref:y,className:"relative aspect-square cursor-zoom-in",onMouseMove:e=>{if(!b||!y.current)return;let s=y.current.getBoundingClientRect();w({x:(e.clientX-s.left)/s.width*100,y:(e.clientY-s.top)/s.height*100})},onMouseLeave:()=>v(!1),onClick:A,children:[(0,r.jsx)(n.default,{src:N.url,alt:`${s} - Image ${t+1}`,fill:!0,className:`object-cover transition-transform duration-300 ${b?"scale-150":"scale-100"}`,style:b?{transformOrigin:`${j.x}% ${j.y}%`}:{},priority:0===t,quality:90,sizes:"(max-width: 768px) 100vw, 50vw"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300"}),(0,r.jsxs)("div",{className:"absolute top-4 right-4 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:[(0,r.jsx)(c.$,{size:"icon",variant:"secondary",className:"w-8 h-8 rounded-full bg-white/90 hover:bg-white shadow-md",onClick:e=>{e.stopPropagation(),A()},children:(0,r.jsx)(p,{className:"w-4 h-4"})}),(0,r.jsx)(c.$,{size:"icon",variant:"secondary",className:"w-8 h-8 rounded-full bg-white/90 hover:bg-white shadow-md",onClick:e=>{e.stopPropagation(),C()},children:(0,r.jsx)(h,{className:"w-4 h-4"})})]}),e.length>1&&(0,r.jsxs)("div",{className:"absolute bottom-4 left-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm",children:[t+1," / ",e.length]})]})}),e.length>1&&(0,r.jsx)("div",{className:"flex gap-2 overflow-x-auto pb-2",children:e.map((e,a)=>(0,r.jsx)("button",{onClick:()=>k(a),className:`relative flex-shrink-0 w-20 h-20 rounded-md overflow-hidden border-2 transition-all duration-200 ${a===t?"border-primary shadow-md":"border-transparent hover:border-muted-foreground"}`,children:(0,r.jsx)(n.default,{src:e.url,alt:`${s} - Thumbnail ${a+1}`,fill:!0,className:"object-cover",sizes:"80px"})},a))}),(0,r.jsx)(o.N,{children:i&&(0,r.jsxs)(d.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-50 bg-black/90 flex items-center justify-center",onKeyDown:e=>{"Escape"===e.key?z():"ArrowLeft"===e.key?R("prev"):"ArrowRight"===e.key&&R("next")},tabIndex:-1,children:[(0,r.jsx)(c.$,{size:"icon",variant:"ghost",className:"absolute top-4 right-4 text-white hover:bg-white/20 z-10",onClick:z,children:(0,r.jsx)(x.A,{className:"w-6 h-6"})}),e.length>1&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.$,{size:"icon",variant:"ghost",className:"absolute left-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20 z-10",onClick:()=>R("prev"),children:(0,r.jsx)(f.A,{className:"w-6 h-6"})}),(0,r.jsx)(c.$,{size:"icon",variant:"ghost",className:"absolute right-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20 z-10",onClick:()=>R("next"),children:(0,r.jsx)(g.A,{className:"w-6 h-6"})})]}),(0,r.jsx)(d.P.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.8,opacity:0},className:"relative max-w-4xl max-h-[90vh] w-full h-full flex items-center justify-center p-4",children:(0,r.jsx)(n.default,{src:N.url,alt:`${s} - Image ${t+1}`,width:800,height:800,className:"max-w-full max-h-full object-contain",quality:95})},t),e.length>1&&(0,r.jsxs)("div",{className:"absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/70 text-white px-4 py-2 rounded-full",children:[t+1," of ",e.length]})]})})]}):(0,r.jsx)(m.Zp,{className:"aspect-square bg-muted flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-muted-foreground text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 mx-auto mb-2 opacity-50",children:(0,r.jsx)("svg",{fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"})})}),(0,r.jsx)("p",{children:"No image available"})]})})});var v=t(96834),j=t(89667),w=t(54300),y=t(28253),N=t(63213),k=t(64398),A=t(5748),C=t(96474),z=t(28561),R=t(67760),P=t(19526),S=t(72575);let $=(0,u.A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]),M=(0,u.A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]),E=(0,u.A)("link-2",[["path",{d:"M9 17H7A5 5 0 0 1 7 7h2",key:"8i5ue5"}],["path",{d:"M15 7h2a5 5 0 1 1 0 10h-2",key:"1b9ql8"}],["line",{x1:"8",x2:"16",y1:"12",y2:"12",key:"1jonct"}]]),L=({product:e,url:s})=>{let t=`Check out this amazing product: ${e.title}`,a=encodeURIComponent(s),l=encodeURIComponent(t),n={facebook:`https://www.facebook.com/sharer/sharer.php?u=${a}`,twitter:`https://twitter.com/intent/tweet?url=${a}&text=${l}`,linkedin:`https://www.linkedin.com/sharing/share-offsite/?url=${a}`,whatsapp:`https://wa.me/?text=${l}%20${a}`},o=e=>{let s=n[e];window.open(s,"_blank","width=600,height=400")},d=async()=>{try{await navigator.clipboard.writeText(s),i.oR.success("Link copied to clipboard!")}catch(e){console.error("Failed to copy link:",e),i.oR.error("Failed to copy link")}},m=async()=>{if(navigator.share)try{await navigator.share({title:e.title,text:t,url:s})}catch(e){console.error("Error sharing:",e)}else d()};return(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsx)(c.$,{variant:"outline",size:"icon",onClick:()=>o("facebook"),className:"w-10 h-10",title:"Share on Facebook",children:(0,r.jsx)(P.A,{className:"w-4 h-4"})}),(0,r.jsx)(c.$,{variant:"outline",size:"icon",onClick:()=>o("twitter"),className:"w-10 h-10",title:"Share on Twitter",children:(0,r.jsx)(S.A,{className:"w-4 h-4"})}),(0,r.jsx)(c.$,{variant:"outline",size:"icon",onClick:()=>o("linkedin"),className:"w-10 h-10",title:"Share on LinkedIn",children:(0,r.jsx)($,{className:"w-4 h-4"})}),(0,r.jsx)(c.$,{variant:"outline",size:"icon",onClick:()=>o("whatsapp"),className:"w-10 h-10",title:"Share on WhatsApp",children:(0,r.jsx)(M,{className:"w-4 h-4"})}),(0,r.jsx)(c.$,{variant:"outline",size:"icon",onClick:m,className:"w-10 h-10",title:"Copy link",children:(0,r.jsx)(E,{className:"w-4 h-4"})})]})},q=({product:e,reviews:s})=>{let t,[l,n]=(0,a.useState)(1),[o,d]=(0,a.useState)(!1),{addToCart:u,isUpdating:p}=(0,y._)(),{isAuthenticated:h}=(0,N.A)(),x=s=>{s>=1&&s<=e.stock&&n(s)},f=async()=>{if(!h)return void i.oR.error("Please sign in to add items to cart");await u(e._id,l)&&n(1)},g=0===e.stock,b=e.stock>0&&e.stock<=5,P=e.averageRating||0,S=s.length;return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{children:(0,r.jsx)(v.E,{variant:"secondary",className:"text-xs sm:text-sm",children:e.category})}),(0,r.jsx)("div",{children:(0,r.jsx)("h1",{className:"text-2xl sm:text-3xl md:text-4xl font-bold text-foreground leading-tight",children:e.title})}),(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-2 sm:gap-4",children:[(0,r.jsx)("div",{className:"flex items-center gap-1",children:((e=0)=>{let s=[],t=Math.floor(e),a=e%1!=0;for(let e=0;e<5;e++)e<t?s.push((0,r.jsx)(k.A,{className:"w-5 h-5 fill-yellow-400 text-yellow-400"},e)):e===t&&a?s.push((0,r.jsxs)("div",{className:"relative w-5 h-5",children:[(0,r.jsx)(k.A,{className:"w-5 h-5 text-gray-300 absolute"}),(0,r.jsx)("div",{className:"overflow-hidden w-1/2",children:(0,r.jsx)(k.A,{className:"w-5 h-5 fill-yellow-400 text-yellow-400"})})]},e)):s.push((0,r.jsx)(k.A,{className:"w-5 h-5 text-gray-300"},e));return s})(P)}),(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:[P.toFixed(1)," (",S," review",1!==S?"s":"",")"]})]}),(0,r.jsx)("div",{className:"space-y-1",children:(0,r.jsx)("div",{className:"text-2xl sm:text-3xl font-bold text-primary",children:(t=e.price,new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(t))})}),(0,r.jsx)("div",{className:"prose prose-sm max-w-none",children:(0,r.jsx)("p",{className:"text-muted-foreground leading-relaxed",children:e.description})}),(0,r.jsx)("div",{children:g?(0,r.jsx)(v.E,{variant:"destructive",className:"text-xs sm:text-sm",children:"Out of Stock"}):b?(0,r.jsxs)(v.E,{className:"bg-orange-500 hover:bg-orange-600 text-xs sm:text-sm",children:["Only ",e.stock," left in stock"]}):(0,r.jsxs)(v.E,{className:"bg-green-500 hover:bg-green-600 text-xs sm:text-sm",children:["In Stock (",e.stock," available)"]})}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2 max-w-xs",children:[(0,r.jsx)(w.J,{htmlFor:"quantity",className:"text-sm font-medium",children:"Quantity"}),(0,r.jsxs)("div",{className:"flex items-center w-full border rounded-md overflow-hidden",children:[(0,r.jsx)(c.$,{variant:"ghost",size:"icon",className:"h-10 w-10",onClick:()=>x(l-1),disabled:l<=1,children:(0,r.jsx)(A.A,{className:"w-4 h-4"})}),(0,r.jsx)(j.p,{id:"quantity",type:"number",min:"1",max:e.stock,value:l,onChange:e=>x(parseInt(e.target.value)||1),className:"h-10 w-20 text-center border-0 focus-visible:ring-0"}),(0,r.jsx)(c.$,{variant:"ghost",size:"icon",className:"h-10 w-10",onClick:()=>x(l+1),disabled:l>=e.stock,children:(0,r.jsx)(C.A,{className:"w-4 h-4"})})]})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 w-full",children:[(0,r.jsxs)(c.$,{onClick:f,disabled:p||g,className:"w-full sm:flex-1 h-12 text-base",size:"lg",children:[(0,r.jsx)(z.A,{className:"w-5 h-5 mr-2"}),p?"Adding...":g?"Out of Stock":"Add to Cart"]}),(0,r.jsx)(c.$,{variant:"outline",onClick:()=>{if(!h)return void i.oR.error("Please sign in to add to wishlist");d(!o),i.oR.success(o?"Removed from wishlist":"Added to wishlist")},className:"h-12 px-6 w-full sm:w-auto",size:"lg",children:(0,r.jsx)(R.A,{className:`w-5 h-5 ${o?"fill-red-500 text-red-500":""}`})})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(w.J,{className:"text-sm font-medium",children:"Share this product"}),(0,r.jsx)(L,{product:e,url:`${window.location.origin}/product/${e._id}`})]}),(0,r.jsx)(m.Zp,{className:"w-full",children:(0,r.jsx)(m.Wu,{className:"p-4 sm:p-6",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"font-semibold text-lg",children:"Specifications"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between py-2 border-b",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Weight"}),(0,r.jsx)("span",{className:"font-medium",children:e.weight})]}),(0,r.jsxs)("div",{className:"flex justify-between py-2 border-b",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Category"}),(0,r.jsx)("span",{className:"font-medium",children:e.category})]}),(0,r.jsxs)("div",{className:"flex justify-between py-2 border-b",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Stock"}),(0,r.jsxs)("span",{className:"font-medium",children:[e.stock," units"]})]}),(0,r.jsxs)("div",{className:"flex justify-between py-2",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Product ID"}),(0,r.jsx)("span",{className:"font-medium text-xs",children:e._id})]})]})]})})})]})},_=(0,u.A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var H=t(19080),I=t(58887);let W=({product:e,reviews:s,isLoadingReviews:t})=>{let[l,i]=(0,a.useState)("description"),n=[{id:"description",label:"Description",icon:_,count:null},{id:"specifications",label:"Specifications",icon:H.A,count:null},{id:"reviews",label:"Reviews",icon:I.A,count:s.length}],o=e=>Array.from({length:5},(s,t)=>(0,r.jsx)(k.A,{className:`w-4 h-4 ${t<e?"fill-yellow-400 text-yellow-400":"text-gray-300"}`},t)),d=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return(0,r.jsxs)(m.Zp,{children:[(0,r.jsx)("div",{className:"border-b",children:(0,r.jsx)("div",{className:"flex flex-wrap sm:flex-nowrap",children:n.map(e=>{let s=e.icon;return(0,r.jsxs)("button",{onClick:()=>i(e.id),className:`flex items-center gap-2 px-4 py-3 text-sm font-medium border-b-2 transition-colors w-full sm:w-auto ${l===e.id?"border-primary text-primary bg-primary/5":"border-transparent text-muted-foreground hover:text-foreground hover:bg-muted/40"}`,children:[(0,r.jsx)(s,{className:"w-4 h-4"}),e.label,null!==e.count&&(0,r.jsx)(v.E,{variant:"secondary",className:"ml-1 text-xs",children:e.count})]},e.id)})})}),(0,r.jsx)(m.Wu,{className:"p-4 sm:p-6",children:(()=>{switch(l){case"description":return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"Product Description"}),(0,r.jsx)("p",{className:"text-muted-foreground leading-relaxed",children:e.description}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-6 mt-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Key Features"}),(0,r.jsxs)("ul",{className:"space-y-1 text-sm text-muted-foreground",children:[(0,r.jsx)("li",{children:"• High-quality materials"}),(0,r.jsx)("li",{children:"• Durable construction"}),(0,r.jsx)("li",{children:"• Easy to use"}),(0,r.jsx)("li",{children:"• Great value for money"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"What's Included"}),(0,r.jsxs)("ul",{className:"space-y-1 text-sm text-muted-foreground",children:[(0,r.jsxs)("li",{children:["• 1x ",e.title]}),(0,r.jsx)("li",{children:"• User manual"}),(0,r.jsx)("li",{children:"• Warranty card"}),(0,r.jsx)("li",{children:"• Original packaging"})]})]})]})]});case"specifications":return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"Product Specifications"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between border-b py-2",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Category"}),(0,r.jsx)("span",{className:"font-medium",children:e.category})]}),(0,r.jsxs)("div",{className:"flex justify-between border-b py-2",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Weight"}),(0,r.jsx)("span",{className:"font-medium",children:e.weight})]}),(0,r.jsxs)("div",{className:"flex justify-between border-b py-2",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Stock"}),(0,r.jsxs)("span",{className:"font-medium",children:[e.stock," units"]})]}),(0,r.jsxs)("div",{className:"flex justify-between border-b py-2",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Product ID"}),(0,r.jsx)("span",{className:"font-medium text-xs",children:e._id})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between border-b py-2",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Price"}),(0,r.jsxs)("span",{className:"font-medium",children:["$",e.price]})]}),(0,r.jsxs)("div",{className:"flex justify-between border-b py-2",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Rating"}),(0,r.jsx)("span",{className:"font-medium",children:e.averageRating?`${e.averageRating.toFixed(1)}/5`:"No ratings"})]}),(0,r.jsxs)("div",{className:"flex justify-between border-b py-2",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Created"}),(0,r.jsx)("span",{className:"font-medium",children:d(e.createdAt)})]}),(0,r.jsxs)("div",{className:"flex justify-between border-b py-2",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Updated"}),(0,r.jsx)("span",{className:"font-medium",children:d(e.updatedAt)})]})]})]})]});case"reviews":return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"Customer Reviews"}),t?(0,r.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0].map((e,s)=>(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"h-4 w-32 bg-muted rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-full"}),(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-3/4"})]},s))}):0===s.length?(0,r.jsx)("p",{className:"text-center text-muted-foreground",children:"No reviews yet. Be the first to review this product!"}):(0,r.jsxs)("div",{className:"space-y-4",children:[s.slice(0,3).map(e=>(0,r.jsx)("div",{className:"border-b pb-4",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-muted text-sm flex items-center justify-center font-bold",children:e?.fullName?.charAt(0).toUpperCase()}),(0,r.jsxs)("div",{className:"flex-1 space-y-1",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("p",{className:"font-medium text-sm",children:e.fullName}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:d(e.createdAt)})]}),(0,r.jsx)("div",{className:"flex items-center gap-1",children:o(e.rating)}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:e.comment})]})]})},e._id)),s.length>3&&(0,r.jsx)("div",{className:"text-center pt-4",children:(0,r.jsxs)(c.$,{variant:"outline",onClick:()=>{let e=document.getElementById("reviews-section");e&&e.scrollIntoView({behavior:"smooth"})},children:["View All ",s.length," Reviews"]})})]})]});default:return null}})()})]})};var G=t(85814),U=t.n(G),D=t(70334);let Z=({product:e})=>{let s,t=0===e.stock;return(0,r.jsx)(U(),{href:`/product/${e._id}`,children:(0,r.jsxs)(m.Zp,{className:"group hover:shadow-lg transition-all duration-300 cursor-pointer",children:[(0,r.jsxs)("div",{className:"relative aspect-square bg-muted overflow-hidden",children:[e.images&&e.images.length>0?(0,r.jsx)(n.default,{src:e.images[0].url,alt:e.title,fill:!0,className:"object-cover group-hover:scale-105 transition-transform duration-300"}):(0,r.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-16 h-16 text-muted-foreground opacity-50",children:(0,r.jsx)("svg",{fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"})})})}),t&&(0,r.jsx)("div",{className:"absolute top-2 left-2",children:(0,r.jsx)(v.E,{variant:"destructive",className:"text-xs",children:"Out of Stock"})})]}),(0,r.jsxs)(m.Wu,{className:"p-4 space-y-2",children:[(0,r.jsx)("div",{className:"text-xs text-muted-foreground uppercase tracking-wide",children:e.category}),(0,r.jsx)("h3",{className:"font-medium text-sm leading-tight line-clamp-2 min-h-[2.5rem] group-hover:text-primary transition-colors",children:e.title}),e.averageRating&&(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("div",{className:"flex items-center",children:((e=0)=>{let s=[],t=Math.floor(e),a=e%1!=0;for(let e=0;e<5;e++)e<t?s.push((0,r.jsx)(k.A,{className:"w-3 h-3 fill-yellow-400 text-yellow-400"},e)):e===t&&a?s.push((0,r.jsxs)("div",{className:"relative w-3 h-3",children:[(0,r.jsx)(k.A,{className:"w-3 h-3 text-gray-300 absolute"}),(0,r.jsx)("div",{className:"overflow-hidden w-1/2",children:(0,r.jsx)(k.A,{className:"w-3 h-3 fill-yellow-400 text-yellow-400"})})]},e)):s.push((0,r.jsx)(k.A,{className:"w-3 h-3 text-gray-300"},e));return s})(e.averageRating)}),(0,r.jsxs)("span",{className:"text-xs text-muted-foreground",children:["(",e.averageRating.toFixed(1),")"]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"font-bold text-primary",children:(s=e.price,new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(s))}),e.stock>0&&e.stock<=5&&(0,r.jsx)(v.E,{className:"bg-orange-500 hover:bg-orange-600 text-xs",children:"Low Stock"})]})]})]})})},B=({products:e,isLoading:s})=>s?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"h-8 bg-muted rounded animate-pulse w-48"}),(0,r.jsx)("div",{className:"h-10 bg-muted rounded animate-pulse w-32"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:Array.from({length:4}).map((e,s)=>(0,r.jsxs)(m.Zp,{children:[(0,r.jsx)("div",{className:"aspect-square bg-muted animate-pulse"}),(0,r.jsxs)(m.Wu,{className:"p-4 space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-full"}),(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-3/4"}),(0,r.jsx)("div",{className:"h-6 bg-muted rounded animate-pulse w-20"})]})]},s))})]}):0===e.length?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold",children:"Related Products"}),(0,r.jsx)(m.Zp,{children:(0,r.jsxs)(m.Wu,{className:"flex flex-col items-center justify-center py-16 text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 mb-4 text-muted-foreground",children:(0,r.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",className:"w-full h-full",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H6a1 1 0 00-1 1v1m16 0H4"})})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-foreground mb-2",children:"No related products found"}),(0,r.jsx)("p",{className:"text-muted-foreground max-w-md mb-4",children:"We couldn't find any related products at the moment. Check out our full catalog instead."}),(0,r.jsx)(c.$,{asChild:!0,children:(0,r.jsxs)(U(),{href:"/shop",children:["Browse All Products",(0,r.jsx)(D.A,{className:"w-4 h-4 ml-2"})]})})]})})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold",children:"Related Products"}),(0,r.jsx)(c.$,{variant:"outline",asChild:!0,children:(0,r.jsxs)(U(),{href:"/shop",className:"flex items-center gap-2",children:["View All",(0,r.jsx)(D.A,{className:"w-4 h-4"})]})})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:e.map((e,s)=>(0,r.jsx)(d.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*s},children:(0,r.jsx)(Z,{product:e})},e._id))}),(0,r.jsxs)("div",{className:"text-center pt-8",children:[(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"Looking for something else?"}),(0,r.jsx)(c.$,{asChild:!0,size:"lg",children:(0,r.jsxs)(U(),{href:"/shop",children:["Explore All Products",(0,r.jsx)(D.A,{className:"w-4 h-4 ml-2"})]})})]})]});var F=t(15079),V=t(58869);let O=(0,u.A)("thumbs-up",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2a3.13 3.13 0 0 1 3 3.88Z",key:"emmmcr"}]]),T=(0,u.A)("thumbs-down",[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22a3.13 3.13 0 0 1-3-3.88Z",key:"m61m77"}]]),J=({productId:e,onReviewSubmitted:s,onCancel:t})=>{let[n,o]=(0,a.useState)(0),[d,u]=(0,a.useState)(0),[p,h]=(0,a.useState)(""),[x,f]=(0,a.useState)(!1),{user:g}=(0,N.A)(),b=async t=>{if(t.preventDefault(),0===n)return void i.oR.error("Please select a rating");if(p.trim().length<10)return void i.oR.error("Please write a review with at least 10 characters");try{f(!0);let t={rating:n,comment:p.trim()},r=await l.j.addReview(e,t);r.success&&r.data&&(s(r.data),i.oR.success("Review submitted successfully!"),o(0),h(""))}catch(e){e.response&&i.oR.error(e.response.data.message||"Failed to submit review")}finally{f(!1)}};return(0,r.jsxs)(m.Zp,{children:[(0,r.jsx)(m.aR,{children:(0,r.jsx)(m.ZB,{children:"Write a Review"})}),(0,r.jsx)(m.Wu,{children:(0,r.jsxs)("form",{onSubmit:b,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(w.J,{className:"text-base font-medium",children:"Rating *"}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(()=>{let e=[];for(let s=1;s<=5;s++)e.push((0,r.jsx)("button",{type:"button",onClick:()=>o(s),onMouseEnter:()=>u(s),onMouseLeave:()=>u(0),className:"focus:outline-none",children:(0,r.jsx)(k.A,{className:`w-8 h-8 transition-colors ${s<=(d||n)?"fill-yellow-400 text-yellow-400":"text-gray-300 hover:text-yellow-300"}`})},s));return e})(),n>0&&(0,r.jsxs)("span",{className:"ml-2 text-sm text-muted-foreground",children:[n," star",1!==n?"s":""]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(w.J,{htmlFor:"comment",className:"text-base font-medium",children:"Your Review *"}),(0,r.jsx)("textarea",{id:"comment",value:p,onChange:e=>h(e.target.value),placeholder:"Share your thoughts about this product...",className:"w-full min-h-[120px] px-3 py-2 border border-input rounded-md bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 resize-vertical",required:!0,minLength:10,maxLength:1e3}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground text-right",children:[p.length,"/1000 characters"]})]}),g&&(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Reviewing as: ",(0,r.jsx)("span",{className:"font-medium",children:g.fullName})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)(c.$,{type:"submit",disabled:x||0===n||p.trim().length<10,className:"flex-1",children:x?"Submitting...":"Submit Review"}),(0,r.jsx)(c.$,{type:"button",variant:"outline",onClick:t,disabled:x,children:"Cancel"})]})]})})]})},X=({productId:e,reviews:s,isLoading:t,onReviewSubmitted:l})=>{let[i,n]=(0,a.useState)(!1),[o,u]=(0,a.useState)("newest"),[p,h]=(0,a.useState)("all"),{isAuthenticated:x}=(0,N.A)(),f=(0,a.useMemo)(()=>{let e={1:0,2:0,3:0,4:0,5:0};s.forEach(s=>{e[s.rating]++});let t=s.length,r=t?s.reduce((e,s)=>e+s.rating,0)/t:0,a={1:t?e[1]/t*100:0,2:t?e[2]/t*100:0,3:t?e[3]/t*100:0,4:t?e[4]/t*100:0,5:t?e[5]/t*100:0};return{stats:e,total:t,average:r,percentages:a}},[s]),g=(0,a.useMemo)(()=>{let e=[...s];return"all"!==p&&(e=e.filter(e=>e.rating===Number(p))),e.sort((e,s)=>"newest"===o?new Date(s.createdAt).getTime()-new Date(e.createdAt).getTime():"oldest"===o?new Date(e.createdAt).getTime()-new Date(s.createdAt).getTime():"highest"===o?s.rating-e.rating:"lowest"===o?e.rating-s.rating:0),e},[s,o,p]),b=(e,s="sm")=>{let t="sm"===s?"w-4 h-4":"w-5 h-5";return Array.from({length:5},(s,a)=>(0,r.jsx)(k.A,{className:`${t} ${a<e?"fill-yellow-400 text-yellow-400":"text-gray-300"}`},a))},j=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return t?(0,r.jsxs)(m.Zp,{children:[(0,r.jsx)(m.aR,{children:(0,r.jsx)(m.ZB,{children:"Customer Reviews"})}),(0,r.jsx)(m.Wu,{className:"space-y-4",children:Array.from({length:3}).map((e,s)=>(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-32"}),(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-full"}),(0,r.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-3/4"})]},s))})]}):(0,r.jsxs)(m.Zp,{children:[(0,r.jsx)(m.aR,{children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between gap-4",children:[(0,r.jsx)(m.ZB,{className:"text-2xl",children:"Customer Reviews"}),x&&(0,r.jsx)(c.$,{onClick:()=>n(!i),variant:i?"outline":"default",children:i?"Cancel":"Write a Review"})]})}),(0,r.jsxs)(m.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"text-center space-y-2",children:[(0,r.jsx)("div",{className:"text-4xl font-bold text-primary",children:f.average.toFixed(1)}),(0,r.jsx)("div",{className:"flex justify-center",children:b(Math.round(f.average),"md")}),(0,r.jsxs)("p",{className:"text-muted-foreground",children:["Based on ",f.total," review(s)"]})]}),(0,r.jsx)("div",{className:"space-y-2",children:[5,4,3,2,1].map(e=>(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"w-6 text-sm",children:e}),(0,r.jsx)(k.A,{className:"w-4 h-4 fill-yellow-400 text-yellow-400"}),(0,r.jsx)("div",{className:"flex-1 h-2 bg-muted rounded-full",children:(0,r.jsx)("div",{className:"h-2 bg-yellow-400 rounded-full",style:{width:`${f.percentages[e]}%`}})}),(0,r.jsx)("span",{className:"w-6 text-sm text-muted-foreground",children:f.stats[e]})]},e))})]}),i&&x&&(0,r.jsx)(d.P.div,{initial:{opacity:0},animate:{opacity:1},children:(0,r.jsx)(J,{productId:e,onReviewSubmitted:l,onCancel:()=>n(!1)})}),s.length>0&&(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4",children:[(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsxs)(F.l6,{value:p,onValueChange:e=>h(e),children:[(0,r.jsx)(F.bq,{className:"w-32",children:(0,r.jsx)(F.yv,{placeholder:"Filter"})}),(0,r.jsxs)(F.gC,{children:[(0,r.jsx)(F.eb,{value:"all",children:"All Stars"}),(0,r.jsx)(F.eb,{value:"5",children:"5 Stars"}),(0,r.jsx)(F.eb,{value:"4",children:"4 Stars"}),(0,r.jsx)(F.eb,{value:"3",children:"3 Stars"}),(0,r.jsx)(F.eb,{value:"2",children:"2 Stars"}),(0,r.jsx)(F.eb,{value:"1",children:"1 Star"})]})]}),(0,r.jsxs)(F.l6,{value:o,onValueChange:e=>u(e),children:[(0,r.jsx)(F.bq,{className:"w-36",children:(0,r.jsx)(F.yv,{placeholder:"Sort"})}),(0,r.jsxs)(F.gC,{children:[(0,r.jsx)(F.eb,{value:"newest",children:"Newest"}),(0,r.jsx)(F.eb,{value:"oldest",children:"Oldest"}),(0,r.jsx)(F.eb,{value:"highest",children:"Highest Rated"}),(0,r.jsx)(F.eb,{value:"lowest",children:"Lowest Rated"}),(0,r.jsx)(F.eb,{value:"helpful",children:"Most Helpful"})]})]})]}),(0,r.jsxs)(v.E,{variant:"secondary",children:[g.length," review",1!==g.length?"s":""]})]}),(0,r.jsx)("div",{className:"space-y-6",children:0===g.length?(0,r.jsx)("p",{className:"text-center text-muted-foreground py-8",children:"all"===p?"No reviews yet. Be the first to write one!":`No ${p}-star reviews found.`}):g.map((e,s)=>(0,r.jsx)(d.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.05*s},className:"border-b pb-6 last:border-0",children:(0,r.jsxs)("div",{className:"flex items-start gap-4",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-muted rounded-full flex items-center justify-center",children:(0,r.jsx)(V.A,{className:"w-5 h-5 text-muted-foreground"})}),(0,r.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,r.jsx)("div",{className:"flex justify-between",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:e.fullName}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[b(e.rating),(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:j(e.createdAt)})]})]})}),(0,r.jsx)("p",{className:"text-muted-foreground",children:e.comment}),(0,r.jsxs)("div",{className:"flex gap-4 pt-2",children:[(0,r.jsxs)(c.$,{variant:"ghost",size:"sm",children:[(0,r.jsx)(O,{className:"w-4 h-4 mr-1"})," Helpful"]}),(0,r.jsxs)(c.$,{variant:"ghost",size:"sm",children:[(0,r.jsx)(T,{className:"w-4 h-4 mr-1"})," Not Helpful"]})]})]})]})},e._id))})]})]})};var K=t(4780);let Q=a.forwardRef(({...e},s)=>(0,r.jsx)("nav",{ref:s,"aria-label":"breadcrumb",...e}));Q.displayName="Breadcrumb";let Y=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("ol",{ref:t,className:(0,K.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",e),...s}));Y.displayName="BreadcrumbList";let ee=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("li",{ref:t,className:(0,K.cn)("inline-flex items-center gap-1.5",e),...s}));ee.displayName="BreadcrumbItem";let es=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("a",{ref:t,className:(0,K.cn)("transition-colors hover:text-foreground",e),...s}));es.displayName="BreadcrumbLink";let et=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("span",{ref:t,role:"link","aria-disabled":"true","aria-current":"page",className:(0,K.cn)("font-normal text-foreground",e),...s}));et.displayName="BreadcrumbPage";let er=({children:e,className:s,...t})=>(0,r.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,K.cn)("[&>svg]:size-3.5",s),...t,children:e??(0,r.jsx)(g.A,{})});er.displayName="BreadcrumbSeparator";let ea=(0,a.memo)(({product:e})=>{let[s,t]=(0,a.useState)([]),[n,o]=(0,a.useState)([]),[d,c]=(0,a.useState)(!0),[m,u]=(0,a.useState)(!0);(0,a.useEffect)(()=>{e&&(async()=>{try{c(!0);let s=await l.j.getReviews(e._id);s.success&&s.data&&t(s.data.reviews)}catch(e){console.error("Error fetching reviews:",e),i.oR.error("Failed to load reviews")}finally{c(!1)}})()},[e]),(0,a.useEffect)(()=>{e&&(async()=>{try{u(!0);let s=await l.j.getProductsByCategory({category:e.category,limit:8});if(s.success&&s.data){let t=s.data.products.filter(s=>s._id!==e._id).slice(0,4);o(t)}}catch(e){console.error("Error fetching related products:",e)}finally{u(!1)}})()},[e]);let p=(0,a.useCallback)(e=>{t(s=>[e,...s]),i.oR.success("Review submitted successfully!")},[]);return e?(0,r.jsx)("div",{className:"min-h-screen bg-background",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)(Q,{className:"mb-6 sm:mb-8",children:(0,r.jsxs)(Y,{className:"flex flex-wrap gap-2 text-sm",children:[(0,r.jsx)(ee,{children:(0,r.jsx)(es,{href:"/",children:"Home"})}),(0,r.jsx)(er,{}),(0,r.jsx)(ee,{children:(0,r.jsx)(es,{href:"/shop",children:"Shop"})}),(0,r.jsx)(er,{}),(0,r.jsx)(ee,{children:(0,r.jsx)(es,{href:`/shop?category=${encodeURIComponent(e.category)}`,children:e.category})}),(0,r.jsx)(er,{}),(0,r.jsx)(ee,{children:(0,r.jsx)(et,{children:e.title})})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 xl:gap-12 mb-12 md:mb-16",children:[(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsx)(b,{images:e.images,productTitle:e.title})}),(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)(q,{product:e,reviews:s})})]}),(0,r.jsx)("div",{className:"mb-12 md:mb-16",children:(0,r.jsx)(W,{product:e,reviews:s,isLoadingReviews:d,onReviewSubmitted:p})}),(0,r.jsx)("div",{id:"reviews-section",className:"mb-12 md:mb-16",children:(0,r.jsx)(X,{productId:e._id,reviews:s,isLoading:d,onReviewSubmitted:p})}),(0,r.jsx)("div",{className:"mb-8 md:mb-16",children:(0,r.jsx)(B,{products:n,isLoading:m,currentProductId:e._id})})]})}):null});var el=t(45648),ei=t(66981),en=t(16189);function eo(){let{id:e}=(0,en.useParams)(),[s,t]=(0,a.useState)(null),[l,i]=(0,a.useState)(!0);return!s||l?(0,r.jsx)(el.default,{}):(0,r.jsx)(ei.default,{children:(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)(el.default,{}),children:(0,r.jsx)(ea,{product:s})})})}},96474:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96834:(e,s,t)=>{"use strict";t.d(s,{E:()=>o});var r=t(60687);t(43210);var a=t(8730),l=t(24224),i=t(4780);let n=(0,l.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:s,asChild:t=!1,...l}){let o=t?a.DX:"span";return(0,r.jsx)(o,{"data-slot":"badge",className:(0,i.cn)(n({variant:s}),e),...l})}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,162,658,598,1,893,367],()=>t(45589));module.exports=r})();