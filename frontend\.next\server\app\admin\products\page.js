(()=>{var e={};e.id=575,e.ids=[575],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6343:(e,s,t)=>{Promise.resolve().then(t.bind(t,7956)),Promise.resolve().then(t.bind(t,39552))},7956:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminProtectedRoute.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminProtectedRoute.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16023:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38571:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(37413),a=t(7956),i=t(39552);function l(){return(0,r.jsx)(a.default,{children:(0,r.jsx)(i.default,{})})}},39552:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\components\\\\admin\\\\ProductManagement.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\ProductManagement.tsx","default")},45335:(e,s,t)=>{"use strict";t.d(s,{default:()=>_});var r=t(60687),a=t(43210),i=t(30474),l=t(58376),d=t(29523),c=t(89667),o=t(44493),n=t(96834),u=t(91821),m=t(85726),p=t(96474),h=t(99270),x=t(80462),g=t(93613),j=t(19080),f=t(93661),v=t(63143),b=t(13861),y=t(88233),N=t(93853),w=t(54300),A=t(4780);let P=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("textarea",{className:(0,A.cn)("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...s}));P.displayName="Textarea";var k=t(28559),C=t(62688);let R=(0,C.A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var S=t(11860),q=t(41862);let M=(0,C.A)("wand-sparkles",[["path",{d:"m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72",key:"ul74o6"}],["path",{d:"m14 7 3 3",key:"1r5n42"}],["path",{d:"M5 6v4",key:"ilb8ba"}],["path",{d:"M19 14v4",key:"blhpug"}],["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M7 8H3",key:"zfb6yr"}],["path",{d:"M21 16h-4",key:"1cnmox"}],["path",{d:"M11 3H9",key:"1obp7u"}]]);var E=t(16023);let F=({product:e,onClose:s,onSuccess:t})=>{let[n,m]=(0,a.useState)({title:e?.title||"",description:e?.description||"",price:e?.price?.toString()||"",category:e?.category||"",weight:e?.weight||"",stock:e?.stock?.toString()||""}),[p,h]=(0,a.useState)([]),[x,j]=(0,a.useState)(e?.images?.map(e=>e.url)||[]),[f,v]=(0,a.useState)(!1),[b,y]=(0,a.useState)(""),A=(0,a.useRef)(null),[C,F]=(0,a.useState)(!1),[G,U]=(0,a.useState)(null),[_,L]=(0,a.useState)(""),[$,z]=(0,a.useState)(null),[H,I]=(0,a.useState)(!1),J=!!e,Z=e=>{let{name:s,value:t}=e.target;m(e=>({...e,[s]:t})),b&&y("")},D=s=>{j(e=>e.filter((e,t)=>t!==s));let t=e?.images?.length||0;if(s>=t){let e=s-t;h(s=>s.filter((s,t)=>t!==e))}},B=async()=>{if(!G)return void N.oR.error("Please select an image first");F(!0),y("");try{let e=await l.ZJ.suggestProductDetails(G);e.success&&e.data?(z(e.data[0]),I(!0),N.oR.success("Product suggestions generated successfully!")):N.oR.error(e.message||"Failed to generate suggestions")}catch(s){let e=s.response?.data?.message||"An error occurred while generating suggestions";N.oR.error(e)}finally{F(!1)}},W=()=>{z(null),I(!1),U(null),L("")},T=()=>n.title.trim()?n.description.trim()?!n.price||isNaN(Number(n.price))||0>=Number(n.price)?(y("Please enter a valid price"),!1):n.category.trim()?n.weight.trim()?!n.stock||isNaN(Number(n.stock))||0>Number(n.stock)?(y("Please enter a valid stock quantity"),!1):!!J||0!==p.length||(y("Please select at least one product image"),!1):(y("Product weight is required"),!1):(y("Product category is required"),!1):(y("Product description is required"),!1):(y("Product title is required"),!1),V=async s=>{if(s.preventDefault(),T()){v(!0),y("");try{if(J&&e){let s={title:n.title,description:n.description,price:Number(n.price),category:n.category,weight:n.weight,stock:Number(n.stock)},r=await l.ZJ.updateProduct(e._id,s);r.success?(N.oR.success("Product updated successfully"),t()):y(r.message||"Failed to update product")}else{let e={title:n.title,description:n.description,price:Number(n.price),category:n.category,weight:n.weight,stock:Number(n.stock),images:p},s=await l.ZJ.uploadProduct(e);s.success?(N.oR.success("Product created successfully"),t()):y(s.message||"Failed to create product")}}catch(s){let e=s.response?.data?.message||`An error occurred while ${J?"updating":"creating"} the product`;y(e),N.oR.error(e)}finally{v(!1)}}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(d.$,{variant:"ghost",size:"icon",onClick:s,children:(0,r.jsx)(k.A,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:J?"Edit Product":"Add New Product"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:J?"Update product information":"Fill in the details to add a new product"})]})]}),(0,r.jsxs)("form",{onSubmit:V,className:"space-y-6",children:[b&&(0,r.jsxs)(u.Fc,{variant:"destructive",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{children:"Error"}),(0,r.jsx)("p",{children:b})]})]}),!J&&(0,r.jsxs)(o.Zp,{children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{children:"AI Product Suggestion"}),(0,r.jsx)(o.BT,{children:"Upload an image to get AI-generated product title and description suggestions"})]}),(0,r.jsx)(o.Wu,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"border-2 border-dashed border-border rounded-lg p-4 text-center",children:[(0,r.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{let s=e.target.files?.[0];if(!s)return;if(!["image/jpeg","image/jpg","image/png","image/webp"].includes(s.type))return void N.oR.error("Please select a valid image file (JPEG, PNG, WebP)");if(s.size>5242880)return void N.oR.error("Please select an image smaller than 5MB");U(s);let t=new FileReader;t.onload=e=>{L(e.target?.result)},t.readAsDataURL(s)},className:"hidden",id:"suggestion-image",disabled:C}),(0,r.jsxs)("label",{htmlFor:"suggestion-image",className:"cursor-pointer",children:[(0,r.jsx)(R,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-sm font-medium",children:"Upload Image for Suggestions"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"JPEG, PNG, WebP (max 5MB)"})]})]}),_&&(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(i.default,{src:_,alt:"Suggestion preview",width:400,height:128,className:"w-full h-32 object-cover rounded-lg border border-border"}),(0,r.jsx)(d.$,{type:"button",variant:"destructive",size:"icon",className:"absolute top-2 right-2 h-6 w-6",onClick:W,disabled:C,children:(0,r.jsx)(S.A,{className:"h-3 w-3"})})]}),(0,r.jsx)(d.$,{type:"button",onClick:B,disabled:!G||C,className:"w-full",children:C?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(q.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Generating Suggestions..."]}):"Get AI Suggestions"})]}),(0,r.jsx)("div",{className:"space-y-4",children:H&&$?(0,r.jsxs)("div",{className:"border border-border rounded-lg p-4 space-y-4",children:[(0,r.jsx)("h4",{className:"font-medium text-sm text-muted-foreground",children:"AI Suggestions"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(w.J,{className:"text-xs text-muted-foreground",children:"Suggested Title"}),(0,r.jsx)("p",{className:"text-sm font-medium p-2 bg-muted rounded",children:$.title})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(w.J,{className:"text-xs text-muted-foreground",children:"Suggested Description"}),(0,r.jsx)("p",{className:"text-sm p-2 bg-muted rounded max-h-20 overflow-y-auto",children:$.description})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(d.$,{type:"button",size:"sm",onClick:()=>{$&&(m(e=>({...e,title:$.title,description:$.description})),I(!1),N.oR.success("Suggestions applied to form"))},className:"flex-1",children:"Accept Suggestions"}),(0,r.jsx)(d.$,{type:"button",variant:"outline",size:"sm",onClick:W,className:"flex-1",children:"Reject"})]})]}):(0,r.jsx)("div",{className:"border border-dashed border-border rounded-lg p-8 text-center text-muted-foreground",children:(0,r.jsx)("p",{className:"text-sm",children:"AI suggestions will appear here"})})})]})})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{children:"Basic Information"}),(0,r.jsx)(o.BT,{children:"Enter the basic details of your product"})]}),(0,r.jsxs)(o.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(w.J,{htmlFor:"title",children:"Product Title *"}),(0,r.jsx)(c.p,{id:"title",name:"title",value:n.title,onChange:Z,placeholder:"Enter product title",required:!0,disabled:f})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(w.J,{htmlFor:"category",children:"Category *"}),(0,r.jsxs)("select",{id:"category",name:"category",value:n.category,onChange:Z,className:"w-full px-3 py-2 border border-border rounded-md bg-background text-foreground",required:!0,disabled:f,children:[(0,r.jsx)("option",{value:"",children:"Select a category"}),["electronics","clothing","home-garden","sports","books","toys","beauty","automotive","food","other"].map(e=>(0,r.jsx)("option",{value:e,children:e.charAt(0).toUpperCase()+e.slice(1).replace("-"," & ")},e))]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(w.J,{htmlFor:"description",children:"Description *"}),(0,r.jsx)(P,{id:"description",name:"description",value:n.description,onChange:Z,placeholder:"Enter product description",rows:4,required:!0,disabled:f}),(0,r.jsx)(M,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(w.J,{htmlFor:"price",children:"Price ($) *"}),(0,r.jsx)(c.p,{id:"price",name:"price",type:"number",step:"0.01",min:"0",value:n.price,onChange:Z,placeholder:"0.00",required:!0,disabled:f})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(w.J,{htmlFor:"weight",children:"Weight *"}),(0,r.jsx)(c.p,{id:"weight",name:"weight",value:n.weight,onChange:Z,placeholder:"e.g., 500g, 1.2kg",required:!0,disabled:f})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(w.J,{htmlFor:"stock",children:"Stock Quantity *"}),(0,r.jsx)(c.p,{id:"stock",name:"stock",type:"number",min:"0",value:n.stock,onChange:Z,placeholder:"0",required:!0,disabled:f})]})]})]})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{children:"Product Images"}),(0,r.jsx)(o.BT,{children:J?"Current images are shown below. Upload new images to replace them.":"Upload high-quality images of your product (max 5MB each)"})]}),(0,r.jsxs)(o.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"border-2 border-dashed border-border rounded-lg p-6 text-center",children:[(0,r.jsx)("input",{ref:A,type:"file",multiple:!0,accept:"image/*",onChange:e=>{let s=Array.from(e.target.files||[]);if(0===s.length)return;let t=["image/jpeg","image/jpg","image/png","image/webp"];return s.filter(e=>!t.includes(e.type)).length>0?void N.oR.error("Please select only image files (JPEG, PNG, WebP)"):s.filter(e=>e.size>5242880).length>0?void N.oR.error("Please select images smaller than 5MB"):void(h(e=>[...e,...s]),s.forEach(e=>{let s=new FileReader;s.onload=e=>{j(s=>[...s,e.target?.result])},s.readAsDataURL(e)}))},className:"hidden",disabled:f}),(0,r.jsx)(R,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-lg font-medium mb-2",children:"Upload Product Images"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"Drag and drop images here, or click to browse"}),(0,r.jsxs)(d.$,{type:"button",variant:"outline",onClick:()=>A.current?.click(),disabled:f,children:[(0,r.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"Choose Images"]})]}),x.length>0&&(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:x.map((e,s)=>(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)(i.default,{src:e,alt:`Preview ${s+1}`,width:200,height:128,className:"w-full h-32 object-cover rounded-lg border border-border"}),(0,r.jsx)(d.$,{type:"button",variant:"destructive",size:"icon",className:"absolute top-2 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity",onClick:()=>D(s),disabled:f,children:(0,r.jsx)(S.A,{className:"h-3 w-3"})})]},s))})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,r.jsx)(d.$,{type:"button",variant:"outline",onClick:s,disabled:f,children:"Cancel"}),(0,r.jsx)(d.$,{type:"submit",disabled:f,children:f?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(q.A,{className:"mr-2 h-4 w-4 animate-spin"}),J?"Updating...":"Creating..."]}):J?"Update Product":"Create Product"})]})]})]})};var G=t(21342);let U=({product:e,onEdit:s,onDelete:t})=>{let a;return(0,r.jsxs)(o.Zp,{className:"overflow-hidden",children:[(0,r.jsxs)("div",{className:"aspect-square relative",children:[e.images?.[0]?.url?(0,r.jsx)(i.default,{src:e.images[0].url,alt:e.title,fill:!0,className:"object-cover"}):(0,r.jsx)("div",{className:"w-full h-full bg-muted flex items-center justify-center",children:(0,r.jsx)(j.A,{className:"h-12 w-12 text-muted-foreground"})}),(0,r.jsx)("div",{className:"absolute top-2 right-2",children:(0,r.jsxs)(G.rI,{children:[(0,r.jsx)(G.ty,{asChild:!0,children:(0,r.jsx)(d.$,{variant:"secondary",size:"icon",className:"h-8 w-8",children:(0,r.jsx)(f.A,{className:"h-4 w-4"})})}),(0,r.jsxs)(G.SQ,{align:"end",children:[(0,r.jsxs)(G._2,{onClick:s,children:[(0,r.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Edit"]}),(0,r.jsxs)(G._2,{children:[(0,r.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"View Details"]}),(0,r.jsxs)(G._2,{onClick:t,className:"text-destructive",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})})]}),(0,r.jsxs)(o.Wu,{className:"p-4",children:[(0,r.jsx)("h3",{className:"font-semibold text-lg mb-2 line-clamp-2",children:e.title}),(0,r.jsx)("p",{className:"text-muted-foreground text-sm mb-3 line-clamp-2",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsx)("span",{className:"text-2xl font-bold text-primary",children:(a=e.price,new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(a))}),(0,r.jsx)(n.E,{variant:e.stock>0?"default":"destructive",children:e.stock>0?`${e.stock} in stock`:"Out of stock"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm text-muted-foreground",children:[(0,r.jsx)("span",{className:"capitalize",children:e.category}),(0,r.jsx)("span",{children:e.weight})]})]})]})},_=()=>{let[e,s]=(0,a.useState)([]),[t,i]=(0,a.useState)(!0),[n,f]=(0,a.useState)(""),[v,b]=(0,a.useState)(""),[y,w]=(0,a.useState)(""),[A,P]=(0,a.useState)(!1),[k,C]=(0,a.useState)(null),[R,S]=(0,a.useState)(1),[q,M]=(0,a.useState)(1),E=async()=>{try{i(!0);let e=await l.jU.getAllProducts();e.success&&e.data?(s(e.data.products),M(e.data.totalPages),S(e.data.currentPage)):f(e.message||"Failed to load products")}catch(s){let e=s.response?.data?.message||"An error occurred while loading products";f(e),N.oR.error(e)}finally{i(!1)}};(0,a.useEffect)(()=>{E()},[]);let G=async e=>{if(confirm("Are you sure you want to delete this product?"))try{let s=await l.ZJ.deleteProduct(e);s.success?(N.oR.success("Product deleted successfully"),E()):N.oR.error(s.message||"Failed to delete product")}catch(s){let e=s.response?.data?.message||"An error occurred while deleting product";N.oR.error(e)}},_=e=>{C(e),P(!0)},L=()=>{P(!1),C(null)},$=e.filter(e=>{let s=e.title.toLowerCase().includes(v.toLowerCase())||e.description.toLowerCase().includes(v.toLowerCase()),t=!y||e.category===y;return s&&t}),z=Array.from(new Set(e.map(e=>e.category)));return A?(0,r.jsx)(F,{product:k,onClose:L,onSuccess:()=>{L(),E()}}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Product Management"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage your product inventory, add new products, and update existing ones."})]}),(0,r.jsxs)(d.$,{onClick:()=>P(!0),className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Add Product"]})]}),(0,r.jsx)(o.Zp,{children:(0,r.jsx)(o.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(h.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(c.p,{placeholder:"Search products...",value:v,onChange:e=>b(e.target.value),className:"pl-10"})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("select",{value:y,onChange:e=>w(e.target.value),className:"px-3 py-2 border border-border rounded-md bg-background text-foreground",children:[(0,r.jsx)("option",{value:"",children:"All Categories"}),z.map(e=>(0,r.jsx)("option",{value:e,children:e.charAt(0).toUpperCase()+e.slice(1)},e))]}),(0,r.jsx)(d.$,{variant:"outline",size:"icon",children:(0,r.jsx)(x.A,{className:"h-4 w-4"})})]})]})})}),n&&(0,r.jsxs)(u.Fc,{variant:"destructive",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{children:"Error loading products"}),(0,r.jsx)("p",{children:n})]})]}),t&&(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((e,s)=>(0,r.jsx)(o.Zp,{children:(0,r.jsxs)(o.Wu,{className:"p-4",children:[(0,r.jsx)(m.E,{className:"h-48 w-full mb-4"}),(0,r.jsx)(m.E,{className:"h-4 w-3/4 mb-2"}),(0,r.jsx)(m.E,{className:"h-4 w-1/2 mb-2"}),(0,r.jsx)(m.E,{className:"h-4 w-1/4"})]})},s))}),!t&&!n&&(0,r.jsx)(r.Fragment,{children:0===$.length?(0,r.jsx)(o.Zp,{children:(0,r.jsxs)(o.Wu,{className:"p-8 text-center",children:[(0,r.jsx)(j.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No products found"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:v||y?"No products match your current filters.":"Get started by adding your first product."}),(0,r.jsxs)(d.$,{onClick:()=>P(!0),children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Add Product"]})]})}):(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:$.map(e=>(0,r.jsx)(U,{product:e,onEdit:()=>_(e),onDelete:()=>G(e._id)},e._id))})}),q>1&&(0,r.jsxs)("div",{className:"flex justify-center space-x-2",children:[(0,r.jsx)(d.$,{variant:"outline",onClick:()=>S(e=>Math.max(e-1,1)),disabled:1===R,children:"Previous"}),(0,r.jsxs)("span",{className:"flex items-center px-4 py-2 text-sm text-muted-foreground",children:["Page ",R," of ",q]}),(0,r.jsx)(d.$,{variant:"outline",onClick:()=>S(e=>Math.min(e+1,q)),disabled:R===q,children:"Next"})]})]})}},45663:(e,s,t)=>{Promise.resolve().then(t.bind(t,73482)),Promise.resolve().then(t.bind(t,45335))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},73482:(e,s,t)=>{"use strict";t.d(s,{default:()=>o});var r=t(60687);t(43210);var a=t(58873),i=t(91821),l=t(93613),d=t(99891);let c=(e,s)=>"admin"===s?"admin"===e.role||"superAdmin"===e.role:"superAdmin"===s&&"superAdmin"===e.role,o=({children:e,requiredRole:s,fallback:t})=>{let{admin:o,isAuthenticated:n,isLoading:u}=(0,a.b)();return u?t||(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})}):n&&o?s&&!c(o,s)?t||(0,r.jsxs)(i.Fc,{variant:"destructive",children:[(0,r.jsx)(d.A,{className:"h-4 w-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{children:"Insufficient Permissions"}),(0,r.jsxs)("p",{children:["You need ",s," privileges to access this page. Your current role is: ",o.role]})]})]}):(0,r.jsx)(r.Fragment,{children:e}):t||(0,r.jsxs)(i.Fc,{variant:"destructive",children:[(0,r.jsx)(l.A,{className:"h-4 w-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{children:"Access Denied"}),(0,r.jsx)("p",{children:"You must be logged in as an admin to access this page."})]})]})}},74075:e=>{"use strict";e.exports=require("zlib")},75653:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>n,routeModule:()=>m,tree:()=>o});var r=t(65239),a=t(48088),i=t(88170),l=t.n(i),d=t(30893),c={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>d[e]);t.d(s,c);let o={children:["",{children:["admin",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,38571)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\products\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,99111)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,n=["C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\products\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/products/page",pathname:"/admin/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},79551:e=>{"use strict";e.exports=require("url")},80462:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88233:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},93661:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},94735:e=>{"use strict";e.exports=require("events")},96474:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,162,658,598,367,10],()=>t(75653));module.exports=r})();