"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5193],{285:(e,t,r)=>{r.d(t,{$:()=>o});var a=r(5155);r(2115);var n=r(9708),s=r(2085),i=r(9434);let d=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:s,asChild:o=!1,...l}=e,c=o?n.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(d({variant:r,size:s,className:t})),...l})}},2523:(e,t,r)=>{r.d(t,{p:()=>s});var a=r(5155);r(2115);var n=r(9434);function s(e){let{className:t,type:r,...s}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}},2657:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3717:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},3779:(e,t,r)=>{r.d(t,{AuthProvider:()=>o,b:()=>d});var a=r(5155),n=r(2115),s=r(5654);let i=(0,n.createContext)(void 0),d=()=>{let e=(0,n.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},o=e=>{let{children:t}=e,[r,d]=(0,n.useState)(null),[o,l]=(0,n.useState)(!0);(0,n.useEffect)(()=>{(async()=>{if(localStorage.getItem("access_token"))try{let e=await s.ZJ.getAdmin();e.success&&e.data&&d(e.data)}catch(e){e.response&&(console.error("Error checking auth status:",e.response.data.message),localStorage.removeItem("access_token"))}l(!1)})()},[]);let c=async()=>{try{await s.ZJ.logout()}catch(e){console.error("Error during logout:",e)}finally{localStorage.removeItem("access_token"),d(null)}};return(0,a.jsx)(i.Provider,{value:{admin:r,isLoading:o,isAuthenticated:!!r,login:(e,t)=>{localStorage.setItem("access_token",t),d(e)},logout:c},children:t})}},4368:(e,t,r)=>{r.d(t,{default:()=>l});var a=r(5155);r(2115);var n=r(3779),s=r(5365),i=r(5339),d=r(5525);let o=(e,t)=>"admin"===t?"admin"===e.role||"superAdmin"===e.role:"superAdmin"===t&&"superAdmin"===e.role,l=e=>{let{children:t,requiredRole:r,fallback:l}=e,{admin:c,isAuthenticated:u,isLoading:v}=(0,n.b)();return v?l||(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})}):u&&c?r&&!o(c,r)?l||(0,a.jsxs)(s.Fc,{variant:"destructive",children:[(0,a.jsx)(d.A,{className:"h-4 w-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{children:"Insufficient Permissions"}),(0,a.jsxs)("p",{children:["You need ",r," privileges to access this page. Your current role is: ",c.role]})]})]}):(0,a.jsx)(a.Fragment,{children:t}):l||(0,a.jsxs)(s.Fc,{variant:"destructive",children:[(0,a.jsx)(i.A,{className:"h-4 w-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{children:"Access Denied"}),(0,a.jsx)("p",{children:"You must be logged in as an admin to access this page."})]})]})}},4838:(e,t,r)=>{r.d(t,{SQ:()=>o,_2:()=>l,mB:()=>c,rI:()=>i,ty:()=>d});var a=r(5155);r(2115);var n=r(9449),s=r(9434);function i(e){let{...t}=e;return(0,a.jsx)(n.bL,{"data-slot":"dropdown-menu",...t})}function d(e){let{...t}=e;return(0,a.jsx)(n.l9,{"data-slot":"dropdown-menu-trigger",...t})}function o(e){let{className:t,sideOffset:r=4,...i}=e;return(0,a.jsx)(n.ZL,{children:(0,a.jsx)(n.UC,{"data-slot":"dropdown-menu-content",sideOffset:r,className:(0,s.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...i})})}function l(e){let{className:t,inset:r,variant:i="default",...d}=e;return(0,a.jsx)(n.q7,{"data-slot":"dropdown-menu-item","data-inset":r,"data-variant":i,className:(0,s.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...d})}function c(e){let{className:t,...r}=e;return(0,a.jsx)(n.wv,{"data-slot":"dropdown-menu-separator",className:(0,s.cn)("bg-border -mx-1 my-1 h-px",t),...r})}},5339:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5365:(e,t,r)=>{r.d(t,{Fc:()=>o,TN:()=>l});var a=r(5155),n=r(2115),s=r(2085),i=r(9434);let d=(0,s.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=n.forwardRef((e,t)=>{let{className:r,variant:n,...s}=e;return(0,a.jsx)("div",{ref:t,role:"alert",className:(0,i.cn)(d({variant:n}),r),...s})});o.displayName="Alert",n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("h5",{ref:t,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",r),...n})}).displayName="AlertTitle";let l=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",r),...n})});l.displayName="AlertDescription"},5525:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5623:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},6126:(e,t,r)=>{r.d(t,{E:()=>o});var a=r(5155);r(2115);var n=r(9708),s=r(2085),i=r(9434);let d=(0,s.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:r,asChild:s=!1,...o}=e,l=s?n.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,i.cn)(d({variant:r}),t),...o})}},6654:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return n}});let a=r(2115);function n(e,t){let r=(0,a.useRef)(null),n=(0,a.useRef)(null);return(0,a.useCallback)(a=>{if(null===a){let e=r.current;e&&(r.current=null,e());let t=n.current;t&&(n.current=null,t())}else e&&(r.current=s(e,a)),t&&(n.current=s(t,a))},[e,t])}function s(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6695:(e,t,r)=>{r.d(t,{BT:()=>o,Wu:()=>l,ZB:()=>d,Zp:()=>s,aR:()=>i,wL:()=>c});var a=r(5155);r(2115);var n=r(9434);function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r})}},6932:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},7108:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},7550:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7924:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8856:(e,t,r)=>{r.d(t,{E:()=>s});var a=r(5155),n=r(9434);function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,n.cn)("animate-pulse rounded-md bg-muted",t),...r})}}}]);