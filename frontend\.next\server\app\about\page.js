(()=>{var e={};e.id=220,e.ids=[220],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},9887:(e,t,r)=>{Promise.resolve().then(r.bind(r,20072))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20072:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),i=r(26001),a=r(19080),o=r(88059);let n=(0,r(62688).A)("shield-check",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);var l=r(5336),d=r(85814),c=r.n(d);let p=[{icon:a.A,title:"Premium Products",description:"We offer a wide range of high-quality and trendy items from top brands."},{icon:o.A,title:"Fast Delivery",description:"Enjoy nationwide delivery with tracking and flexible return options."},{icon:n,title:"Secure Shopping",description:"Shop confidently with secure payments and buyer protection."}],u=()=>(0,s.jsxs)("section",{className:"min-h-screen bg-background text-foreground py-16 px-4 lg:px-20",children:[(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center max-w-3xl mx-auto mb-12",children:[(0,s.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-4",children:"About Mega Mall"}),(0,s.jsx)("p",{className:"text-muted-foreground text-lg",children:"Mega Mall is your one-stop destination for everything fashion, tech, lifestyle & more. We're committed to providing the best shopping experience to people across the globe."})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-3 gap-8 max-w-6xl mx-auto",children:p.map((e,t)=>{let r=e.icon;return(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.2*t,duration:.6},className:"bg-card rounded-xl p-6 shadow-lg hover:shadow-xl transition",children:[(0,s.jsx)("div",{className:"w-12 h-12 flex items-center justify-center rounded-full bg-primary/10 mb-4",children:(0,s.jsx)(r,{className:"w-6 h-6 text-primary"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold mb-2",children:e.title}),(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:e.description})]},t)})}),(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"max-w-4xl mx-auto mt-20 text-center",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold mb-4",children:"Our Mission"}),(0,s.jsx)("p",{className:"text-muted-foreground text-lg",children:"At Mega Mall, we aim to deliver the best of e-commerce—quality products, smooth delivery, and unmatched service. We believe shopping should be easy, secure, and delightful."})]}),(0,s.jsx)(i.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{delay:.8,duration:.6},className:"text-center mt-16",children:(0,s.jsxs)(c(),{href:"/shop",className:"inline-flex items-center gap-2 px-6 py-3 bg-primary text-white rounded-full font-medium shadow-lg hover:bg-primary/90 transition",children:[(0,s.jsx)(l.A,{className:"w-5 h-5"}),"Start Shopping with Mega Mall"]})})]})},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28039:(e,t,r)=>{Promise.resolve().then(r.bind(r,28770))},28354:e=>{"use strict";e.exports=require("util")},28770:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\app\\\\about\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\about\\page.tsx","default")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},98557:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),i=r(48088),a=r(88170),o=r.n(a),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,28770)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\about\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\about\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,162,658,1,367],()=>r(98557));module.exports=s})();