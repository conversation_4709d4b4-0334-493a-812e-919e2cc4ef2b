(()=>{var e={};e.id=16,e.ids=[16],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6307:(e,s,r)=>{"use strict";r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\components\\\\admin\\\\CustomerManagement.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\CustomerManagement.tsx","default")},7956:(e,s,r)=>{"use strict";r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminProtectedRoute.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminProtectedRoute.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23267:(e,s,r)=>{Promise.resolve().then(r.bind(r,73482)),Promise.resolve().then(r.bind(r,67351))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35920:(e,s,r)=>{Promise.resolve().then(r.bind(r,7956)),Promise.resolve().then(r.bind(r,6307))},40228:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57570:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d});var t=r(37413),a=r(7956),i=r(6307);function d(){return(0,t.jsx)(a.default,{children:(0,t.jsx)(i.default,{})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67351:(e,s,r)=>{"use strict";r.d(s,{default:()=>S});var t=r(60687),a=r(43210),i=r(30474),d=r(58376),n=r(29523),l=r(89667),c=r(44493),o=r(96834),m=r(91821),x=r(85726),u=r(41312),h=r(62688);let p=(0,h.A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),j=(0,h.A)("user-x",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]);var f=r(71057),g=r(99270),v=r(80462),N=r(93613),y=r(41550),b=r(48340),A=r(40228),w=r(97992),C=r(13861),k=r(93661),P=r(58887),_=r(93853),q=r(21342);let R=({customer:e,onView:s,onSendMessage:r})=>(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center",children:e.avatar?(0,t.jsx)(i.default,{src:e.avatar,alt:e.fullName,width:48,height:48,className:"rounded-full object-cover"}):(0,t.jsx)(u.A,{className:"h-6 w-6 text-primary"})}),(0,t.jsxs)("div",{className:"flex-1 space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg",children:e.fullName}),(0,t.jsx)(o.E,{variant:e.isVerified?"default":"secondary",children:e.isVerified?"Verified":"Unverified"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-muted-foreground",children:[(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(y.A,{className:"h-3 w-3 mr-1"}),e.email]}),e.phoneNumber&&(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(b.A,{className:"h-3 w-3 mr-1"}),e.phoneNumber]}),(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(A.A,{className:"h-3 w-3 mr-1"}),"Joined ",new Date(e.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})]})]}),e.address&&(0,t.jsxs)("p",{className:"text-sm text-muted-foreground flex items-center",children:[(0,t.jsx)(w.A,{className:"h-3 w-3 mr-1"}),e.address.city,", ",e.address.state]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",onClick:s,children:[(0,t.jsx)(C.A,{className:"h-4 w-4 mr-1"}),"View"]}),(0,t.jsxs)(q.rI,{children:[(0,t.jsx)(q.ty,{asChild:!0,children:(0,t.jsx)(n.$,{variant:"outline",size:"sm",children:(0,t.jsx)(k.A,{className:"h-4 w-4"})})}),(0,t.jsxs)(q.SQ,{align:"end",children:[(0,t.jsxs)(q._2,{onClick:r,children:[(0,t.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"Send Message"]}),(0,t.jsxs)(q._2,{onClick:s,children:[(0,t.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"View Details"]})]})]})]})]})})}),M=({customer:e,orders:s,loadingOrders:r,onBack:a,onSendMessage:d})=>{let l=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),m=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),h=s.reduce((e,s)=>e+s.totalRevenue+s.codCharges,0);return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(n.$,{variant:"ghost",onClick:a,children:"← Back to Customers"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:e.fullName}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Customer Details"})]})]}),(0,t.jsxs)(n.$,{onClick:d,children:[(0,t.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"Send Message"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{children:"Personal Information"})}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center",children:e.avatar?(0,t.jsx)(i.default,{src:e.avatar,alt:e.fullName,width:64,height:64,className:"rounded-full object-cover"}):(0,t.jsx)(u.A,{className:"h-8 w-8 text-primary"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-xl",children:e.fullName}),(0,t.jsx)(o.E,{variant:e.isVerified?"default":"secondary",children:e.isVerified?"Verified Account":"Unverified Account"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Email"}),(0,t.jsx)("p",{className:"font-medium",children:e.email})]}),e.phoneNumber&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Phone"}),(0,t.jsx)("p",{className:"font-medium",children:e.phoneNumber})]}),e.dateOfBirth&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Date of Birth"}),(0,t.jsx)("p",{className:"font-medium",children:m(e.dateOfBirth)})]}),e.gender&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Gender"}),(0,t.jsx)("p",{className:"font-medium capitalize",children:e.gender.replace("-"," ")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Member Since"}),(0,t.jsx)("p",{className:"font-medium",children:m(e.createdAt)})]})]})]})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{children:"Address Information"})}),(0,t.jsx)(c.Wu,{children:e.address?(0,t.jsxs)("div",{className:"space-y-3",children:[e.address.street&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Street Address"}),(0,t.jsx)("p",{className:"font-medium",children:e.address.street})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[e.address.city&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"City"}),(0,t.jsx)("p",{className:"font-medium",children:e.address.city})]}),e.address.state&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"State"}),(0,t.jsx)("p",{className:"font-medium",children:e.address.state})]}),e.address.zipCode&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"ZIP Code"}),(0,t.jsx)("p",{className:"font-medium",children:e.address.zipCode})]}),e.address.country&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Country"}),(0,t.jsx)("p",{className:"font-medium",children:e.address.country})]})]})]}):(0,t.jsx)("p",{className:"text-muted-foreground",children:"No address information available"})})]})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{children:"Order History"}),(0,t.jsxs)(c.BT,{children:[s.length," orders • Total spent: ",l(h)]})]}),(0,t.jsx)(c.Wu,{children:r?(0,t.jsx)("div",{className:"space-y-3",children:Array.from({length:3}).map((e,s)=>(0,t.jsx)(x.E,{className:"h-16 w-full"},s))}):0===s.length?(0,t.jsx)("p",{className:"text-muted-foreground text-center py-8",children:"No orders found for this customer."}):(0,t.jsx)("div",{className:"space-y-3",children:s.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border border-border rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{className:"font-medium",children:["Order #",e._id.slice(-8)]}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:[m(e.createdAt)," • ",e.orderItems.length," items"]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("p",{className:"font-medium",children:l(e.totalRevenue+e.codCharges)}),(0,t.jsx)(o.E,{variant:"delivered"===e.status?"default":"secondary",children:e.status})]})]},e._id))})})]})]})},S=()=>{let[e,s]=(0,a.useState)([]),[r,i]=(0,a.useState)(!0),[o,h]=(0,a.useState)(""),[y,b]=(0,a.useState)(""),[A,w]=(0,a.useState)(null),[C,k]=(0,a.useState)([]),[P,q]=(0,a.useState)(!1),S=async()=>{try{i(!0);let e=await d.ZJ.getAllUsers();e.success&&e.data?s(e.data.users):h(e.message||"Failed to load customers")}catch(s){let e=s.response?.data?.message||"An error occurred while loading customers";h(e),_.oR.error(e)}finally{i(!1)}};(0,a.useEffect)(()=>{S()},[]);let E=async e=>{try{q(!0);let s=await d.ZJ.getUserOrders(e);s.success&&s.data?k(s.data):(k([]),_.oR.error(s.message||"Failed to load customer orders"))}catch(s){k([]);let e=s.response?.data?.message||"An error occurred while loading customer orders";_.oR.error(e)}finally{q(!1)}},U=e=>{w(e),e._id&&E(e._id)},Z=async e=>{let s=prompt("Enter your message to the customer:");if(s&&s.trim())try{let r=await d.ZJ.sendMessageToUser(e,s.trim());r.success?_.oR.success("Message sent successfully"):_.oR.error(r.message||"Failed to send message")}catch(s){let e=s.response?.data?.message||"An error occurred while sending message";_.oR.error(e)}},L=e.filter(e=>{let s=y.toLowerCase();return e.fullName.toLowerCase().includes(s)||e.email.toLowerCase().includes(s)||e.phoneNumber&&e.phoneNumber.toLowerCase().includes(s)}),G={total:e.length,verified:e.filter(e=>e.isVerified).length,unverified:e.filter(e=>!e.isVerified).length,withOrders:e.filter(e=>e._id).length};return A?(0,t.jsx)(M,{customer:A,orders:C,loadingOrders:P,onBack:()=>w(null),onSendMessage:()=>A._id&&Z(A._id)}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Customer Management"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"View and manage customer accounts, orders, and support requests."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Customers"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:G.total})]}),(0,t.jsx)(u.A,{className:"h-8 w-8 text-muted-foreground"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Verified Accounts"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-green-600",children:G.verified})]}),(0,t.jsx)(p,{className:"h-8 w-8 text-green-600"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Unverified Accounts"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:G.unverified})]}),(0,t.jsx)(j,{className:"h-8 w-8 text-yellow-600"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Active Customers"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:G.withOrders})]}),(0,t.jsx)(f.A,{className:"h-8 w-8 text-blue-600"})]})})})]}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"pt-6",children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,t.jsxs)("div",{className:"relative flex-1",children:[(0,t.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(l.p,{placeholder:"Search customers by name, email, or phone...",value:y,onChange:e=>b(e.target.value),className:"pl-10"})]}),(0,t.jsx)("div",{className:"flex gap-2",children:(0,t.jsx)(n.$,{variant:"outline",size:"icon",children:(0,t.jsx)(v.A,{className:"h-4 w-4"})})})]})})}),o&&(0,t.jsxs)(m.Fc,{variant:"destructive",children:[(0,t.jsx)(N.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{children:"Error loading customers"}),(0,t.jsx)("p",{children:o})]})]}),r&&(0,t.jsx)("div",{className:"space-y-4",children:Array.from({length:5}).map((e,s)=>(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.E,{className:"h-4 w-32"}),(0,t.jsx)(x.E,{className:"h-4 w-48"}),(0,t.jsx)(x.E,{className:"h-4 w-24"})]}),(0,t.jsx)(x.E,{className:"h-8 w-20"})]})})},s))}),!r&&!o&&(0,t.jsx)(t.Fragment,{children:0===L.length?(0,t.jsx)(c.Zp,{children:(0,t.jsxs)(c.Wu,{className:"p-8 text-center",children:[(0,t.jsx)(u.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No customers found"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:y?"No customers match your search criteria.":"No customers have registered yet."})]})}):(0,t.jsx)("div",{className:"space-y-4",children:L.map(e=>(0,t.jsx)(R,{customer:e,onView:()=>U(e),onSendMessage:()=>e._id&&Z(e._id)},e._id||e.email))})})]})}},71057:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("shopping-bag",[["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}],["path",{d:"M3.103 6.034h17.794",key:"awc11p"}],["path",{d:"M3.4 5.467a2 2 0 0 0-.4 1.2V20a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6.667a2 2 0 0 0-.4-1.2l-2-2.667A2 2 0 0 0 17 2H7a2 2 0 0 0-1.6.8z",key:"o988cm"}]])},73482:(e,s,r)=>{"use strict";r.d(s,{default:()=>c});var t=r(60687);r(43210);var a=r(58873),i=r(91821),d=r(93613),n=r(99891);let l=(e,s)=>"admin"===s?"admin"===e.role||"superAdmin"===e.role:"superAdmin"===s&&"superAdmin"===e.role,c=({children:e,requiredRole:s,fallback:r})=>{let{admin:c,isAuthenticated:o,isLoading:m}=(0,a.b)();return m?r||(0,t.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})}):o&&c?s&&!l(c,s)?r||(0,t.jsxs)(i.Fc,{variant:"destructive",children:[(0,t.jsx)(n.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{children:"Insufficient Permissions"}),(0,t.jsxs)("p",{children:["You need ",s," privileges to access this page. Your current role is: ",c.role]})]})]}):(0,t.jsx)(t.Fragment,{children:e}):r||(0,t.jsxs)(i.Fc,{variant:"destructive",children:[(0,t.jsx)(d.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{children:"Access Denied"}),(0,t.jsx)("p",{children:"You must be logged in as an admin to access this page."})]})]})}},74075:e=>{"use strict";e.exports=require("zlib")},74673:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>d.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var t=r(65239),a=r(48088),i=r(88170),d=r.n(i),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(s,l);let c={children:["",{children:["admin",{children:["customers",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,57570)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\customers\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,99111)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\customers\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/customers/page",pathname:"/admin/customers",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},79551:e=>{"use strict";e.exports=require("url")},80462:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},93661:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,162,658,598,367,10],()=>r(74673));module.exports=t})();