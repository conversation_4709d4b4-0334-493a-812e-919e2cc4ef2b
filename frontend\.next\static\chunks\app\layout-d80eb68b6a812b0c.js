(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{283:(e,t,s)=>{"use strict";s.d(t,{A:()=>l,AuthProvider:()=>o});var a=s(5155),r=s(2115),i=s(5654);let n=(0,r.createContext)(void 0),l=()=>{let e=(0,r.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},o=e=>{let{children:t}=e,[s,l]=(0,r.useState)(null),[o,c]=(0,r.useState)(!0),d=!!s;(0,r.useEffect)(()=>{(async()=>{if(localStorage.getItem("token"))try{let e=await i.Dv.getUser();e.success&&e.data&&l(e.data)}catch(e){e.response&&(console.error("Error checking auth status:",e.response.data.message),localStorage.removeItem("token"))}c(!1)})()},[]);let m=async()=>{try{await i.Dv.logout()}catch(e){console.error("Error during logout:",e)}finally{localStorage.removeItem("token"),l(null)}},x=async()=>{try{let e=await i.Dv.getUser();e.success&&e.data&&l(e.data)}catch(e){console.error("Error refreshing user data:",e)}};return(0,a.jsx)(n.Provider,{value:{user:s,isLoading:o,isAuthenticated:d,login:(e,t)=>{localStorage.setItem("token",t),l(e)},logout:m,updateUser:e=>{s&&l({...s,...e})},refreshUser:x},children:t})}},285:(e,t,s)=>{"use strict";s.d(t,{$:()=>o});var a=s(5155);s(2115);var r=s(9708),i=s(2085),n=s(9434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:s,size:i,asChild:o=!1,...c}=e,d=o?r.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,n.cn)(l({variant:s,size:i,className:t})),...c})}},347:()=>{},1107:(e,t,s)=>{Promise.resolve().then(s.bind(s,8543)),Promise.resolve().then(s.t.bind(s,5716,23)),Promise.resolve().then(s.t.bind(s,2093,23)),Promise.resolve().then(s.t.bind(s,7735,23)),Promise.resolve().then(s.t.bind(s,347,23)),Promise.resolve().then(s.bind(s,6821)),Promise.resolve().then(s.bind(s,2062)),Promise.resolve().then(s.bind(s,283)),Promise.resolve().then(s.bind(s,5323))},2062:(e,t,s)=>{"use strict";s.d(t,{default:()=>S});var a=s(5155),r=s(7809),i=s(1007),n=s(9772),l=s(7108),o=s(4835),c=s(4783),d=s(6874),m=s.n(d),x=s(285);s(2115);var h=s(5452),u=s(4416),f=s(9434);function p(e){let{...t}=e;return(0,a.jsx)(h.bL,{"data-slot":"sheet",...t})}function g(e){let{...t}=e;return(0,a.jsx)(h.l9,{"data-slot":"sheet-trigger",...t})}function v(e){let{...t}=e;return(0,a.jsx)(h.ZL,{"data-slot":"sheet-portal",...t})}function y(e){let{className:t,...s}=e;return(0,a.jsx)(h.hJ,{"data-slot":"sheet-overlay",className:(0,f.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...s})}function b(e){let{className:t,children:s,side:r="right",...i}=e;return(0,a.jsxs)(v,{children:[(0,a.jsx)(y,{}),(0,a.jsxs)(h.UC,{"data-slot":"sheet-content",className:(0,f.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===r&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===r&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===r&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===r&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...i,children:[s,(0,a.jsxs)(h.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,a.jsx)(u.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}var j=s(4838),N=s(283),w=s(5323),C=s(8543);let k=[{name:"Home",href:"/"},{name:"Shop",href:"/shop"},{name:"About",href:"/about"},{name:"Contact",href:"/contact"}];function S(){let{user:e,isAuthenticated:t,logout:s}=(0,N.A)(),{itemCount:d}=(0,w._)(),h=async()=>{try{await s(),C.oR.success("Logged out successfully")}catch(e){console.log(e)}};return(0,a.jsx)("header",{className:"w-full border-b border-gray-200 bg-white/95 backdrop-blur-sm sticky top-0 z-50 shadow-sm",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:[(0,a.jsx)(m(),{href:"/",className:"text-xl font-bold text-primary transition-colors hover:text-primary/90",children:"Mega Mall"}),(0,a.jsx)("nav",{className:"hidden md:flex items-center space-x-8",children:k.map(e=>(0,a.jsx)(m(),{href:e.href,className:"text-sm font-medium text-gray-700 hover:text-primary transition-colors relative after:absolute after:bottom-[-4px] after:left-0 after:h-[2px] after:w-0 after:bg-primary after:transition-all hover:after:w-full",children:e.name},e.name))}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-4",children:[(0,a.jsx)(m(),{href:"/cart",className:"relative",children:(0,a.jsxs)(x.$,{variant:"ghost",size:"icon",className:"rounded-full hover:bg-gray-100",children:[(0,a.jsx)(r.A,{className:"w-5 h-5"}),d>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:d>99?"99+":d})]})}),t?(0,a.jsxs)(j.rI,{children:[(0,a.jsx)(j.ty,{asChild:!0,children:(0,a.jsx)(x.$,{variant:"ghost",size:"icon",className:"rounded-full hover:bg-gray-100",children:(0,a.jsx)(i.A,{className:"w-5 h-5"})})}),(0,a.jsxs)(j.SQ,{align:"end",className:"w-48",children:[(0,a.jsx)("div",{className:"px-2 py-1.5 text-sm font-medium text-gray-900",children:null==e?void 0:e.fullName}),(0,a.jsx)("div",{className:"px-2 py-1.5 text-xs text-gray-500",children:null==e?void 0:e.email}),(0,a.jsx)(j.mB,{}),(0,a.jsxs)(j._2,{className:"cursor-pointer py-2",children:[(0,a.jsx)(n.A,{className:"w-4 h-4 mr-2"}),(0,a.jsx)(m(),{href:"/profile",className:"flex w-full",children:"Profile"})]}),(0,a.jsxs)(j._2,{className:"cursor-pointer py-2",children:[(0,a.jsx)(l.A,{className:"w-4 h-4 mr-2"}),(0,a.jsx)(m(),{href:"/orders",className:"flex w-full",children:"Orders"})]}),(0,a.jsx)(j.mB,{}),(0,a.jsxs)(j._2,{className:"cursor-pointer py-2 text-destructive focus:text-destructive",onClick:h,children:[(0,a.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Logout"]})]})]}):(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(m(),{href:"/signin",children:(0,a.jsx)(x.$,{variant:"ghost",size:"sm",className:"text-sm font-medium",children:"Sign In"})}),(0,a.jsx)(m(),{href:"/signup",children:(0,a.jsx)(x.$,{size:"sm",className:"text-sm font-medium",children:"Sign Up"})})]}),(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsxs)(p,{children:[(0,a.jsx)(g,{asChild:!0,children:(0,a.jsx)(x.$,{variant:"ghost",size:"icon",className:"rounded-full hover:bg-gray-100",children:(0,a.jsx)(c.A,{className:"w-5 h-5"})})}),(0,a.jsxs)(b,{side:"left",className:"w-[250px]",children:[(0,a.jsx)(m(),{href:"/",className:"flex items-center py-6 text-xl font-bold text-primary",children:"Mega Mall"}),(0,a.jsxs)("div",{className:"flex flex-col space-y-6 mt-6",children:[k.map(e=>(0,a.jsx)(m(),{href:e.href,className:"text-base font-medium text-gray-700 hover:text-primary transition-colors",children:e.name},e.name)),(0,a.jsxs)(m(),{href:"/cart",className:"flex items-center justify-between text-base font-medium text-gray-700 hover:text-primary transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(r.A,{className:"w-4 h-4"}),"Cart"]}),d>0&&(0,a.jsx)("span",{className:"bg-primary text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:d>99?"99+":d})]}),(0,a.jsx)("div",{className:"h-px bg-gray-200 my-2"}),t?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 mb-2",children:null==e?void 0:e.fullName}),(0,a.jsx)(m(),{href:"/profile",className:"text-base font-medium text-gray-700 hover:text-primary",children:"Profile"}),(0,a.jsx)(m(),{href:"/orders",className:"text-base font-medium text-gray-700 hover:text-primary",children:"Orders"}),(0,a.jsx)("button",{onClick:h,className:"text-base font-medium text-destructive text-left w-full",children:"Logout"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m(),{href:"/signin",className:"text-base font-medium text-gray-700 hover:text-primary",children:"Sign In"}),(0,a.jsx)(m(),{href:"/signup",className:"text-base font-medium text-primary hover:text-primary/80",children:"Sign Up"})]})]})]})]})})]})]})})}},4838:(e,t,s)=>{"use strict";s.d(t,{SQ:()=>o,_2:()=>c,mB:()=>d,rI:()=>n,ty:()=>l});var a=s(5155);s(2115);var r=s(9449),i=s(9434);function n(e){let{...t}=e;return(0,a.jsx)(r.bL,{"data-slot":"dropdown-menu",...t})}function l(e){let{...t}=e;return(0,a.jsx)(r.l9,{"data-slot":"dropdown-menu-trigger",...t})}function o(e){let{className:t,sideOffset:s=4,...n}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsx)(r.UC,{"data-slot":"dropdown-menu-content",sideOffset:s,className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...n})})}function c(e){let{className:t,inset:s,variant:n="default",...l}=e;return(0,a.jsx)(r.q7,{"data-slot":"dropdown-menu-item","data-inset":s,"data-variant":n,className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l})}function d(e){let{className:t,...s}=e;return(0,a.jsx)(r.wv,{"data-slot":"dropdown-menu-separator",className:(0,i.cn)("bg-border -mx-1 my-1 h-px",t),...s})}},5323:(e,t,s)=>{"use strict";s.d(t,{CartProvider:()=>d,_:()=>c});var a=s(5155),r=s(2115),i=s(5654),n=s(283),l=s(8543);let o=(0,r.createContext)(void 0),c=()=>{let e=(0,r.useContext)(o);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},d=e=>{let{children:t}=e,[s,c]=(0,r.useState)(null),[d,m]=(0,r.useState)(!1),[x,h]=(0,r.useState)(!1),{isAuthenticated:u,user:f}=(0,n.A)(),p=(null==s?void 0:s.length)||0;(0,r.useEffect)(()=>{u&&f?g():c(null)},[u,f,g]);let g=async()=>{if(u)try{m(!0);let e=await i.CV.getCartItems();e.success&&e.data&&c(e.data)}catch(e){c(null)}finally{m(!1)}},v=async(e,t)=>{if(!u)return l.oR.error("Please sign in to add items to cart"),!1;try{h(!0);let s=await i.CV.addToCart({productId:e,quantity:t});if(s.success&&s.data)return c(s.data),l.oR.success("Item added to cart successfully"),!0;return!1}catch(e){var s;return l.oR.error((null==(s=e.response)?void 0:s.data.message)||"Failed to add item to cart"),!1}finally{h(!1)}},y=async(e,t)=>{if(!u||t<1)return!1;try{h(!0);let s=await i.CV.updateQuantity({productId:e,quantity:t});if(s.success&&s.data)return l.oR.success("Quantity updated successfully"),c(s.data),!0;return!1}catch(e){var s;return l.oR.error((null==(s=e.response)?void 0:s.data.message)||"Failed to update quantity"),!1}finally{h(!1)}},b=async e=>{if(!u)return!1;try{h(!0);let t=await i.CV.removeProduct(e);if(t.success&&t.data)return c(t.data),l.oR.success("Item removed from cart"),!0;return!1}catch(e){var t;return l.oR.error((null==(t=e.response)?void 0:t.data.message)||"Failed to remove item"),!1}finally{h(!1)}};return(0,a.jsx)(o.Provider,{value:{cart:s,isLoading:d,isUpdating:x,itemCount:p,addToCart:v,updateQuantity:y,removeFromCart:b,clearCart:()=>{c(null)},refreshCart:g,getCartCalculations:()=>{if(!s)return{subtotal:0,tax:0,shipping:0,total:0};let e=i.CV.calculateCartTotal(s),t=i.CV.calculateTax(e),a=i.CV.calculateShipping(e),r=e+t+a;return{subtotal:e,tax:t,shipping:a,total:r}}},children:t})}},6821:(e,t,s)=>{"use strict";s.d(t,{default:()=>j});var a=s(5155),r=s(6874),i=s.n(r),n=s(488),l=s(8175),o=s(5684),c=s(2925),d=s(9799),m=s(133),x=s(5525),h=s(1586),u=s(4516),f=s(9420),p=s(1264),g=s(285);let v={shop:[{name:"All Products",href:"/shop"},{name:"New Arrivals",href:"/shop"},{name:"Best Sellers",href:"/"},{name:"Sale",href:"/shop"},{name:"Categories",href:"/"}],company:[{name:"About Us",href:"/about"},{name:"Careers",href:"/"},{name:"Press",href:"/"},{name:"Blog",href:"/"},{name:"Sustainability",href:"/"}],support:[{name:"Contact Us",href:"/contact"},{name:"FAQ",href:"/"},{name:"Size Guide",href:"/"},{name:"Shipping Info",href:"/"},{name:"Returns",href:"/orders"}],legal:[{name:"Privacy Policy",href:"/"},{name:"Terms of Service",href:"/"},{name:"Cookie Policy",href:"/"},{name:"Accessibility",href:"/"}]},y=[{name:"Facebook",href:"#",icon:n.A},{name:"Twitter",href:"#",icon:l.A},{name:"Instagram",href:"#",icon:o.A},{name:"YouTube",href:"#",icon:c.A}],b=[{icon:d.A,title:"Free Shipping",description:"On orders over $50"},{icon:m.A,title:"Easy Returns",description:"30-day return policy"},{icon:x.A,title:"Secure Payment",description:"SSL encrypted checkout"},{icon:h.A,title:"Multiple Payment",description:"Various payment options"}];function j(){return(0,a.jsxs)("footer",{className:"bg-gray-50 border-t border-gray-200",children:[(0,a.jsx)("div",{className:"border-b border-gray-200 bg-white",children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:b.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-3 text-center sm:text-left",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center",children:(0,a.jsx)(e.icon,{className:"w-6 h-6 text-primary"})})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]})]},t))})})}),(0,a.jsx)("div",{className:"bg-primary text-white",children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-12",children:(0,a.jsxs)("div",{className:"max-w-2xl mx-auto text-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"Stay in the loop"}),(0,a.jsx)("p",{className:"text-primary-foreground/80 mb-6",children:"Subscribe to our newsletter for exclusive deals, new arrivals, and style tips."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 max-w-md mx-auto",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("input",{type:"email",placeholder:"Enter your email",className:"w-full px-4 py-3 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white/20"})}),(0,a.jsx)(g.$,{variant:"secondary",className:"px-6 py-3 bg-white text-primary hover:bg-gray-100 font-semibold",children:"Subscribe"})]})]})})}),(0,a.jsx)("div",{className:"container mx-auto px-4 py-12",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[(0,a.jsx)(i(),{href:"/",className:"text-2xl font-bold text-primary mb-4 block",children:"Mega Mall"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6 max-w-sm",children:"Your one-stop destination for quality products at unbeatable prices. Discover the latest trends and timeless classics."}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 text-gray-600",children:[(0,a.jsx)(u.A,{className:"w-4 h-4 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-sm",children:"123 Shopping Street, City, State 12345"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 text-gray-600",children:[(0,a.jsx)(f.A,{className:"w-4 h-4 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-sm",children:"+1 (555) 123-4567"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 text-gray-600",children:[(0,a.jsx)(p.A,{className:"w-4 h-4 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-sm",children:"<EMAIL>"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-4",children:"Shop"}),(0,a.jsx)("ul",{className:"space-y-3",children:v.shop.map(e=>(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:e.href,className:"text-gray-600 hover:text-primary transition-colors text-sm",children:e.name})},e.name))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-4",children:"Company"}),(0,a.jsx)("ul",{className:"space-y-3",children:v.company.map(e=>(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:e.href,className:"text-gray-600 hover:text-primary transition-colors text-sm",children:e.name})},e.name))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-4",children:"Support"}),(0,a.jsx)("ul",{className:"space-y-3",children:v.support.map(e=>(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:e.href,className:"text-gray-600 hover:text-primary transition-colors text-sm",children:e.name})},e.name))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-4",children:"Legal"}),(0,a.jsx)("ul",{className:"space-y-3",children:v.legal.map(e=>(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:e.href,className:"text-gray-600 hover:text-primary transition-colors text-sm",children:e.name})},e.name))})]})]})}),(0,a.jsx)("div",{className:"border-t border-gray-200 bg-white",children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["\xa9 ",new Date().getFullYear()," Mega Mall. All rights reserved."]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600 mr-2",children:"Follow us:"}),y.map(e=>(0,a.jsx)(i(),{href:e.href,className:"text-gray-400 hover:text-primary transition-colors","aria-label":e.name,children:(0,a.jsx)(e.icon,{className:"w-5 h-5"})},e.name))]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600 mr-2",children:"We accept:"}),(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:"w-8 h-5 bg-gray-200 rounded text-xs flex items-center justify-center font-bold text-gray-600",children:"VISA"}),(0,a.jsx)("div",{className:"w-8 h-5 bg-gray-200 rounded text-xs flex items-center justify-center font-bold text-gray-600",children:"MC"}),(0,a.jsx)("div",{className:"w-8 h-5 bg-gray-200 rounded text-xs flex items-center justify-center font-bold text-gray-600",children:"PP"})]})]})]})})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[949,7690,4277,9078,8543,6874,3888,9449,5567,7389,8441,1684,7358],()=>t(1107)),_N_E=e.O()}]);