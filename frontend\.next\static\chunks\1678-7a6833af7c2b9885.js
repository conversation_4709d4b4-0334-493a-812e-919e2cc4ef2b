"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1678],{283:(e,t,r)=>{r.d(t,{A:()=>n,AuthProvider:()=>l});var a=r(5155),s=r(2115),i=r(5654);let o=(0,s.createContext)(void 0),n=()=>{let e=(0,s.useContext)(o);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},l=e=>{let{children:t}=e,[r,n]=(0,s.useState)(null),[l,c]=(0,s.useState)(!0),d=!!r;(0,s.useEffect)(()=>{(async()=>{if(localStorage.getItem("token"))try{let e=await i.Dv.getUser();e.success&&e.data&&n(e.data)}catch(e){e.response&&(console.error("Error checking auth status:",e.response.data.message),localStorage.removeItem("token"))}c(!1)})()},[]);let u=async()=>{try{await i.Dv.logout()}catch(e){console.error("Error during logout:",e)}finally{localStorage.removeItem("token"),n(null)}},h=async()=>{try{let e=await i.Dv.getUser();e.success&&e.data&&n(e.data)}catch(e){console.error("Error refreshing user data:",e)}};return(0,a.jsx)(o.Provider,{value:{user:r,isLoading:l,isAuthenticated:d,login:(e,t)=>{localStorage.setItem("token",t),n(e)},logout:u,updateUser:e=>{r&&n({...r,...e})},refreshUser:h},children:t})}},285:(e,t,r)=>{r.d(t,{$:()=>l});var a=r(5155);r(2115);var s=r(9708),i=r(2085),o=r(9434);let n=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:i,asChild:l=!1,...c}=e,d=l?s.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,o.cn)(n({variant:r,size:i,className:t})),...c})}},2357:(e,t,r)=>{r.d(t,{default:()=>u});var a=r(5155),s=r(2115),i=r(285),o=r(6695),n=r(1243),l=r(3904),c=r(7340);class d extends s.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("Shop Error Boundary caught an error:",e,t)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,a.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center p-4",children:(0,a.jsx)(o.Zp,{className:"w-full max-w-lg",children:(0,a.jsxs)(o.Wu,{className:"flex flex-col items-center justify-center py-12 text-center space-y-6",children:[(0,a.jsx)("div",{className:"w-20 h-20 text-destructive",children:(0,a.jsx)(n.A,{className:"w-full h-full"})}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-foreground",children:"Something went wrong"}),(0,a.jsx)("p",{className:"text-muted-foreground max-w-md",children:"We encountered an unexpected error while loading the shop. This might be a temporary issue."}),!1]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,a.jsxs)(i.$,{onClick:this.handleRetry,className:"flex items-center gap-2",size:"lg",children:[(0,a.jsx)(l.A,{className:"w-4 h-4"}),"Try Again"]}),(0,a.jsxs)(i.$,{onClick:this.handleGoHome,variant:"outline",className:"flex items-center gap-2",size:"lg",children:[(0,a.jsx)(c.A,{className:"w-4 h-4"}),"Go Home"]})]})]})})}):this.props.children}constructor(e){super(e),this.handleRetry=()=>{this.setState({hasError:!1,error:void 0})},this.handleGoHome=()=>{window.location.href="/"},this.state={hasError:!1}}}let u=d},4236:(e,t,r)=>{r.d(t,{K:()=>f});var a=r(5155),s=r(2115),i=r(6695),o=r(285),n=r(6126),l=r(8564),c=r(2657),d=r(1976),u=r(7809),h=r(6408),g=r(6766),x=r(5323),m=r(8543),v=r(5695);let f=e=>{let t,{product:r,badge:f}=e,[A,p]=(0,s.useState)(1),[b,w]=(0,s.useState)(!1),[y,j]=(0,s.useState)(!0),{addToCart:N,isUpdating:C}=(0,x._)(),k=(0,v.useRouter)(),E=async()=>{await N(r._id,A)&&p(1)},S=0===r.stock,K=r.stock>0&&r.stock<=5;return(0,a.jsx)(h.P.div,{className:"group relative",onHoverStart:()=>w(!0),onHoverEnd:()=>w(!1),whileHover:{y:-4},transition:{type:"spring",stiffness:300,damping:30},children:(0,a.jsxs)(i.Zp,{className:"w-full h-full shadow-md hover:shadow-xl transition-all duration-300 border-0 bg-card overflow-hidden",children:[(0,a.jsxs)("div",{className:"relative aspect-square bg-muted overflow-hidden",children:[r.images&&r.images.length>0&&(0,a.jsx)(g.default,{src:r.images[0].url,alt:r.title,fill:!0,className:"object-cover transition-all duration-300 ".concat(b?"scale-110":"scale-100"," ").concat(y?"blur-sm":"blur-0"),onLoad:()=>j(!1),onError:()=>j(!1),sizes:"(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw",priority:!1,quality:85,placeholder:"blur",blurDataURL:"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="}),y&&(0,a.jsx)("div",{className:"absolute inset-0 bg-muted animate-pulse"}),(0,a.jsxs)("div",{className:"absolute top-3 left-3 flex flex-col gap-2",children:[f&&(0,a.jsx)(n.E,{className:"text-xs font-medium ".concat("Sale"===f?"bg-red-500 hover:bg-red-600":"New Arrival"===f?"bg-green-500 hover:bg-green-600":"bg-blue-500 hover:bg-blue-600"),children:f}),S&&(0,a.jsx)(n.E,{variant:"destructive",className:"text-xs",children:"Out of Stock"}),K&&!S&&(0,a.jsx)(n.E,{className:"bg-orange-500 hover:bg-orange-600 text-xs",children:"Low Stock"})]}),(0,a.jsxs)(h.P.div,{className:"absolute top-3 right-3 flex flex-col gap-2",initial:{opacity:0,x:20},animate:{opacity:+!!b,x:20*!b},transition:{duration:.2},children:[(0,a.jsx)(o.$,{size:"icon",variant:"secondary",className:"w-8 h-8 rounded-full bg-white/90 hover:bg-white shadow-md",onClick:()=>{setShowQuickView(!0),m.oR.info("Quick view feature coming soon!")},children:(0,a.jsx)(c.A,{className:"w-4 h-4"})}),(0,a.jsx)(o.$,{size:"icon",variant:"secondary",className:"w-8 h-8 rounded-full bg-white/90 hover:bg-white shadow-md",children:(0,a.jsx)(d.A,{className:"w-4 h-4"})})]}),(0,a.jsxs)(h.P.div,{className:"absolute bottom-3 left-3 right-3",initial:{opacity:0,y:20},animate:{opacity:+!!b,y:20*!b},transition:{duration:.2,delay:.1},children:[(0,a.jsxs)(o.$,{onClick:E,disabled:C||S,className:"w-full bg-primary/90 hover:bg-primary text-primary-foreground shadow-lg",size:"sm",children:[(0,a.jsx)(u.A,{className:"w-4 h-4 mr-2"}),C?"Adding...":S?"Out of Stock":"Quick Add"]}),(0,a.jsx)(o.$,{className:"w-full bg-accent/90 hover:bg-accent text-accent-foreground shadow-lg",size:"sm",onClick:()=>k.push("/product/".concat(r._id)),children:"View Product"})]})]}),(0,a.jsxs)(i.Wu,{className:"p-4 space-y-3",children:[(0,a.jsx)("div",{className:"text-xs text-muted-foreground uppercase tracking-wide",children:r.category}),(0,a.jsx)("h3",{className:"font-semibold text-sm leading-tight line-clamp-2 min-h-[2.5rem]",children:r.title}),r.averageRating&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"flex items-center",children:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=[],r=Math.floor(e),s=e%1!=0;for(let e=0;e<5;e++)e<r?t.push((0,a.jsx)(l.A,{className:"w-4 h-4 fill-yellow-400 text-yellow-400"},e)):e===r&&s?t.push((0,a.jsxs)("div",{className:"relative w-4 h-4",children:[(0,a.jsx)(l.A,{className:"w-4 h-4 text-gray-300 absolute"}),(0,a.jsx)("div",{className:"overflow-hidden w-1/2",children:(0,a.jsx)(l.A,{className:"w-4 h-4 fill-yellow-400 text-yellow-400"})})]},e)):t.push((0,a.jsx)(l.A,{className:"w-4 h-4 text-gray-300"},e));return t}(r.averageRating)}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:["(",r.averageRating.toFixed(1),")"]}),r.reviews&&(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:[r.reviews.length," review",1!==r.reviews.length?"s":""]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsx)("span",{className:"text-lg font-bold text-primary",children:(t=r.price,new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(t))})}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:r.stock>0?"".concat(r.stock," left"):"Out of stock"})]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground line-clamp-2",children:r.description})]})]})})}},5323:(e,t,r)=>{r.d(t,{CartProvider:()=>d,_:()=>c});var a=r(5155),s=r(2115),i=r(5654),o=r(283),n=r(8543);let l=(0,s.createContext)(void 0),c=()=>{let e=(0,s.useContext)(l);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},d=e=>{let{children:t}=e,[r,c]=(0,s.useState)(null),[d,u]=(0,s.useState)(!1),[h,g]=(0,s.useState)(!1),{isAuthenticated:x,user:m}=(0,o.A)(),v=(null==r?void 0:r.length)||0;(0,s.useEffect)(()=>{x&&m?f():c(null)},[x,m,f]);let f=(0,s.useCallback)(async()=>{if(x)try{u(!0);let e=await i.CV.getCartItems();e.success&&e.data&&c(e.data)}catch(e){c(null)}finally{u(!1)}},[x]),A=async(e,t)=>{if(!x)return n.oR.error("Please sign in to add items to cart"),!1;try{g(!0);let r=await i.CV.addToCart({productId:e,quantity:t});if(r.success&&r.data)return c(r.data),n.oR.success("Item added to cart successfully"),!0;return!1}catch(e){var r;return n.oR.error((null==(r=e.response)?void 0:r.data.message)||"Failed to add item to cart"),!1}finally{g(!1)}},p=async(e,t)=>{if(!x||t<1)return!1;try{g(!0);let r=await i.CV.updateQuantity({productId:e,quantity:t});if(r.success&&r.data)return n.oR.success("Quantity updated successfully"),c(r.data),!0;return!1}catch(e){var r;return n.oR.error((null==(r=e.response)?void 0:r.data.message)||"Failed to update quantity"),!1}finally{g(!1)}},b=async e=>{if(!x)return!1;try{g(!0);let t=await i.CV.removeProduct(e);if(t.success&&t.data)return c(t.data),n.oR.success("Item removed from cart"),!0;return!1}catch(e){var t;return n.oR.error((null==(t=e.response)?void 0:t.data.message)||"Failed to remove item"),!1}finally{g(!1)}};return(0,a.jsx)(l.Provider,{value:{cart:r,isLoading:d,isUpdating:h,itemCount:v,addToCart:A,updateQuantity:p,removeFromCart:b,clearCart:()=>{c(null)},refreshCart:f,getCartCalculations:()=>{if(!r)return{subtotal:0,tax:0,shipping:0,total:0};let e=i.CV.calculateCartTotal(r),t=i.CV.calculateTax(e),a=i.CV.calculateShipping(e),s=e+t+a;return{subtotal:e,tax:t,shipping:a,total:s}}},children:t})}},6126:(e,t,r)=>{r.d(t,{E:()=>l});var a=r(5155);r(2115);var s=r(9708),i=r(2085),o=r(9434);let n=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,asChild:i=!1,...l}=e,c=i?s.DX:"span";return(0,a.jsx)(c,{"data-slot":"badge",className:(0,o.cn)(n({variant:r}),t),...l})}},6695:(e,t,r)=>{r.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>n,Zp:()=>i,aR:()=>o,wL:()=>d});var a=r(5155);r(2115);var s=r(9434);function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r})}}}]);