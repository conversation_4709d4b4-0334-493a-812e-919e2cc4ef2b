(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6636],{283:(e,s,r)=>{"use strict";r.d(s,{A:()=>i,AuthProvider:()=>c});var t=r(5155),a=r(2115),l=r(5654);let n=(0,a.createContext)(void 0),i=()=>{let e=(0,a.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},c=e=>{let{children:s}=e,[r,i]=(0,a.useState)(null),[c,o]=(0,a.useState)(!0),d=!!r;(0,a.useEffect)(()=>{(async()=>{if(localStorage.getItem("token"))try{let e=await l.Dv.getUser();e.success&&e.data&&i(e.data)}catch(e){e.response&&(console.error("Error checking auth status:",e.response.data.message),localStorage.removeItem("token"))}o(!1)})()},[]);let u=async()=>{try{await l.Dv.logout()}catch(e){console.error("Error during logout:",e)}finally{localStorage.removeItem("token"),i(null)}},m=async()=>{try{let e=await l.Dv.getUser();e.success&&e.data&&i(e.data)}catch(e){console.error("Error refreshing user data:",e)}};return(0,t.jsx)(n.Provider,{value:{user:r,isLoading:c,isAuthenticated:d,login:(e,s)=>{localStorage.setItem("token",s),i(e)},logout:u,updateUser:e=>{r&&i({...r,...e})},refreshUser:m},children:s})}},285:(e,s,r)=>{"use strict";r.d(s,{$:()=>c});var t=r(5155);r(2115);var a=r(9708),l=r(2085),n=r(9434);let i=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:s,variant:r,size:l,asChild:c=!1,...o}=e,d=c?a.DX:"button";return(0,t.jsx)(d,{"data-slot":"button",className:(0,n.cn)(i({variant:r,size:l,className:s})),...o})}},2523:(e,s,r)=>{"use strict";r.d(s,{p:()=>l});var t=r(5155);r(2115);var a=r(9434);function l(e){let{className:s,type:r,...l}=e;return(0,t.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...l})}},5057:(e,s,r)=>{"use strict";r.d(s,{J:()=>n});var t=r(5155);r(2115);var a=r(968),l=r(9434);function n(e){let{className:s,...r}=e;return(0,t.jsx)(a.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...r})}},5332:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>et});var t=r(5155),a=r(2115),l=r(283),n=r(9053),i=r(6695),c=r(285),o=r(1007),d=r(1264),u=r(5525),m=r(9074),x=r(7108),h=r(7550),p=r(4355),j=r(1154),f=r(4416),g=r(9869),v=r(5654),y=r(8543),b=r(6766);function N(e){let{currentAvatar:s,onAvatarUpdate:r,className:n=""}=e,[o,d]=(0,a.useState)(!1),[u,m]=(0,a.useState)(null),[x,h]=(0,a.useState)(!1),N=(0,a.useRef)(null),{updateUser:w}=(0,l.A)(),A=e=>{if(!e.type.startsWith("image/"))return void y.oR.error("Please select a valid image file");if(e.size>5242880)return void y.oR.error("File size must be less than 5MB");let s=new FileReader;s.onload=e=>{var s;(null==(s=e.target)?void 0:s.result)&&m(e.target.result)},s.readAsDataURL(e),P(e)},P=async e=>{d(!0);try{let s=await v.Dv.updateAvatar(e);if(s.success&&s.data)w(s.data),s.data.avatar&&(null==r||r(s.data.avatar)),y.oR.success("Profile picture updated successfully!"),m(null);else throw Error(s.message||"Failed to update avatar")}catch(e){console.error("Error uploading avatar:",e),e instanceof Error?y.oR.error(e.message||"Failed to update profile picture. Please try again."):y.oR.error("Failed to update profile picture. Please try again."),m(null)}finally{d(!1)}},C=u||(s&&s.length>0?s:null);return(0,t.jsxs)("div",{className:"relative ".concat(n),children:[(0,t.jsx)(i.Zp,{className:"overflow-hidden",children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)("div",{className:"\n                  relative w-32 h-32 rounded-full overflow-hidden border-4 border-gray-200 \n                  ".concat(x?"border-blue-400 bg-blue-50":"","\n                  ").concat(o?"opacity-50":"","\n                  transition-all duration-200\n                "),onDrop:e=>{e.preventDefault(),e.stopPropagation(),h(!1);let s=Array.from(e.dataTransfer.files);s.length>0&&A(s[0])},onDragOver:e=>{e.preventDefault(),e.stopPropagation(),h(!0)},onDragLeave:e=>{e.preventDefault(),e.stopPropagation(),h(!1)},children:[C?(0,t.jsx)(b.default,{src:C,width:32,height:32,alt:"Profile",className:"w-full h-full object-cover",onError:e=>{e.currentTarget.onerror=null,e.currentTarget.src="",m(null)}}):(0,t.jsx)("div",{className:"w-full h-full bg-gray-100 flex items-center justify-center",children:(0,t.jsx)(p.A,{className:"w-8 h-8 text-gray-400"})}),o&&(0,t.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center",children:(0,t.jsx)(j.A,{className:"w-6 h-6 text-white animate-spin"})})]}),u&&!o&&(0,t.jsx)(c.$,{size:"icon",variant:"destructive",className:"absolute -top-2 -right-2 w-6 h-6 rounded-full",onClick:()=>{m(null)},"aria-label":"Clear preview",title:"Clear preview",children:(0,t.jsx)(f.A,{className:"w-3 h-3"})})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-1",children:"Profile Picture"}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mb-3",children:"JPG, PNG or GIF. Max size 5MB."}),(0,t.jsx)(c.$,{onClick:()=>{var e;null==(e=N.current)||e.click()},disabled:o,variant:"outline",size:"sm",className:"w-full","aria-label":"Upload profile picture",title:"Upload profile picture",children:o?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Uploading..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"Choose Photo"]})})]}),(0,t.jsx)("p",{className:"text-xs text-gray-400 text-center",children:"or drag and drop a photo here"})]})})}),(0,t.jsx)("input",{name:"avatar",ref:N,type:"file",accept:"image/*",onChange:e=>{let s=Array.from(e.target.files||[]);s.length>0&&(A(s[0]),e.target&&(e.target.value=""))},className:"hidden"})]})}var w=r(2177),A=r(8778),P=r(1153),C=r(4516),k=r(4229),S=r(2523),D=r(9708),E=r(9434),R=r(5057);let I=w.Op,F=a.createContext({}),z=e=>{let{...s}=e;return(0,t.jsx)(F.Provider,{value:{name:s.name},children:(0,t.jsx)(w.xI,{...s})})},B=()=>{let e=a.useContext(F),s=a.useContext(V),{getFieldState:r}=(0,w.xW)(),t=(0,w.lN)({name:e.name}),l=r(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=s;return{id:n,name:e.name,formItemId:"".concat(n,"-form-item"),formDescriptionId:"".concat(n,"-form-item-description"),formMessageId:"".concat(n,"-form-item-message"),...l}},V=a.createContext({});function $(e){let{className:s,...r}=e,l=a.useId();return(0,t.jsx)(V.Provider,{value:{id:l},children:(0,t.jsx)("div",{"data-slot":"form-item",className:(0,E.cn)("grid gap-2",s),...r})})}function M(e){let{className:s,...r}=e,{error:a,formItemId:l}=B();return(0,t.jsx)(R.J,{"data-slot":"form-label","data-error":!!a,className:(0,E.cn)("data-[error=true]:text-destructive",s),htmlFor:l,...r})}function U(e){let{...s}=e,{error:r,formItemId:a,formDescriptionId:l,formMessageId:n}=B();return(0,t.jsx)(D.DX,{"data-slot":"form-control",id:a,"aria-describedby":r?"".concat(l," ").concat(n):"".concat(l),"aria-invalid":!!r,...s})}function O(e){let{className:s,...r}=e,{formDescriptionId:a}=B();return(0,t.jsx)("p",{"data-slot":"form-description",id:a,className:(0,E.cn)("text-muted-foreground text-sm",s),...r})}function Z(e){var s;let{className:r,...a}=e,{error:l,formMessageId:n}=B(),i=l?String(null!=(s=null==l?void 0:l.message)?s:""):a.children;return i?(0,t.jsx)("p",{"data-slot":"form-message",id:n,className:(0,E.cn)("text-destructive text-sm",r),...a,children:i}):null}let Y=P.Ik({fullName:P.Yj().min(2,"Full name must be at least 2 characters"),phoneNumber:P.Yj().optional(),dateOfBirth:P.Yj().optional(),gender:P.k5(["male","female","other","prefer-not-to-say"]).optional(),address:P.Ik({street:P.Yj().optional(),city:P.Yj().optional(),state:P.Yj().optional(),zipCode:P.Yj().optional(),country:P.Yj().optional()}).optional()});function _(e){var s,r,n,d,u;let{user:m,onUpdate:x}=e,[h,p]=(0,a.useState)(!1),{updateUser:f}=(0,l.A)(),g=(0,w.mN)({resolver:(0,A.u)(Y),defaultValues:{fullName:m.fullName||"",phoneNumber:m.phoneNumber||"",dateOfBirth:m.dateOfBirth||"",gender:m.gender||void 0,address:{street:(null==(s=m.address)?void 0:s.street)||"",city:(null==(r=m.address)?void 0:r.city)||"",state:(null==(n=m.address)?void 0:n.state)||"",zipCode:(null==(d=m.address)?void 0:d.zipCode)||"",country:(null==(u=m.address)?void 0:u.country)||""}}}),b=async e=>{p(!0),console.log(e);try{let s=await v.Dv.updatePersonalInfo(e);s.success&&s.data&&(f(s.data),null==x||x(s.data),y.oR.success("Personal information updated successfully!"))}catch(e){var s;y.oR.error((null==(s=e.response)?void 0:s.data.message)||"Failed to update personal information. Please try again.")}finally{p(!1)}};return(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsxs)(i.ZB,{className:"flex items-center",children:[(0,t.jsx)(o.A,{className:"w-5 h-5 mr-2"}),"Personal Information"]}),(0,t.jsx)(i.BT,{children:"Update your personal details and contact information"})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)(I,{...g,children:(0,t.jsxs)("form",{onSubmit:g.handleSubmit(b),className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsx)(z,{control:g.control,name:"fullName",render:e=>{let{field:s}=e;return(0,t.jsxs)($,{children:[(0,t.jsx)(M,{children:"Full Name *"}),(0,t.jsx)(U,{children:(0,t.jsx)(S.p,{placeholder:"Enter your full name",...s})}),(0,t.jsx)(Z,{})]})}}),(0,t.jsx)(z,{control:g.control,name:"phoneNumber",render:e=>{let{field:s}=e;return(0,t.jsxs)($,{children:[(0,t.jsx)(M,{children:"Phone Number"}),(0,t.jsx)(U,{children:(0,t.jsx)(S.p,{placeholder:"Enter your phone number",...s})}),(0,t.jsx)(Z,{})]})}}),(0,t.jsx)(z,{control:g.control,name:"dateOfBirth",render:e=>{let{field:s}=e;return(0,t.jsxs)($,{children:[(0,t.jsx)(M,{children:"Date of Birth"}),(0,t.jsx)(U,{children:(0,t.jsx)(S.p,{type:"text",placeholder:"MM/DD/YYYY",...s,maxLength:10,onChange:e=>{let r=e.target.value.replace(/[^\d]/g,"");r.length>=3&&r.length<=4?r=r.slice(0,2)+"/"+r.slice(2):r.length>4&&r.length<=8&&(r=r.slice(0,2)+"/"+r.slice(2,4)+"/"+r.slice(4)),s.onChange(r)}})}),(0,t.jsx)(Z,{})]})}}),(0,t.jsx)(z,{control:g.control,name:"gender",render:e=>{let{field:s}=e;return(0,t.jsxs)($,{children:[(0,t.jsx)(M,{children:"Gender"}),(0,t.jsx)(U,{children:(0,t.jsxs)("select",{...s,className:"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-xs transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",children:[(0,t.jsx)("option",{value:"",children:"Select gender"}),(0,t.jsx)("option",{value:"male",children:"Male"}),(0,t.jsx)("option",{value:"female",children:"Female"}),(0,t.jsx)("option",{value:"other",children:"Other"}),(0,t.jsx)("option",{value:"prefer-not-to-say",children:"Prefer not to say"})]})}),(0,t.jsx)(Z,{})]})}})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(C.A,{className:"w-4 h-4 mr-2 text-gray-500"}),(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Address"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,t.jsx)(z,{control:g.control,name:"address.street",render:e=>{let{field:s}=e;return(0,t.jsxs)($,{children:[(0,t.jsx)(M,{children:"Street Address"}),(0,t.jsx)(U,{children:(0,t.jsx)(S.p,{placeholder:"Enter your street address",...s})}),(0,t.jsx)(Z,{})]})}}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsx)(z,{control:g.control,name:"address.city",render:e=>{let{field:s}=e;return(0,t.jsxs)($,{children:[(0,t.jsx)(M,{children:"City"}),(0,t.jsx)(U,{children:(0,t.jsx)(S.p,{placeholder:"Enter your city",...s})}),(0,t.jsx)(Z,{})]})}}),(0,t.jsx)(z,{control:g.control,name:"address.state",render:e=>{let{field:s}=e;return(0,t.jsxs)($,{children:[(0,t.jsx)(M,{children:"State/Province"}),(0,t.jsx)(U,{children:(0,t.jsx)(S.p,{placeholder:"Enter your state",...s})}),(0,t.jsx)(Z,{})]})}})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsx)(z,{control:g.control,name:"address.zipCode",render:e=>{let{field:s}=e;return(0,t.jsxs)($,{children:[(0,t.jsx)(M,{children:"ZIP/Postal Code"}),(0,t.jsx)(U,{children:(0,t.jsx)(S.p,{placeholder:"Enter your ZIP code",...s})}),(0,t.jsx)(Z,{})]})}}),(0,t.jsx)(z,{control:g.control,name:"address.country",render:e=>{let{field:s}=e;return(0,t.jsxs)($,{children:[(0,t.jsx)(M,{children:"Country"}),(0,t.jsx)(U,{children:(0,t.jsx)(S.p,{placeholder:"Enter your country",...s})}),(0,t.jsx)(Z,{})]})}})]})]})]}),(0,t.jsx)("div",{className:"flex justify-end pt-4 border-t",children:(0,t.jsx)(c.$,{type:"submit",disabled:h,children:h?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Saving..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(k.A,{className:"w-4 h-4 mr-2"}),"Save Changes"]})})})]})})})]})}var T=r(8749),W=r(2657),L=r(1243),Q=r(5695);let q=P.Ik({currentPassword:P.Yj().min(1,"Current password is required"),newPassword:P.Yj().min(8,"Password must be at least 8 characters"),confirmPassword:P.Yj().min(1,"Please confirm your password")}).refine(e=>e.newPassword===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]}),G=P.Ik({newEmail:P.Yj().email("Please enter a valid email address")});function H(e){let{user:s}=e,[r,l]=(0,a.useState)(!1),[n,o]=(0,a.useState)(!1),[m,x]=(0,a.useState)(!1),[h,p]=(0,a.useState)(!1),[f,g]=(0,a.useState)(!1),b=(0,Q.useRouter)(),N=(0,w.mN)({resolver:(0,A.u)(q),defaultValues:{currentPassword:"",newPassword:"",confirmPassword:""}}),P=(0,w.mN)({resolver:(0,A.u)(G),defaultValues:{newEmail:""}}),C=async e=>{l(!0);try{let s=await v.Dv.updatePassword({currentPassword:e.currentPassword,newPassword:e.newPassword});if(s.success)y.oR.success("Password updated successfully!"),N.reset();else throw Error(s.message||"Failed to update password")}catch(e){console.error("Error updating password:",e),y.oR.error("Failed to update password. Please check your current password and try again.")}finally{l(!1)}},D=async e=>{o(!0);try{let r=await v.Dv.emailVerification({newEmail:e.newEmail});console.log(r),r.success&&(y.oR.success("Verification email sent! Please check your new email address."),P.reset(),b.push("/otpVerification/".concat(null==s?void 0:s._id,"/").concat(e.newEmail)))}catch(e){var r,t;y.oR.error((null==(t=e.response)||null==(r=t.data)?void 0:r.message)||"Failed to send verification email"),P.reset()}finally{o(!1)}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsxs)(i.ZB,{className:"flex items-center",children:[(0,t.jsx)(u.A,{className:"w-5 h-5 mr-2"}),"Change Password"]}),(0,t.jsx)(i.BT,{children:"Update your password to keep your account secure"})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)(I,{...N,children:(0,t.jsxs)("form",{onSubmit:N.handleSubmit(C),className:"space-y-4",children:[(0,t.jsx)(z,{control:N.control,name:"currentPassword",render:e=>{let{field:s}=e;return(0,t.jsxs)($,{children:[(0,t.jsx)(M,{children:"Current Password"}),(0,t.jsx)(U,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(S.p,{type:m?"text":"password",placeholder:"Enter your current password",...s}),(0,t.jsx)(c.$,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>x(!m),children:m?(0,t.jsx)(T.A,{className:"h-4 w-4"}):(0,t.jsx)(W.A,{className:"h-4 w-4"})})]})}),(0,t.jsx)(Z,{})]})}}),(0,t.jsx)(z,{control:N.control,name:"newPassword",render:e=>{let{field:s}=e;return(0,t.jsxs)($,{children:[(0,t.jsx)(M,{children:"New Password"}),(0,t.jsx)(U,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(S.p,{type:h?"text":"password",placeholder:"Enter your new password",...s}),(0,t.jsx)(c.$,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>p(!h),children:h?(0,t.jsx)(T.A,{className:"h-4 w-4"}):(0,t.jsx)(W.A,{className:"h-4 w-4"})})]})}),(0,t.jsx)(Z,{})]})}}),(0,t.jsx)(z,{control:N.control,name:"confirmPassword",render:e=>{let{field:s}=e;return(0,t.jsxs)($,{children:[(0,t.jsx)(M,{children:"Confirm New Password"}),(0,t.jsx)(U,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(S.p,{type:f?"text":"password",placeholder:"Confirm your new password",...s}),(0,t.jsx)(c.$,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>g(!f),children:f?(0,t.jsx)(T.A,{className:"h-4 w-4"}):(0,t.jsx)(W.A,{className:"h-4 w-4"})})]})}),(0,t.jsx)(Z,{})]})}}),(0,t.jsx)("div",{className:"flex justify-end pt-4 border-t",children:(0,t.jsx)(c.$,{type:"submit",disabled:r,children:r?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Updating..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(k.A,{className:"w-4 h-4 mr-2"}),"Update Password"]})})})]})})})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsxs)(i.ZB,{className:"flex items-center",children:[(0,t.jsx)(d.A,{className:"w-5 h-5 mr-2"}),"Change Email Address"]}),(0,t.jsx)(i.BT,{children:"Update your email address. You will need to verify the new email."})]}),(0,t.jsxs)(i.Wu,{children:[(0,t.jsx)("div",{className:"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(L.A,{className:"w-4 h-4 text-blue-600 mt-0.5 mr-2 flex-shrink-0"}),(0,t.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,t.jsxs)("p",{className:"font-medium",children:["Current email: ",s.email]}),(0,t.jsx)("p",{className:"mt-1",children:"Changing your email will require verification. You will receive a verification link at your new email address."})]})]})}),(0,t.jsx)(I,{...P,children:(0,t.jsxs)("form",{onSubmit:P.handleSubmit(D),className:"space-y-4",children:[(0,t.jsx)(z,{control:P.control,name:"newEmail",render:e=>{let{field:s}=e;return(0,t.jsxs)($,{children:[(0,t.jsx)(M,{children:"New Email Address"}),(0,t.jsx)(U,{children:(0,t.jsx)(S.p,{type:"email",placeholder:"Enter your new email address",...s})}),(0,t.jsx)(Z,{})]})}}),(0,t.jsx)("div",{className:"flex justify-end pt-4 border-t",children:(0,t.jsx)(c.$,{type:"submit",disabled:n,children:n?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Sending..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"Send Verification Email"]})})})]})})]})]})]})}var J=r(3861),X=r(2525);let K=P.Ik({notifications:P.Ik({email:P.zM().optional(),sms:P.zM().optional(),push:P.zM().optional(),marketing:P.zM().optional()}).optional(),privacy:P.Ik({profileVisibility:P.k5(["public","private"]).optional(),showEmail:P.zM().optional(),showPhone:P.zM().optional()}).optional()});function ee(e){var s,r,n,o,d,u,m,x,h,p,f,g,b,N,P,C,S,D,E,R,F;let{user:B,onUpdate:V}=e,[Z,Y]=(0,a.useState)(!1),[_,T]=(0,a.useState)(!1),[Q,q]=(0,a.useState)(""),[G,H]=(0,a.useState)(!1),{updateUser:ee,logout:es}=(0,l.A)(),er=(0,w.mN)({resolver:(0,A.u)(K),defaultValues:{notifications:{email:null==(P=null==(r=B.preferences)||null==(s=r.notifications)?void 0:s.email)||P,sms:null!=(C=null==(o=B.preferences)||null==(n=o.notifications)?void 0:n.sms)&&C,push:null==(S=null==(u=B.preferences)||null==(d=u.notifications)?void 0:d.push)||S,marketing:null!=(D=null==(x=B.preferences)||null==(m=x.notifications)?void 0:m.marketing)&&D},privacy:{profileVisibility:null!=(E=null==(p=B.preferences)||null==(h=p.privacy)?void 0:h.profileVisibility)?E:"public",showEmail:null!=(R=null==(g=B.preferences)||null==(f=g.privacy)?void 0:f.showEmail)&&R,showPhone:null!=(F=null==(N=B.preferences)||null==(b=N.privacy)?void 0:b.showPhone)&&F}}}),et=async e=>{Y(!0);try{let s=await v.Dv.updatePreferences({preferences:e});console.log(s),s.success&&s.data&&(ee(s.data),null==V||V(s.data),y.oR.success("Account settings updated successfully!"))}catch(e){e.response?y.oR.error(e.response.data.message||"Failed to update settings"):y.oR.error("Failed to update account settings. Please try again.")}finally{Y(!1)}},ea=async()=>{if(!Q.trim())return void y.oR.error("Please enter your password to confirm account deletion");H(!0);try{(await v.Dv.deleteAccount(Q)).success&&(y.oR.success("Account deleted successfully"),es())}catch(e){e.response&&y.oR.error(e.response.data.message||"Failed to delete account")}finally{H(!1),T(!1),q("")}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsxs)(i.ZB,{className:"flex items-center",children:[(0,t.jsx)(J.A,{className:"w-5 h-5 mr-2"}),"Notification Preferences"]}),(0,t.jsx)(i.BT,{children:"Choose how you want to receive notifications"})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)(I,{...er,children:(0,t.jsxs)("form",{onSubmit:er.handleSubmit(et),className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(z,{control:er.control,name:"notifications.email",render:e=>{let{field:s}=e;return(0,t.jsxs)($,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsx)(M,{className:"text-base",children:"Email Notifications"}),(0,t.jsx)(O,{children:"Receive notifications via email"})]}),(0,t.jsx)(U,{children:(0,t.jsx)("input",{type:"checkbox",checked:s.value,onChange:s.onChange,className:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"})})]})}}),(0,t.jsx)(z,{control:er.control,name:"notifications.sms",render:e=>{let{field:s}=e;return(0,t.jsxs)($,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsx)(M,{className:"text-base",children:"SMS Notifications"}),(0,t.jsx)(O,{children:"Receive notifications via SMS"})]}),(0,t.jsx)(U,{children:(0,t.jsx)("input",{type:"checkbox",checked:s.value,onChange:s.onChange,className:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"})})]})}}),(0,t.jsx)(z,{control:er.control,name:"notifications.push",render:e=>{let{field:s}=e;return(0,t.jsxs)($,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsx)(M,{className:"text-base",children:"Push Notifications"}),(0,t.jsx)(O,{children:"Receive push notifications in your browser"})]}),(0,t.jsx)(U,{children:(0,t.jsx)("input",{type:"checkbox",checked:s.value,onChange:s.onChange,className:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"})})]})}}),(0,t.jsx)(z,{control:er.control,name:"notifications.marketing",render:e=>{let{field:s}=e;return(0,t.jsxs)($,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsx)(M,{className:"text-base",children:"Marketing Emails"}),(0,t.jsx)(O,{children:"Receive promotional emails and special offers"})]}),(0,t.jsx)(U,{children:(0,t.jsx)("input",{type:"checkbox",checked:s.value,onChange:s.onChange,className:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"})})]})}})]}),(0,t.jsxs)("div",{className:"space-y-4 pt-6 border-t",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(W.A,{className:"w-4 h-4 mr-2 text-gray-500"}),(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Privacy Settings"})]}),(0,t.jsx)(z,{control:er.control,name:"privacy.profileVisibility",render:e=>{let{field:s}=e;return(0,t.jsxs)($,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsx)(M,{className:"text-base",children:"Profile Visibility"}),(0,t.jsx)(O,{children:"Control who can see your profile"})]}),(0,t.jsx)(U,{children:(0,t.jsxs)("select",{...s,className:"flex h-9 w-32 rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-xs transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring",children:[(0,t.jsx)("option",{value:"public",children:"Public"}),(0,t.jsx)("option",{value:"private",children:"Private"})]})})]})}}),(0,t.jsx)(z,{control:er.control,name:"privacy.showEmail",render:e=>{let{field:s}=e;return(0,t.jsxs)($,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsx)(M,{className:"text-base",children:"Show Email"}),(0,t.jsx)(O,{children:"Display your email address on your profile"})]}),(0,t.jsx)(U,{children:(0,t.jsx)("input",{type:"checkbox",checked:s.value,onChange:s.onChange,className:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"})})]})}}),(0,t.jsx)(z,{control:er.control,name:"privacy.showPhone",render:e=>{let{field:s}=e;return(0,t.jsxs)($,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsx)(M,{className:"text-base",children:"Show Phone Number"}),(0,t.jsx)(O,{children:"Display your phone number on your profile"})]}),(0,t.jsx)(U,{children:(0,t.jsx)("input",{type:"checkbox",checked:s.value,onChange:s.onChange,className:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"})})]})}})]}),(0,t.jsx)("div",{className:"flex justify-end pt-4 border-t",children:(0,t.jsx)(c.$,{type:"submit",disabled:Z,children:Z?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Saving..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(k.A,{className:"w-4 h-4 mr-2"}),"Save Preferences"]})})})]})})})]}),(0,t.jsxs)(i.Zp,{className:"border-red-200",children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsxs)(i.ZB,{className:"flex items-center text-red-600",children:[(0,t.jsx)(X.A,{className:"w-5 h-5 mr-2"}),"Delete Account"]}),(0,t.jsx)(i.BT,{children:"Permanently delete your account and all associated data"})]}),(0,t.jsx)(i.Wu,{children:_?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-md",children:(0,t.jsx)("p",{className:"text-sm text-red-800 font-medium",children:"Please enter your password to confirm account deletion:"})}),(0,t.jsx)("input",{type:"password",placeholder:"Enter your password",value:Q,onChange:e=>q(e.target.value),className:"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-xs transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"}),(0,t.jsxs)("div",{className:"flex space-x-3",children:[(0,t.jsx)(c.$,{variant:"destructive",onClick:ea,disabled:G,children:G?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Deleting..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(X.A,{className:"w-4 h-4 mr-2"}),"Confirm Delete"]})}),(0,t.jsx)(c.$,{variant:"outline",onClick:()=>{T(!1),q("")},disabled:G,children:"Cancel"})]})]}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-md",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(L.A,{className:"w-4 h-4 text-red-600 mt-0.5 mr-2 flex-shrink-0"}),(0,t.jsxs)("div",{className:"text-sm text-red-800",children:[(0,t.jsx)("p",{className:"font-medium",children:"This action cannot be undone"}),(0,t.jsx)("p",{className:"mt-1",children:"Deleting your account will permanently remove all your data, including orders, wishlist, and personal information."})]})]})}),(0,t.jsxs)(c.$,{variant:"destructive",onClick:()=>T(!0),children:[(0,t.jsx)(X.A,{className:"w-4 h-4 mr-2"}),"Delete My Account"]})]})})]})]})}var es=r(9362);function er(e){let{className:s=""}=e,[r,l]=(0,a.useState)([]),[n,o]=(0,a.useState)(!0),[d,u]=(0,a.useState)("all"),[h,p]=(0,a.useState)("date"),[g,N]=(0,a.useState)("desc"),[w,A]=(0,a.useState)(null),[P,C]=(0,a.useState)(!1);(0,a.useEffect)(()=>{(async()=>{o(!0);try{let e=await v.Qo.getOrders();e.success&&e.data&&(l(e.data),y.oR.success(e.message))}catch(s){var e;y.oR.error((null==(e=s.response)?void 0:e.data.message)||"Failed to load order history. Please try again.")}finally{o(!1)}})()},[]);let k=async e=>{try{let s=await v.Qo.cancelOrder({orderId:e,reason:"Cancelled by user"});s.success?(y.oR.success(s.message||"Order cancelled successfully"),l(s=>s.filter(s=>s._id!==e))):y.oR.error(s.message||"Failed to cancel order")}catch(e){e instanceof es.pe&&y.oR.error(e.message||"Failed to cancel order")}},S=e=>{let s={pending:{color:"bg-yellow-100 text-yellow-800",text:"Pending"},shipped:{color:"bg-purple-100 text-purple-800",text:"Shipped"},delivered:{color:"bg-green-100 text-green-800",text:"Delivered"},cancelled:{color:"bg-red-100 text-red-800",text:"Cancelled"}},r=s[e]||s.pending;return(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(r.color),children:r.text})},D=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),E=e=>{let s=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e);return"+".concat(s)},R=r.filter(e=>"all"===d||e.status===d).sort((e,s)=>{let r="date"===h?new Date(e.createdAt).getTime():e.totalRevenue,t="date"===h?new Date(s.createdAt).getTime():s.totalRevenue;return"asc"===g?r-t:t-r});return n?(0,t.jsx)(i.Zp,{className:s,children:(0,t.jsxs)(i.Wu,{className:"flex orderItems-center justify-center py-12",children:[(0,t.jsx)(j.A,{className:"w-6 h-6 animate-spin mr-2"}),(0,t.jsx)("span",{children:"Loading order history..."})]})}):(0,t.jsxs)("div",{className:s,children:[(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsxs)(i.ZB,{className:"flex orderItems-center",children:[(0,t.jsx)(x.A,{className:"w-5 h-5 mr-2"}),"Order History"]}),(0,t.jsx)(i.BT,{children:"View and manage your order history"})]}),(0,t.jsxs)(i.Wu,{children:[(0,t.jsx)("div",{className:"flex flex-col sm:flex-row gap-4 mb-6",children:(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("select",{value:d,onChange:e=>u(e.target.value),className:"flex h-9 rounded-md border border-input bg-transparent px-3 py-1 text-sm",children:[(0,t.jsx)("option",{value:"all",children:"All Status"}),(0,t.jsx)("option",{value:"pending",children:"Pending"}),(0,t.jsx)("option",{value:"shipped",children:"Shipped"}),(0,t.jsx)("option",{value:"delivered",children:"Delivered"}),(0,t.jsx)("option",{value:"cancelled",children:"Cancelled"})]}),(0,t.jsxs)("select",{value:"".concat(h,"-").concat(g),onChange:e=>{let[s,r]=e.target.value.split("-");p(s),N(r)},className:"flex h-9 rounded-md border border-input bg-transparent px-3 py-1 text-sm",children:[(0,t.jsx)("option",{value:"date-desc",children:"Newest First"}),(0,t.jsx)("option",{value:"date-asc",children:"Oldest First"}),(0,t.jsx)("option",{value:"amount-desc",children:"Highest Amount"}),(0,t.jsx)("option",{value:"amount-asc",children:"Lowest Amount"})]})]})}),0===R.length?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(x.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No orders found"}),(0,t.jsx)("p",{className:"text-gray-500",children:"Try adjusting your search or filter criteria"})]}):(0,t.jsx)("div",{className:"space-y-4",children:R.map(e=>{var s,r,a,l,n;return(0,t.jsx)("div",{className:"border rounded-lg p-4 hover:bg-gray-50 transition-colors",children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:orderItems-center justify-between gap-4",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex orderItems-center gap-3 mb-2",children:[(0,t.jsxs)("h3",{className:"font-medium text-gray-900",children:["Order #",e._id.slice(-8).toUpperCase()]}),S(e.status)]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,t.jsxs)("div",{className:"flex orderItems-center",children:[(0,t.jsx)(m.A,{className:"w-4 h-4 mr-1"}),D(e.createdAt)]}),(0,t.jsxs)("div",{children:[null==(s=e.orderItems)?void 0:s.length," item",(null==(r=e.orderItems)?void 0:r.length)!==1?"s":""," • ",E(e.totalRevenue)]}),(0,t.jsxs)("div",{className:"text-xs",children:[null==(a=e.orderItems)?void 0:a.slice(0,2).map(e=>e.product.title).join(", "),(null==(l=e.orderItems)?void 0:l.length)>2&&" +".concat(e.orderItems.length-2," more")]})]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>{A(e),C(!0)},children:[(0,t.jsx)(W.A,{className:"w-4 h-4 mr-1"}),"View Details"]}),(null===v.Qo||void 0===v.Qo||null==(n=v.Qo.canCancelOrder)?void 0:n.call(v.Qo,e))&&(0,t.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>k(e._id),className:"text-red-600 hover:text-red-700",children:"Cancel"})]})]})},e._id)})})]})]}),P&&w&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex orderItems-center justify-center p-4 z-50",children:(0,t.jsx)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex orderItems-center justify-between mb-4",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold",children:["Order #",w._id.slice(-8).toUpperCase()]}),(0,t.jsx)(c.$,{variant:"ghost",size:"icon",onClick:()=>C(!1),children:(0,t.jsx)(f.A,{className:"w-4 h-4"})})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium mb-2",children:"Order Status"}),(0,t.jsxs)("div",{className:"flex orderItems-center gap-2",children:[S(w.status),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:["Ordered on ",D(w.createdAt)]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"font-medium mb-3",children:["orderItems (",w.orderItems.length,")"]}),(0,t.jsx)("div",{className:"space-y-3",children:w.orderItems.map((e,s)=>{var r;return(0,t.jsxs)("div",{className:"flex orderItems-center gap-3 p-3 border rounded-lg",children:[(null==(r=e.product.images)?void 0:r[0])&&(0,t.jsx)(b.default,{src:e.product.images[0].url,width:400,height:400,alt:e.product.title,className:"w-16 h-16 object-cover rounded"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h4",{className:"font-medium",children:e.product.title}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Quantity: ",e.quantity," \xd7 ",E(e.product.price)]})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsx)("p",{className:"font-medium",children:E(e.product.price*e.quantity)})})]},s)})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium mb-2",children:"Shipping Address"}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,t.jsx)("p",{children:w.shippingDetails[0].fullName}),(0,t.jsx)("p",{children:w.shippingDetails[0].address}),(0,t.jsxs)("p",{children:[w.shippingDetails[0].city,", ",w.shippingDetails[0].state," ",w.shippingDetails[0].postalCode]}),(0,t.jsx)("p",{children:w.shippingDetails[0].country}),(0,t.jsxs)("p",{children:["Phone: ",w.shippingDetails[0].phone]})]})]}),(0,t.jsxs)("div",{className:"flex justify-between orderItems-center",children:[(0,t.jsx)("span",{className:"font-medium",children:"COD Charges"}),(0,t.jsx)("span",{className:"text-lg font-semibold",children:"+$".concat(w.codCharges)})]}),(0,t.jsxs)("div",{className:"border-t pt-4",children:[(0,t.jsxs)("div",{className:"flex justify-between orderItems-center",children:[(0,t.jsx)("span",{className:"font-medium",children:"Total Amount"}),(0,t.jsx)("span",{className:"text-lg font-semibold",children:"$".concat(w.totalRevenue)})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 mt-1",children:["Payment Method: ",w.paymentMethod.toUpperCase()]})]})]})]})})})]})}function et(){let{user:e}=(0,l.A)(),[s,r]=(0,a.useState)("overview"),p=e=>{console.log("User updated:",e)},j=(0,a.useMemo)(()=>{switch(s){case"personal":return"Personal Information";case"security":return"Security Settings";case"orders":return"Order History";case"settings":return"Account Settings";default:return"Profile"}},[s]),f=(0,a.useMemo)(()=>{switch(s){case"personal":return"Update your personal details and contact information";case"security":return"Manage your password and email settings";case"orders":return"View and track your order history";case"settings":return"Configure your account preferences";default:return"Manage your account settings and preferences"}},[s]);return(0,t.jsx)(n.A,{children:(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 max-w-6xl",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)("div",{className:"flex flex-col gap-4 md:flex-row md:items-center md:justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl md:text-3xl font-bold text-gray-900",children:j}),(0,t.jsx)("p",{className:"text-gray-600 mt-1 text-sm md:text-base",children:f})]}),"overview"!==s&&(0,t.jsxs)(c.$,{variant:"outline",onClick:()=>r("overview"),className:"flex items-center w-fit",children:[(0,t.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Back to Overview"]})]})}),(0,t.jsx)("div",{className:"mb-6 overflow-x-auto",children:(0,t.jsx)("nav",{className:"flex gap-6 text-sm border-b pb-1",children:["overview","personal","security","orders","settings"].map(e=>(0,t.jsx)("button",{onClick:()=>r(e),className:"whitespace-nowrap border-b-2 transition-all duration-150 ".concat(s===e?"border-blue-500 text-blue-600 font-medium":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:e.charAt(0).toUpperCase()+e.slice(1)},e))})}),e&&(()=>{if(!e)return null;switch(s){case"personal":return(0,t.jsx)(_,{user:e,onUpdate:p});case"security":return(0,t.jsx)(H,{user:e});case"orders":return(0,t.jsx)(er,{});case"settings":return(0,t.jsx)(ee,{user:e,onUpdate:p});default:return(0,t.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-3 gap-6",children:[(0,t.jsx)("div",{className:"xl:col-span-2",children:(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsxs)(i.ZB,{className:"flex items-center",children:[(0,t.jsx)(o.A,{className:"w-5 h-5 mr-2"}),"Personal Information"]}),(0,t.jsx)(i.BT,{children:"Your account details and personal information"})]}),(0,t.jsxs)(i.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Full Name"}),(0,t.jsx)("p",{className:"mt-1 text-gray-900",children:e.fullName||"—"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Email Address"}),(0,t.jsxs)("p",{className:"mt-1 text-gray-900 flex items-center",children:[(0,t.jsx)(d.A,{className:"w-4 h-4 mr-2 text-gray-400"}),e.email]})]}),e.phoneNumber&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Phone Number"}),(0,t.jsx)("p",{className:"mt-1 text-gray-900",children:e.phoneNumber})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Account Status"}),(0,t.jsxs)("p",{className:"mt-1 flex items-center",children:[(0,t.jsx)(u.A,{className:"w-4 h-4 mr-2 text-gray-400"}),(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(e.isVerified?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:e.isVerified?"Verified":"Pending Verification"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Member Since"}),(0,t.jsxs)("p",{className:"mt-1 text-gray-900 flex items-center",children:[(0,t.jsx)(m.A,{className:"w-4 h-4 mr-2 text-gray-400"}),new Date(e.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})]})]})]}),(0,t.jsx)("div",{className:"pt-4 border-t",children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,t.jsx)(c.$,{variant:"outline",onClick:()=>r("personal"),children:"Edit Profile"}),(0,t.jsx)(c.$,{variant:"outline",onClick:()=>r("security"),children:"Security Settings"}),(0,t.jsx)(c.$,{variant:"outline",onClick:()=>r("settings"),children:"Account Settings"})]})})]})]})}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(N,{currentAvatar:e.avatar,onAvatarUpdate:p}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsx)(i.ZB,{children:"Quick Actions"})}),(0,t.jsxs)(i.Wu,{className:"space-y-3",children:[(0,t.jsxs)(c.$,{variant:"outline",className:"w-full justify-start",onClick:()=>r("orders"),children:[(0,t.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"View Orders"]}),(0,t.jsx)(c.$,{variant:"outline",className:"w-full justify-start",disabled:!0,children:"Wishlist"}),(0,t.jsx)(c.$,{variant:"outline",className:"w-full justify-start",disabled:!0,children:"Address Book"}),(0,t.jsx)(c.$,{variant:"outline",className:"w-full justify-start",disabled:!0,children:"Payment Methods"})]})]})]})]})}})()]})})})}},6253:(e,s,r)=>{Promise.resolve().then(r.bind(r,5332))},6695:(e,s,r)=>{"use strict";r.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>i,Zp:()=>l,aR:()=>n,wL:()=>d});var t=r(5155);r(2115);var a=r(9434);function l(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...r})}function n(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...r})}function i(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",s),...r})}function c(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",s),...r})}function o(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",s),...r})}function d(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",s),...r})}},7023:(e,s,r)=>{"use strict";r.d(s,{AV:()=>l});var t=r(5155);r(2115);let a=e=>{let{size:s="md",className:r=""}=e;return(0,t.jsx)("div",{className:"animate-spin rounded-full border-2 border-gray-300 border-t-primary ".concat({sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"}[s]," ").concat(r)})},l=e=>{let{message:s="Loading..."}=e;return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(a,{size:"lg",className:"mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600 text-lg",children:s})]})})}},9053:(e,s,r)=>{"use strict";r.d(s,{A:()=>c});var t=r(5155),a=r(2115),l=r(5695),n=r(283),i=r(7023);function c(e){let{children:s,redirectTo:r="/signin",requireAuth:c=!0}=e,{isAuthenticated:o,isLoading:d}=(0,n.A)(),u=(0,l.useRouter)();return((0,a.useEffect)(()=>{d||(c&&!o?u.push(r):!c&&o&&u.push("/"))},[o,d,c,r,u]),d)?(0,t.jsx)(i.AV,{message:"Checking authentication..."}):c&&!o?(0,t.jsx)(i.AV,{message:"Redirecting to sign in..."}):!c&&o?(0,t.jsx)(i.AV,{message:"Redirecting..."}):(0,t.jsx)(t.Fragment,{children:s})}}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,9078,8543,6766,2366,7389,8441,1684,7358],()=>s(6253)),_N_E=e.O()}]);