"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3888],{1275:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(2115),o=n(2712);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},1285:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(2115),i=n(2712),l=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function u(e){let[t,n]=o.useState(l());return(0,i.N)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},2293:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(2115),o=0;function i(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:l()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},2712:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(2115),o=globalThis?.document?r.useLayoutEffect:()=>{}},3655:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>a});var r=n(2115),o=n(7650),i=n(9708),l=n(5155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},3795:(e,t,n)=>{n.d(t,{A:()=>Y});var r,o,i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function l(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,n(2115)),u="right-scroll-bar-position",c="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var f="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,d=new WeakMap;function p(e){return e}var m=function(e){void 0===e&&(e={});var t,n,r,o,l=(t=null,void 0===n&&(n=p),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),r={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),r}}}});return l.options=i({async:!0,ssr:!1},e),l}(),h=function(){},v=a.forwardRef(function(e,t){var n,r,o,u,c=a.useRef(null),p=a.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),v=p[0],g=p[1],y=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,R=e.shards,C=e.sideCar,S=e.noRelative,A=e.noIsolation,L=e.inert,T=e.allowPinchZoom,P=e.as,O=e.gapMode,N=l(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),k=(n=[c,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,f(function(){var e=d.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}d.set(u,n)},[n]),u),M=i(i({},N),v);return a.createElement(a.Fragment,null,E&&a.createElement(C,{sideCar:m,removeScrollBar:x,shards:R,noRelative:S,noIsolation:A,inert:L,setCallbacks:g,allowPinchZoom:!!T,lockRef:c,gapMode:O}),y?a.cloneElement(a.Children.only(w),i(i({},M),{ref:k})):a.createElement(void 0===P?"div":P,i({},M,{className:b,ref:k}),w))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:c,zeroRight:u};var g=function(e){var t=e.sideCar,n=l(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,i({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=y();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},R=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=R(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},S=b(),A="data-scroll-locked",L=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(A,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(A,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},T=function(){var e=parseInt(document.body.getAttribute(A)||"0",10);return isFinite(e)?e:0},P=function(){a.useEffect(function(){return document.body.setAttribute(A,(T()+1).toString()),function(){var e=T()-1;e<=0?document.body.removeAttribute(A):document.body.setAttribute(A,e.toString())}},[])},O=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;P();var i=a.useMemo(function(){return C(o)},[o]);return a.createElement(S,{styles:L(i,!t,o,n?"":"!important")})},N=!1;if("undefined"!=typeof window)try{var k=Object.defineProperty({},"passive",{get:function(){return N=!0,!0}});window.addEventListener("test",k,k),window.removeEventListener("test",k,k)}catch(e){N=!1}var M=!!N&&{passive:!1},D=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},j=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),W(e,r)){var o=F(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},W=function(e,t){return"v"===e?D(t,"overflowY"):D(t,"overflowX")},F=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},I=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,f=a>0,d=0,p=0;do{if(!u)break;var m=F(e,u),h=m[0],v=m[1]-m[2]-l*h;(h||v)&&W(e,u)&&(d+=v,p+=h);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return f&&(o&&1>Math.abs(d)||!o&&a>d)?s=!0:!f&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},B=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},H=function(e){return[e.deltaX,e.deltaY]},_=function(e){return e&&"current"in e?e.current:e},z=0,$=[];let X=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(z++)[0],i=a.useState(b)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(_),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=B(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-i[0],c="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,f=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===f&&"range"===s.type)return!1;var d=j(f,s);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=j(f,s)),!d)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return I(p,t,e,"h"===p?u:c,!0)},[]),c=a.useCallback(function(e){if($.length&&$[$.length-1]===i){var n="deltaY"in e?H(e):B(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(_).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),f=a.useCallback(function(e){n.current=B(e),r.current=void 0},[]),d=a.useCallback(function(t){s(t.type,H(t),t.target,u(t,e.lockRef.current))},[]),p=a.useCallback(function(t){s(t.type,B(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return $.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",c,M),document.addEventListener("touchmove",c,M),document.addEventListener("touchstart",f,M),function(){$=$.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,M),document.removeEventListener("touchmove",c,M),document.removeEventListener("touchstart",f,M)}},[]);var m=e.removeScrollBar,h=e.inert;return a.createElement(a.Fragment,null,h?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?a.createElement(O,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},m.useMedium(r),g);var V=a.forwardRef(function(e,t){return a.createElement(v,i({},e,{ref:t,sideCar:X}))});V.classNames=v.classNames;let Y=V},4315:(e,t,n)=>{n.d(t,{jH:()=>i});var r=n(2115);n(5155);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},4378:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(2115),o=n(7650),i=n(3655),l=n(2712),a=n(5155),u=r.forwardRef((e,t)=>{var n,u;let{container:c,...s}=e,[f,d]=r.useState(!1);(0,l.N)(()=>d(!0),[]);let p=c||f&&(null==(u=globalThis)||null==(n=u.document)?void 0:n.body);return p?o.createPortal((0,a.jsx)(i.sG.div,{...s,ref:t}),p):null});u.displayName="Portal"},5152:(e,t,n)=>{n.d(t,{Mz:()=>eZ,i3:()=>eJ,UC:()=>eU,bL:()=>eK,Bk:()=>eM});var r=n(2115);let o=["top","right","bottom","left"],i=Math.min,l=Math.max,a=Math.round,u=Math.floor,c=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function m(e){return e.split("-")[1]}function h(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>f[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function b(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function E(e,t,n){let r,{reference:o,floating:i}=e,l=g(t),a=h(g(t)),u=v(a),c=p(t),s="y"===l,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,y=o[u]/2-i[u]/2;switch(c){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(m(t)){case"start":r[a]-=y*(n&&s?-1:1);break;case"end":r[a]+=y*(n&&s?-1:1)}return r}let R=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:f}=E(c,r,u),d=r,p={},m=0;for(let n=0;n<a.length;n++){let{name:i,fn:h}=a[n],{x:v,y:g,data:y,reset:w}=await h({x:s,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=v?v:s,f=null!=g?g:f,p={...p,[i]:{...p[i],...y}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:f}=E(c,d,u)),n=-1)}return{x:s,y:f,placement:d,strategy:o,middlewareData:p}};async function C(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:f="floating",altBoundary:p=!1,padding:m=0}=d(t,e),h=b(m),v=a[p?"floating"===f?"reference":"floating":f],g=x(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),y="floating"===f?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),E=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},R=x(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:w,strategy:u}):y);return{top:(g.top-R.top+h.top)/E.y,bottom:(R.bottom-g.bottom+h.bottom)/E.y,left:(g.left-R.left+h.left)/E.x,right:(R.right-g.right+h.right)/E.x}}function S(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function A(e){return o.some(t=>e[t]>=0)}async function L(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=p(n),a=m(n),u="y"===g(n),c=["left","top"].includes(l)?-1:1,s=i&&u?-1:1,f=d(t,e),{mainAxis:h,crossAxis:v,alignmentAxis:y}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&"number"==typeof y&&(v="end"===a?-1*y:y),u?{x:v*s,y:h*c}:{x:h*c,y:v*s}}function T(){return"undefined"!=typeof window}function P(e){return k(e)?(e.nodeName||"").toLowerCase():"#document"}function O(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function N(e){var t;return null==(t=(k(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function k(e){return!!T()&&(e instanceof Node||e instanceof O(e).Node)}function M(e){return!!T()&&(e instanceof Element||e instanceof O(e).Element)}function D(e){return!!T()&&(e instanceof HTMLElement||e instanceof O(e).HTMLElement)}function j(e){return!!T()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof O(e).ShadowRoot)}function W(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=_(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function F(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function I(e){let t=B(),n=M(e)?_(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function B(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function H(e){return["html","body","#document"].includes(P(e))}function _(e){return O(e).getComputedStyle(e)}function z(e){return M(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function $(e){if("html"===P(e))return e;let t=e.assignedSlot||e.parentNode||j(e)&&e.host||N(e);return j(t)?t.host:t}function X(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=$(t);return H(n)?t.ownerDocument?t.ownerDocument.body:t.body:D(n)&&W(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=O(o);if(i){let e=V(l);return t.concat(l,l.visualViewport||[],W(o)?o:[],e&&n?X(e):[])}return t.concat(o,X(o,[],n))}function V(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Y(e){let t=_(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=D(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,u=a(n)!==i||a(r)!==l;return u&&(n=i,r=l),{width:n,height:r,$:u}}function q(e){return M(e)?e:e.contextElement}function G(e){let t=q(e);if(!D(t))return c(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=Y(t),l=(i?a(n.width):n.width)/r,u=(i?a(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),u&&Number.isFinite(u)||(u=1),{x:l,y:u}}let K=c(0);function Z(e){let t=O(e);return B()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:K}function U(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=q(e),a=c(1);t&&(r?M(r)&&(a=G(r)):a=G(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===O(l))&&o)?Z(l):c(0),s=(i.left+u.x)/a.x,f=(i.top+u.y)/a.y,d=i.width/a.x,p=i.height/a.y;if(l){let e=O(l),t=r&&M(r)?O(r):r,n=e,o=V(n);for(;o&&r&&t!==n;){let e=G(o),t=o.getBoundingClientRect(),r=_(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,f*=e.y,d*=e.x,p*=e.y,s+=i,f+=l,o=V(n=O(o))}}return x({width:d,height:p,x:s,y:f})}function J(e,t){let n=z(e).scrollLeft;return t?t.left+n:U(N(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:J(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=O(e),r=N(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=B();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=N(e),n=z(e),r=e.ownerDocument.body,o=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+J(e),u=-n.scrollTop;return"rtl"===_(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:u}}(N(e));else if(M(t))r=function(e,t){let n=U(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=D(e)?G(e):c(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=Z(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return x(r)}function et(e){return"static"===_(e).position}function en(e,t){if(!D(e)||"fixed"===_(e).position)return null;if(t)return t(e);let n=e.offsetParent;return N(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=O(e);if(F(e))return n;if(!D(e)){let t=$(e);for(;t&&!H(t);){if(M(t)&&!et(t))return t;t=$(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(P(r))&&et(r);)r=en(r,t);return r&&H(r)&&et(r)&&!I(r)?n:r||function(e){let t=$(e);for(;D(t)&&!H(t);){if(I(t))return t;if(F(t))break;t=$(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=D(t),o=N(t),i="fixed"===n,l=U(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=c(0);if(r||!r&&!i)if(("body"!==P(t)||W(o))&&(a=z(t)),r){let e=U(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=J(o));i&&!r&&o&&(u.x=J(o));let s=!o||r||i?c(0):Q(o,a);return{x:l.left+a.scrollLeft-u.x-s.x,y:l.top+a.scrollTop-u.y-s.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=N(r),a=!!t&&F(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},s=c(1),f=c(0),d=D(r);if((d||!d&&!i)&&(("body"!==P(r)||W(l))&&(u=z(r)),D(r))){let e=U(r);s=G(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}let p=!l||d||i?c(0):Q(l,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+f.x+p.x,y:n.y*s.y-u.scrollTop*s.y+f.y+p.y}},getDocumentElement:N,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,a=[..."clippingAncestors"===n?F(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=X(e,[],!1).filter(e=>M(e)&&"body"!==P(e)),o=null,i="fixed"===_(e).position,l=i?$(e):e;for(;M(l)&&!H(l);){let t=_(l),n=I(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||W(l)&&!n&&function e(t,n){let r=$(t);return!(r===n||!M(r)||H(r))&&("fixed"===_(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=$(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=a[0],c=a.reduce((e,n)=>{let r=ee(t,n,o);return e.top=l(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=l(r.left,e.left),e},ee(t,u,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=Y(e);return{width:t,height:n}},getScale:G,isElement:M,isRTL:function(e){return"rtl"===_(e).direction}};function el(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:a,platform:u,elements:c,middlewareData:s}=t,{element:f,padding:p=0}=d(e,t)||{};if(null==f)return{};let y=b(p),w={x:n,y:r},x=h(g(o)),E=v(x),R=await u.getDimensions(f),C="y"===x,S=C?"clientHeight":"clientWidth",A=a.reference[E]+a.reference[x]-w[x]-a.floating[E],L=w[x]-a.reference[x],T=await (null==u.getOffsetParent?void 0:u.getOffsetParent(f)),P=T?T[S]:0;P&&await (null==u.isElement?void 0:u.isElement(T))||(P=c.floating[S]||a.floating[E]);let O=P/2-R[E]/2-1,N=i(y[C?"top":"left"],O),k=i(y[C?"bottom":"right"],O),M=P-R[E]-k,D=P/2-R[E]/2+(A/2-L/2),j=l(N,i(D,M)),W=!s.arrow&&null!=m(o)&&D!==j&&a.reference[E]/2-(D<N?N:k)-R[E]/2<0,F=W?D<N?D-N:D-M:0;return{[x]:w[x]+F,data:{[x]:j,centerOffset:D-j-F,...W&&{alignmentOffset:F}},reset:W}}}),eu=(e,t,n)=>{let r=new Map,o={platform:ei,...n},i={...o.platform,_c:r};return R(e,t,{...o,platform:i})};var ec=n(7650),es="undefined"!=typeof document?r.useLayoutEffect:function(){};function ef(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ef(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ef(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ed(e);return Math.round(t*n)/n}function em(e){let t=r.useRef(e);return es(()=>{t.current=e}),t}let eh=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),ev=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await L(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}),eg=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=d(e,t),f={x:n,y:r},m=await C(t,s),v=g(p(o)),y=h(v),w=f[y],b=f[v];if(a){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+m[e],r=w-m[t];w=l(n,i(w,r))}if(u){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=b+m[e],r=b-m[t];b=l(n,i(b,r))}let x=c.fn({...t,[y]:w,[v]:b});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[y]:a,[v]:u}}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=d(e,t),s={x:n,y:r},f=g(o),m=h(f),v=s[m],y=s[f],w=d(a,t),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===m?"height":"width",t=i.reference[m]-i.floating[e]+b.mainAxis,n=i.reference[m]+i.reference[e]-b.mainAxis;v<t?v=t:v>n&&(v=n)}if(c){var x,E;let e="y"===m?"width":"height",t=["top","left"].includes(p(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(x=l.offset)?void 0:x[f])||0)+(t?0:b.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[f])||0)-(t?b.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[m]:v,[f]:y}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:f,elements:b}=t,{mainAxis:x=!0,crossAxis:E=!0,fallbackPlacements:R,fallbackStrategy:S="bestFit",fallbackAxisSideDirection:A="none",flipAlignment:L=!0,...T}=d(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let P=p(a),O=g(s),N=p(s)===s,k=await (null==f.isRTL?void 0:f.isRTL(b.floating)),M=R||(N||!L?[w(s)]:function(e){let t=w(e);return[y(e),t,y(t)]}(s)),D="none"!==A;!R&&D&&M.push(...function(e,t,n,r){let o=m(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(y)))),i}(s,L,A,k));let j=[s,...M],W=await C(t,T),F=[],I=(null==(r=u.flip)?void 0:r.overflows)||[];if(x&&F.push(W[P]),E){let e=function(e,t,n){void 0===n&&(n=!1);let r=m(e),o=h(g(e)),i=v(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=w(l)),[l,w(l)]}(a,c,k);F.push(W[e[0]],W[e[1]])}if(I=[...I,{placement:a,overflows:F}],!F.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=j[e];if(t&&("alignment"!==E||O===g(t)||I.every(e=>e.overflows[0]>0&&g(e.placement)===O)))return{data:{index:e,overflows:I},reset:{placement:t}};let n=null==(i=I.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(S){case"bestFit":{let e=null==(l=I.filter(e=>{if(D){let t=g(e.placement);return t===O||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,a,{placement:u,rects:c,platform:s,elements:f}=t,{apply:h=()=>{},...v}=d(e,t),y=await C(t,v),w=p(u),b=m(u),x="y"===g(u),{width:E,height:R}=c.floating;"top"===w||"bottom"===w?(o=w,a=b===(await (null==s.isRTL?void 0:s.isRTL(f.floating))?"start":"end")?"left":"right"):(a=w,o="end"===b?"top":"bottom");let S=R-y.top-y.bottom,A=E-y.left-y.right,L=i(R-y[o],S),T=i(E-y[a],A),P=!t.middlewareData.shift,O=L,N=T;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(N=A),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(O=S),P&&!b){let e=l(y.left,0),t=l(y.right,0),n=l(y.top,0),r=l(y.bottom,0);x?N=E-2*(0!==e||0!==t?e+t:l(y.left,y.right)):O=R-2*(0!==n||0!==r?n+r:l(y.top,y.bottom))}await h({...t,availableWidth:N,availableHeight:O});let k=await s.getDimensions(f.floating);return E!==k.width||R!==k.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=d(e,t);switch(r){case"referenceHidden":{let e=S(await C(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:A(e)}}}case"escaped":{let e=S(await C(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:A(e)}}}default:return{}}}}}(e),options:[e,t]}),eE=(e,t)=>({...eh(e),options:[e,t]});var eR=n(3655),eC=n(5155),eS=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eC.jsx)(eR.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eC.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eS.displayName="Arrow";var eA=n(6101),eL=n(6081),eT=n(9033),eP=n(2712),eO=n(1275),eN="Popper",[ek,eM]=(0,eL.A)(eN),[eD,ej]=ek(eN),eW=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eC.jsx)(eD,{scope:t,anchor:o,onAnchorChange:i,children:n})};eW.displayName=eN;var eF="PopperAnchor",eI=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,l=ej(eF,n),a=r.useRef(null),u=(0,eA.s)(t,a);return r.useEffect(()=>{l.onAnchorChange((null==o?void 0:o.current)||a.current)}),o?null:(0,eC.jsx)(eR.sG.div,{...i,ref:u})});eI.displayName=eF;var eB="PopperContent",[eH,e_]=ek(eB),ez=r.forwardRef((e,t)=>{var n,o,a,c,s,f,d,p;let{__scopePopper:m,side:h="bottom",sideOffset:v=0,align:g="center",alignOffset:y=0,arrowPadding:w=0,avoidCollisions:b=!0,collisionBoundary:x=[],collisionPadding:E=0,sticky:R="partial",hideWhenDetached:C=!1,updatePositionStrategy:S="optimized",onPlaced:A,...L}=e,T=ej(eB,m),[P,O]=r.useState(null),k=(0,eA.s)(t,e=>O(e)),[M,D]=r.useState(null),j=(0,eO.X)(M),W=null!=(d=null==j?void 0:j.width)?d:0,F=null!=(p=null==j?void 0:j.height)?p:0,I="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},B=Array.isArray(x)?x:[x],H=B.length>0,_={padding:I,boundary:B.filter(eY),altBoundary:H},{refs:z,floatingStyles:$,placement:V,isPositioned:Y,middlewareData:G}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:l,floating:a}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[f,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=r.useState(o);ef(p,o)||m(o);let[h,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),b=r.useCallback(e=>{e!==C.current&&(C.current=e,y(e))},[]),x=l||h,E=a||g,R=r.useRef(null),C=r.useRef(null),S=r.useRef(f),A=null!=c,L=em(c),T=em(i),P=em(s),O=r.useCallback(()=>{if(!R.current||!C.current)return;let e={placement:t,strategy:n,middleware:p};T.current&&(e.platform=T.current),eu(R.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==P.current};N.current&&!ef(S.current,t)&&(S.current=t,ec.flushSync(()=>{d(t)}))})},[p,t,n,T,P]);es(()=>{!1===s&&S.current.isPositioned&&(S.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[s]);let N=r.useRef(!1);es(()=>(N.current=!0,()=>{N.current=!1}),[]),es(()=>{if(x&&(R.current=x),E&&(C.current=E),x&&E){if(L.current)return L.current(x,E,O);O()}},[x,E,O,L,A]);let k=r.useMemo(()=>({reference:R,floating:C,setReference:w,setFloating:b}),[w,b]),M=r.useMemo(()=>({reference:x,floating:E}),[x,E]),D=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!M.floating)return e;let t=ep(M.floating,f.x),r=ep(M.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...ed(M.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,M.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:O,refs:k,elements:M,floatingStyles:D}),[f,O,k,M,D])}({strategy:"fixed",placement:h+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=q(e),m=a||c?[...p?X(p):[],...X(t)]:[];m.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let h=p&&f?function(e,t){let n,r=null,o=N(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function c(s,f){void 0===s&&(s=!1),void 0===f&&(f=1),a();let d=e.getBoundingClientRect(),{left:p,top:m,width:h,height:v}=d;if(s||t(),!h||!v)return;let g=u(m),y=u(o.clientWidth-(p+h)),w={rootMargin:-g+"px "+-y+"px "+-u(o.clientHeight-(m+v))+"px "+-u(p)+"px",threshold:l(0,i(1,f))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==f){if(!b)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||el(d,e.getBoundingClientRect())||c(),b=!1}try{r=new IntersectionObserver(x,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,w)}r.observe(e)}(!0),a}(p,n):null,v=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!d&&g.observe(p),g.observe(t));let y=d?U(e):null;return d&&function t(){let r=U(e);y&&!el(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;m.forEach(e=>{a&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==h||h(),null==(e=g)||e.disconnect(),g=null,d&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===S})},elements:{reference:T.anchor},middleware:[ev({mainAxis:v+F,alignmentAxis:y}),b&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?ey():void 0,..._}),b&&ew({..._}),eb({..._,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),M&&eE({element:M,padding:w}),eq({arrowWidth:W,arrowHeight:F}),C&&ex({strategy:"referenceHidden",..._})]}),[K,Z]=eG(V),J=(0,eT.c)(A);(0,eP.N)(()=>{Y&&(null==J||J())},[Y,J]);let Q=null==(n=G.arrow)?void 0:n.x,ee=null==(o=G.arrow)?void 0:o.y,et=(null==(a=G.arrow)?void 0:a.centerOffset)!==0,[en,er]=r.useState();return(0,eP.N)(()=>{P&&er(window.getComputedStyle(P).zIndex)},[P]),(0,eC.jsx)("div",{ref:z.setFloating,"data-radix-popper-content-wrapper":"",style:{...$,transform:Y?$.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null==(c=G.transformOrigin)?void 0:c.x,null==(s=G.transformOrigin)?void 0:s.y].join(" "),...(null==(f=G.hide)?void 0:f.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eC.jsx)(eH,{scope:m,placedSide:K,onArrowChange:D,arrowX:Q,arrowY:ee,shouldHideArrow:et,children:(0,eC.jsx)(eR.sG.div,{"data-side":K,"data-align":Z,...L,ref:k,style:{...L.style,animation:Y?void 0:"none"}})})})});ez.displayName=eB;var e$="PopperArrow",eX={top:"bottom",right:"left",bottom:"top",left:"right"},eV=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e_(e$,n),i=eX[o.placedSide];return(0,eC.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eC.jsx)(eS,{...r,ref:t,style:{...r.style,display:"block"}})})});function eY(e){return null!==e}eV.displayName=e$;var eq=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:c}=t,s=(null==(n=c.arrow)?void 0:n.centerOffset)!==0,f=s?0:e.arrowWidth,d=s?0:e.arrowHeight,[p,m]=eG(a),h={start:"0%",center:"50%",end:"100%"}[m],v=(null!=(i=null==(r=c.arrow)?void 0:r.x)?i:0)+f/2,g=(null!=(l=null==(o=c.arrow)?void 0:o.y)?l:0)+d/2,y="",w="";return"bottom"===p?(y=s?h:"".concat(v,"px"),w="".concat(-d,"px")):"top"===p?(y=s?h:"".concat(v,"px"),w="".concat(u.floating.height+d,"px")):"right"===p?(y="".concat(-d,"px"),w=s?h:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+d,"px"),w=s?h:"".concat(g,"px")),{data:{x:y,y:w}}}});function eG(e){let[t,n="center"]=e.split("-");return[t,n]}var eK=eW,eZ=eI,eU=ez,eJ=eV},5185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},5845:(e,t,n)=>{n.d(t,{i:()=>a});var r,o=n(2115),i=n(2712),l=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function a({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,a,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),a=o.useRef(t);return l(()=>{a.current=t},[t]),o.useEffect(()=>{i.current!==n&&(a.current?.(n),i.current=n)},[n,i]),[n,r,a]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else a(t)},[c,e,a,u])]}Symbol("RADIX:SYNC_STATE")},6081:(e,t,n)=>{n.d(t,{A:()=>l,q:()=>i});var r=n(2115),o=n(5155);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,l=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:l,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function l(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let l=r.createContext(i),a=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,c=n?.[e]?.[a]||l,s=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[a]||l,c=r.useContext(u);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},6101:(e,t,n)=>{n.d(t,{s:()=>l,t:()=>i});var r=n(2115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():o(e[t],null)}}}}function l(...e){return r.useCallback(i(...e),e)}},7328:(e,t,n)=>{function r(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function o(e,t){var n=r(e,t,"get");return n.get?n.get.call(e):n.value}function i(e,t,n){var o=r(e,t,"set");if(o.set)o.set.call(e,n);else{if(!o.writable)throw TypeError("attempted to set read only private field");o.value=n}return n}n.d(t,{N:()=>d});var l,a=n(2115),u=n(6081),c=n(6101),s=n(9708),f=n(5155);function d(e){let t=e+"CollectionProvider",[n,r]=(0,u.A)(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:n}=e,r=a.useRef(null),i=a.useRef(new Map).current;return(0,f.jsx)(o,{scope:t,itemMap:i,collectionRef:r,children:n})};l.displayName=t;let d=e+"CollectionSlot",p=(0,s.TL)(d),m=a.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=i(d,n),l=(0,c.s)(t,o.collectionRef);return(0,f.jsx)(p,{ref:l,children:r})});m.displayName=d;let h=e+"CollectionItemSlot",v="data-radix-collection-item",g=(0,s.TL)(h),y=a.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,l=a.useRef(null),u=(0,c.s)(t,l),s=i(h,n);return a.useEffect(()=>(s.itemMap.set(l,{ref:l,...o}),()=>void s.itemMap.delete(l))),(0,f.jsx)(g,{...{[v]:""},ref:u,children:r})});return y.displayName=h,[{Provider:l,Slot:m,ItemSlot:y},function(t){let n=i(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(v,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var p=new WeakMap;function m(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=h(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function h(e){return e!=e||0===e?0:Math.trunc(e)}l=new WeakMap},7900:(e,t,n)=>{n.d(t,{n:()=>f});var r=n(2115),o=n(6101),i=n(3655),l=n(9033),a=n(5155),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},f=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:f=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[w,b]=r.useState(null),x=(0,l.c)(v),E=(0,l.c)(g),R=r.useRef(null),C=(0,o.s)(t,e=>b(e)),S=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(f){let e=function(e){if(S.paused||!w)return;let t=e.target;w.contains(t)?R.current=t:m(R.current,{select:!0})},t=function(e){if(S.paused||!w)return;let t=e.relatedTarget;null!==t&&(w.contains(t)||m(R.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&m(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[f,w,S.paused]),r.useEffect(()=>{if(w){h.add(S);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,s);w.addEventListener(u,x),w.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(m(r,{select:t}),document.activeElement!==n)return}(d(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(w))}return()=>{w.removeEventListener(u,x),setTimeout(()=>{let t=new CustomEvent(c,s);w.addEventListener(c,E),w.dispatchEvent(t),t.defaultPrevented||m(null!=e?e:document.body,{select:!0}),w.removeEventListener(c,E),h.remove(S)},0)}}},[w,x,E,S]);let A=r.useCallback(e=>{if(!n&&!f||S.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=d(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&m(i,{select:!0})):(e.preventDefault(),n&&m(o,{select:!0})):r===t&&e.preventDefault()}},[n,f,S.paused]);return(0,a.jsx)(i.sG.div,{tabIndex:-1,...y,ref:C,onKeyDown:A})});function d(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function m(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}f.displayName="FocusScope";var h=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=v(e,t)).unshift(t)},remove(t){var n;null==(n=(e=v(e,t))[0])||n.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},8168:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,l={},a=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});l[n]||(l[n]=new WeakMap);var s=l[n],f=[],d=new Set,p=new Set(c),m=function(e){!e||d.has(e)||(d.add(e),m(e.parentNode))};c.forEach(m);var h=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(d.has(e))h(e);else try{var t=e.getAttribute(r),l=null!==t&&"false"!==t,a=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,a),s.set(e,u),f.push(e),1===a&&l&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),l||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),d.clear(),a++,function(){f.forEach(function(e){var t=o.get(e)-1,l=s.get(e)-1;o.set(e,t),s.set(e,l),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),l||e.removeAttribute(n)}),--a||(o=new WeakMap,o=new WeakMap,i=new WeakMap,l={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),c(o,i,n,"aria-hidden")):function(){return null}}},9033:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(2115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9178:(e,t,n)=>{n.d(t,{qW:()=>d});var r,o=n(2115),i=n(5185),l=n(3655),a=n(6101),u=n(9033),c=n(5155),s="dismissableLayer.update",f=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=o.forwardRef((e,t)=>{var n,d;let{disableOutsidePointerEvents:h=!1,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:w,onDismiss:b,...x}=e,E=o.useContext(f),[R,C]=o.useState(null),S=null!=(d=null==R?void 0:R.ownerDocument)?d:null==(n=globalThis)?void 0:n.document,[,A]=o.useState({}),L=(0,a.s)(t,e=>C(e)),T=Array.from(E.layers),[P]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),O=T.indexOf(P),N=R?T.indexOf(R):-1,k=E.layersWithOutsidePointerEventsDisabled.size>0,M=N>=O,D=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){m("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));M&&!n&&(null==g||g(e),null==w||w(e),e.defaultPrevented||null==b||b())},S),j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&m("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...E.branches].some(e=>e.contains(t))&&(null==y||y(e),null==w||w(e),e.defaultPrevented||null==b||b())},S);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{N===E.layers.size-1&&(null==v||v(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},S),o.useEffect(()=>{if(R)return h&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(R)),E.layers.add(R),p(),()=>{h&&1===E.layersWithOutsidePointerEventsDisabled.size&&(S.body.style.pointerEvents=r)}},[R,S,h,E]),o.useEffect(()=>()=>{R&&(E.layers.delete(R),E.layersWithOutsidePointerEventsDisabled.delete(R),p())},[R,E]),o.useEffect(()=>{let e=()=>A({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(l.sG.div,{...x,ref:L,style:{pointerEvents:k?M?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,j.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,D.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function m(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,l.hO)(i,a):i.dispatchEvent(a)}d.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(f),r=o.useRef(null),i=(0,a.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(l.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},9708:(e,t,n)=>{n.d(t,{DX:()=>a,TL:()=>l});var r=n(2115),o=n(6101),i=n(5155);function l(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...i}=e;if(r.isValidElement(n)){var l;let e,a,u=(l=n,(a=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(a=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),c=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==r.Fragment&&(c.ref=t?(0,o.t)(t,u):u),r.cloneElement(n,c)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...l}=e,a=r.Children.toArray(o),u=a.find(c);if(u){let e=u.props.children,o=a.map(t=>t!==u?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...l,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,i.jsx)(t,{...l,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}var a=l("Slot"),u=Symbol("radix.slottable");function c(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}}}]);