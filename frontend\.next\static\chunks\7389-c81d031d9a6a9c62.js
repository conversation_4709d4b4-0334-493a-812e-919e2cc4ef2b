"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7389],{37:(e,t,a)=>{a.d(t,{j:()=>s});var r=a(3243);class n{async getAllProducts(){return(await r.A.get("/product/get-all-products")).data}async getProduct(e){return(await r.A.get("/product/get-product/".concat(e))).data}async getProductsByCategory(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;return e.limit&&t.append("limit",e.limit.toString()),e.page&&t.append("page",e.page.toString()),e.category&&t.append("category",e.category),e.minPrice&&t.append("minPrice",e.minPrice.toString()),e.maxPrice&&t.append("maxPrice",e.maxPrice.toString()),e.sortBy&&t.append("sortBy",e.sortBy),e.sortOrder&&t.append("sortOrder",e.sortOrder),(await r.A.get("/product/get-product-by-category?".concat(t.toString()))).data}async searchProducts(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=new URLSearchParams;return t.limit&&a.append("limit",t.limit.toString()),t.page&&a.append("page",t.page.toString()),(await r.A.get("/product/get-product-by-query?".concat(a.toString()),{data:{query:e}})).data}async addReview(e,t){return(await r.A.post("/product/reviews/".concat(e),t)).data}async getReviews(e){return(await r.A.get("/product/get-reviews/".concat(e))).data}filterProducts(e,t){return e.filter(e=>(!t.category||e.category===t.category)&&(!t.minPrice||!(e.price<t.minPrice))&&(!t.maxPrice||!(e.price>t.maxPrice))&&(!t.minRating||!((e.averageRating||0)<t.minRating)))}sortProducts(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"asc";return[...e].sort((e,r)=>{let n=0;switch(t){case"price":n=e.price-r.price;break;case"rating":n=(e.averageRating||0)-(r.averageRating||0);break;case"createdAt":n=new Date(e.createdAt).getTime()-new Date(r.createdAt).getTime()}return"desc"===a?-n:n})}getCategories(e){return[...new Set(e.map(e=>e.category))]}getPriceRange(e){if(0===e.length)return{min:0,max:0};let t=e.map(e=>e.price);return{min:Math.min(...t),max:Math.max(...t)}}}let s=new n},3243:(e,t,a)=>{a.d(t,{A:()=>i});var r=a(3464),n=a(9509);let s=r.A.create({baseURL:n.env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:5000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});s.interceptors.request.use(e=>{var t;let a=(null==(t=e.url)?void 0:t.includes("/admin"))?localStorage.getItem("admin_token"):localStorage.getItem("token");return a&&(e.headers.Authorization="Bearer ".concat(a)),e},e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>{var t,a,r;return(null==(t=e.response)?void 0:t.status)===401&&((null==(r=e.config)||null==(a=r.url)?void 0:a.includes("/admin"))?(localStorage.removeItem("admin_token"),window.location.href="/admin"):(localStorage.removeItem("token"),window.location.href="/login")),Promise.reject(e)});let i=s},5654:(e,t,a)=>{a.d(t,{ZJ:()=>s,CV:()=>u,Qo:()=>g,jU:()=>c.j,Dv:()=>d});var r=a(3243);class n{async signUp(e){return(await r.A.post("/admin/sign-up",e)).data}async login(e){return(await r.A.post("/admin/login",e)).data}async logout(){return(await r.A.post("/admin/logout")).data}async getAdmin(){return(await r.A.get("admin/get-admin")).data}async uploadProduct(e){let t=new FormData;return t.append("title",e.title),t.append("description",e.description),t.append("price",e.price.toString()),t.append("category",e.category),t.append("weight",e.weight),t.append("stock",e.stock.toString()),e.images.forEach(e=>{t.append("images",e)}),(await r.A.post("/admin/upload-product",t,{headers:{"Content-Type":"multipart/form-data"}})).data}async updateProduct(e,t){return(await r.A.patch("/admin/update-product/".concat(e),t)).data}async deleteProduct(e){return(await r.A.delete("/admin/delete-product/".concat(e))).data}async suggestProductDetails(e){let t=new FormData;return t.append("image",e),(await r.A.post("/admin/suggest-product-details",t,{headers:{"Content-Type":"multipart/form-data"}})).data}async changeDeliveryStatus(e){return(await r.A.patch("/admin/change-delivery-status",e)).data}async deleteOrder(e,t){return(await r.A.delete("/admin/delete-order",{data:{orderId:e,message:t}})).data}async getUserOrders(e){return(await r.A.get("/admin/user-orders/".concat(e))).data}async getAllOrders(){return(await r.A.get("/admin/orders")).data}async getAllUsers(){return(await r.A.get("/admin/users")).data}async getUserById(e){return(await r.A.get("/admin/user/".concat(e))).data}async getAllAdmins(){return(await r.A.get("/admin/admins")).data}async deleteAdmin(e){return(await r.A.delete("/delete-admin/".concat(e))).data}async getDashboard(){return(await r.A.get("/admin/dashboard")).data}async sendMessageToUser(e,t){return(await r.A.post("/message/send-message-by-admin/".concat(e),{message:t})).data}}let s=new n;class i{async signUp(e){return(await r.A.post("/user/sign-up",e)).data}async signIn(e){var t;let a=await r.A.post("/user/sign-in",e);return a.data.success&&(null==(t=a.data.data)?void 0:t.token)&&localStorage.setItem("token",a.data.data.token),a.data}async verifyOtp(e,t){return(await r.A.post("/user/verify-otp/".concat(e),{otp:t})).data}async resendOtp(e,t){return(await r.A.post("/user/resend-otp/".concat(e),{email:t})).data}async logout(){let e=await r.A.post("/user/logout");return localStorage.removeItem("token"),e.data}async getUser(){return(await r.A.get("/user/get-user")).data}async updatePreferences(e){return(await r.A.patch("/user/update-preferences",e)).data}async getAvatar(){return(await r.A.get("/user/get-avatar")).data}async updatePassword(e){return(await r.A.patch("/user/update-password",e)).data}async emailVerification(e){return(await r.A.post("/user/email-verification",e)).data}async updateEmail(e){return(await r.A.patch("/user/update-email",e)).data}async updatePersonalInfo(e){let t=await r.A.patch("/user/update-personal-info",e);return console.log(t),t.data}async updateAvatar(e){let t=new FormData;return t.append("avatar",e),(await r.A.patch("/user/update-avatar",t,{headers:{"Content-Type":"multipart/form-data"}})).data}async deleteAccount(e){let t=await r.A.delete("/user/delete-account",{data:{password:e}});return localStorage.removeItem("token"),t.data}}let d=new i;var c=a(37);class o{async addToCart(e){return(await r.A.post("/cart/add-to-cart",e)).data}async getCartItems(){return(await r.A.get("/cart/get-cart-items")).data}async updateQuantity(e){return(await r.A.patch("/cart/toggle-quantity",e)).data}async removeProduct(e){return(await r.A.delete("/cart/remove-product",{data:{productId:e}})).data}async clearCart(){return(await r.A.delete("/cart/clear")).data}calculateCartTotal(e){return e.reduce((e,t)=>e+t.price*t.quantity,0)}calculateItemSubtotal(e){return e.price*e.quantity}getTotalItems(e){return e.reduce((e,t)=>e+t.quantity,0)}isProductInCart(e,t){return e.some(e=>e.productId===t)}getCartItem(e,t){return e.find(e=>e.productId===t)}calculateShipping(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;return e>=t?0:10}calculateTax(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.08;return e*t}calculateFinalTotal(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=this.calculateCartTotal(e),r=this.calculateShipping(a,t.freeShippingThreshold),n=this.calculateTax(a,t.taxRate),s=a+r+n;return{subtotal:a,shipping:r,tax:n,total:s}}validateCart(e){let t=[];return 0===e.length&&t.push("Cart is empty"),e.forEach(e=>{e.quantity<=0&&t.push("Invalid quantity for ".concat(e.title)),e.quantity>e.stock&&t.push("Not enough stock for ".concat(e.title,". Available: ").concat(e.stock))}),{isValid:0===t.length,errors:t}}}let u=new o;class l{async placeOrder(e){return(await r.A.post("/order/place-order",e)).data}async getOrders(){return(await r.A.get("/order/get-orders")).data}async cancelOrder(e){return(await r.A.delete("/order/cancel-order-by-user",{data:e})).data}getOrdersByStatus(e,t){return e.filter(e=>e.status===t)}getOrderById(e,t){return e.find(e=>e._id===t)}calculateOrderTotal(e){return e.orderItems.reduce((e,t)=>e+t.price*t.quantity,0)}getOrderStatusColor(e){return({pending:"text-yellow-600 bg-yellow-100",processing:"text-blue-600 bg-blue-100",shipped:"text-purple-600 bg-purple-100",delivered:"text-green-600 bg-green-100",cancelled:"text-red-600 bg-red-100"})[e]||"text-gray-600 bg-gray-100"}getOrderStatusText(e){return({pending:"Pending",shipped:"Shipped",delivered:"Delivered",cancelled:"Cancelled"})[e]||"Unknown"}canCancelOrder(e){return["pending","processing"].includes(e.status)}getEstimatedDeliveryDate(e){if("delivered"===e.status)return null;let t=new Date(e.createdAt),a={pending:7,processing:5,shipped:3,delivered:0,cancelled:0}[e.status]||7,r=new Date(t);return r.setDate(r.getDate()+a),r}formatOrderDate(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}getOrderStats(e){let t={total:e.length,pending:0,processing:0,shipped:0,delivered:0,cancelled:0,totalRevenue:0};return e.forEach(e=>{t[e.status]++,"cancelled"!==e.status&&(t.totalRevenue=e.totalRevenue)}),t}sortOrdersByDate(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return[...e].sort((e,a)=>{let r=new Date(e.createdAt).getTime(),n=new Date(a.createdAt).getTime();return t?r-n:n-r})}filterOrdersByDateRange(e,t,a){return e.filter(e=>{let r=new Date(e.createdAt);return r>=t&&r<=a})}}let g=new l;class p{async sendMessageByUser(e){return(await r.A.post("/message/send-message-by-user",e)).data}async getMessages(){return(await r.A.get("/message/get-messages")).data}sortMessagesByDate(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return[...e].sort((e,a)=>{let r=new Date(e.createdAt).getTime(),n=new Date(a.createdAt).getTime();return t?r-n:n-r})}groupMessagesByDate(e){let t={};return e.forEach(e=>{let a=new Date(e.createdAt).toDateString();t[a]||(t[a]=[]),t[a].push(e)}),t}formatMessageTime(e){return new Date(e).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"})}formatMessageDate(e){let t=new Date(e),a=new Date,r=new Date(a);return(r.setDate(r.getDate()-1),t.toDateString()===a.toDateString())?"Today":t.toDateString()===r.toDateString()?"Yesterday":t.toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})}getUnreadMessagesCount(e){return e.filter(e=>e.isFromAdmin&&!e.isRead).length}getLastMessage(e){return 0===e.length?null:this.sortMessagesByDate(e,!1)[0]}searchMessages(e,t){let a=t.toLowerCase();return e.filter(e=>e.message.toLowerCase().includes(a))}getMessagesByType(e,t){return e.filter(e=>e.isFromAdmin===t)}getConversationSummary(e){let t=this.getMessagesByType(e,!1),a=this.getMessagesByType(e,!0),r=this.getLastMessage(e);return{totalMessages:e.length,userMessages:t.length,adminMessages:a.length,lastMessageDate:r?r.createdAt:null}}}new p},9434:(e,t,a)=>{a.d(t,{cn:()=>s});var r=a(2596),n=a(9688);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.QP)((0,r.$)(t))}}}]);