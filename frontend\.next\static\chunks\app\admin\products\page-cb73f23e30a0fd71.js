(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7575],{968:(e,s,t)=>{"use strict";t.d(s,{b:()=>d});var r=t(2115),a=t(3655),l=t(5155),i=r.forwardRef((e,s)=>(0,l.jsx)(a.sG.label,{...e,ref:s,onMouseDown:s=>{var t;s.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));i.displayName="Label";var d=i},1154:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1785:(e,s,t)=>{Promise.resolve().then(t.bind(t,4368)),Promise.resolve().then(t.bind(t,5644))},2525:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},4416:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4616:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5057:(e,s,t)=>{"use strict";t.d(s,{J:()=>i});var r=t(5155);t(2115);var a=t(968),l=t(9434);function i(e){let{className:s,...t}=e;return(0,r.jsx)(a.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...t})}},5644:(e,s,t)=>{"use strict";t.d(s,{default:()=>U});var r=t(5155),a=t(2115),l=t(6766),i=t(5654),d=t(285),c=t(2523),o=t(6695),n=t(6126),u=t(5365),m=t(8856),h=t(4616),x=t(7924),p=t(6932),g=t(5339),j=t(7108),v=t(5623),f=t(3717),b=t(2657),y=t(2525),N=t(8543),w=t(5057),k=t(9434);let A=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("textarea",{className:(0,k.cn)("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...a})});A.displayName="Textarea";var P=t(7550),C=t(9946);let S=(0,C.A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var R=t(4416),M=t(1154);let E=(0,C.A)("wand-sparkles",[["path",{d:"m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72",key:"ul74o6"}],["path",{d:"m14 7 3 3",key:"1r5n42"}],["path",{d:"M5 6v4",key:"ilb8ba"}],["path",{d:"M19 14v4",key:"blhpug"}],["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M7 8H3",key:"zfb6yr"}],["path",{d:"M21 16h-4",key:"1cnmox"}],["path",{d:"M11 3H9",key:"1obp7u"}]]);var F=t(9869);let J=e=>{var s,t,n;let{product:m,onClose:h,onSuccess:x}=e,[p,j]=(0,a.useState)({title:(null==m?void 0:m.title)||"",description:(null==m?void 0:m.description)||"",price:(null==m||null==(s=m.price)?void 0:s.toString())||"",category:(null==m?void 0:m.category)||"",weight:(null==m?void 0:m.weight)||"",stock:(null==m||null==(t=m.stock)?void 0:t.toString())||""}),[v,f]=(0,a.useState)([]),[b,y]=(0,a.useState)((null==m||null==(n=m.images)?void 0:n.map(e=>e.url))||[]),[k,C]=(0,a.useState)(!1),[J,$]=(0,a.useState)(""),q=(0,a.useRef)(null),[U,D]=(0,a.useState)(!1),[Z,z]=(0,a.useState)(null),[I,_]=(0,a.useState)(""),[B,W]=(0,a.useState)(null),[G,L]=(0,a.useState)(!1),T=!!m,H=e=>{let{name:s,value:t}=e.target;j(e=>({...e,[s]:t})),J&&$("")},O=e=>{var s;y(s=>s.filter((s,t)=>t!==e));let t=(null==m||null==(s=m.images)?void 0:s.length)||0;if(e>=t){let s=e-t;f(e=>e.filter((e,t)=>t!==s))}},V=async()=>{if(!Z)return void N.oR.error("Please select an image first");D(!0),$("");try{let e=await i.ZJ.suggestProductDetails(Z);e.success&&e.data?(W(e.data[0]),L(!0),N.oR.success("Product suggestions generated successfully!")):N.oR.error(e.message||"Failed to generate suggestions")}catch(r){var e,s;let t=(null==(s=r.response)||null==(e=s.data)?void 0:e.message)||"An error occurred while generating suggestions";N.oR.error(t)}finally{D(!1)}},Q=()=>{W(null),L(!1),z(null),_("")},K=()=>p.title.trim()?p.description.trim()?!p.price||isNaN(Number(p.price))||0>=Number(p.price)?($("Please enter a valid price"),!1):p.category.trim()?p.weight.trim()?!p.stock||isNaN(Number(p.stock))||0>Number(p.stock)?($("Please enter a valid stock quantity"),!1):!!T||0!==v.length||($("Please select at least one product image"),!1):($("Product weight is required"),!1):($("Product category is required"),!1):($("Product description is required"),!1):($("Product title is required"),!1),X=async e=>{if(e.preventDefault(),K()){C(!0),$("");try{if(T&&m){let e={title:p.title,description:p.description,price:Number(p.price),category:p.category,weight:p.weight,stock:Number(p.stock)},s=await i.ZJ.updateProduct(m._id,e);s.success?(N.oR.success("Product updated successfully"),x()):$(s.message||"Failed to update product")}else{let e={title:p.title,description:p.description,price:Number(p.price),category:p.category,weight:p.weight,stock:Number(p.stock),images:v},s=await i.ZJ.uploadProduct(e);s.success?(N.oR.success("Product created successfully"),x()):$(s.message||"Failed to create product")}}catch(r){var s,t;let e=(null==(t=r.response)||null==(s=t.data)?void 0:s.message)||"An error occurred while ".concat(T?"updating":"creating"," the product");$(e),N.oR.error(e)}finally{C(!1)}}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(d.$,{variant:"ghost",size:"icon",onClick:h,children:(0,r.jsx)(P.A,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:T?"Edit Product":"Add New Product"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:T?"Update product information":"Fill in the details to add a new product"})]})]}),(0,r.jsxs)("form",{onSubmit:X,className:"space-y-6",children:[J&&(0,r.jsxs)(u.Fc,{variant:"destructive",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{children:"Error"}),(0,r.jsx)("p",{children:J})]})]}),!T&&(0,r.jsxs)(o.Zp,{children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{children:"AI Product Suggestion"}),(0,r.jsx)(o.BT,{children:"Upload an image to get AI-generated product title and description suggestions"})]}),(0,r.jsx)(o.Wu,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"border-2 border-dashed border-border rounded-lg p-4 text-center",children:[(0,r.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var s;let t=null==(s=e.target.files)?void 0:s[0];if(!t)return;if(!["image/jpeg","image/jpg","image/png","image/webp"].includes(t.type))return void N.oR.error("Please select a valid image file (JPEG, PNG, WebP)");if(t.size>5242880)return void N.oR.error("Please select an image smaller than 5MB");z(t);let r=new FileReader;r.onload=e=>{var s;_(null==(s=e.target)?void 0:s.result)},r.readAsDataURL(t)},className:"hidden",id:"suggestion-image",disabled:U}),(0,r.jsxs)("label",{htmlFor:"suggestion-image",className:"cursor-pointer",children:[(0,r.jsx)(S,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-sm font-medium",children:"Upload Image for Suggestions"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"JPEG, PNG, WebP (max 5MB)"})]})]}),I&&(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(l.default,{src:I,alt:"Suggestion preview",width:400,height:128,className:"w-full h-32 object-cover rounded-lg border border-border"}),(0,r.jsx)(d.$,{type:"button",variant:"destructive",size:"icon",className:"absolute top-2 right-2 h-6 w-6",onClick:Q,disabled:U,children:(0,r.jsx)(R.A,{className:"h-3 w-3"})})]}),(0,r.jsx)(d.$,{type:"button",onClick:V,disabled:!Z||U,className:"w-full",children:U?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(M.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Generating Suggestions..."]}):"Get AI Suggestions"})]}),(0,r.jsx)("div",{className:"space-y-4",children:G&&B?(0,r.jsxs)("div",{className:"border border-border rounded-lg p-4 space-y-4",children:[(0,r.jsx)("h4",{className:"font-medium text-sm text-muted-foreground",children:"AI Suggestions"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(w.J,{className:"text-xs text-muted-foreground",children:"Suggested Title"}),(0,r.jsx)("p",{className:"text-sm font-medium p-2 bg-muted rounded",children:B.title})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(w.J,{className:"text-xs text-muted-foreground",children:"Suggested Description"}),(0,r.jsx)("p",{className:"text-sm p-2 bg-muted rounded max-h-20 overflow-y-auto",children:B.description})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(d.$,{type:"button",size:"sm",onClick:()=>{B&&(j(e=>({...e,title:B.title,description:B.description})),L(!1),N.oR.success("Suggestions applied to form"))},className:"flex-1",children:"Accept Suggestions"}),(0,r.jsx)(d.$,{type:"button",variant:"outline",size:"sm",onClick:Q,className:"flex-1",children:"Reject"})]})]}):(0,r.jsx)("div",{className:"border border-dashed border-border rounded-lg p-8 text-center text-muted-foreground",children:(0,r.jsx)("p",{className:"text-sm",children:"AI suggestions will appear here"})})})]})})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{children:"Basic Information"}),(0,r.jsx)(o.BT,{children:"Enter the basic details of your product"})]}),(0,r.jsxs)(o.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(w.J,{htmlFor:"title",children:"Product Title *"}),(0,r.jsx)(c.p,{id:"title",name:"title",value:p.title,onChange:H,placeholder:"Enter product title",required:!0,disabled:k})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(w.J,{htmlFor:"category",children:"Category *"}),(0,r.jsxs)("select",{id:"category",name:"category",value:p.category,onChange:H,className:"w-full px-3 py-2 border border-border rounded-md bg-background text-foreground",required:!0,disabled:k,children:[(0,r.jsx)("option",{value:"",children:"Select a category"}),["electronics","clothing","home-garden","sports","books","toys","beauty","automotive","food","other"].map(e=>(0,r.jsx)("option",{value:e,children:e.charAt(0).toUpperCase()+e.slice(1).replace("-"," & ")},e))]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(w.J,{htmlFor:"description",children:"Description *"}),(0,r.jsx)(A,{id:"description",name:"description",value:p.description,onChange:H,placeholder:"Enter product description",rows:4,required:!0,disabled:k}),(0,r.jsx)(E,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(w.J,{htmlFor:"price",children:"Price ($) *"}),(0,r.jsx)(c.p,{id:"price",name:"price",type:"number",step:"0.01",min:"0",value:p.price,onChange:H,placeholder:"0.00",required:!0,disabled:k})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(w.J,{htmlFor:"weight",children:"Weight *"}),(0,r.jsx)(c.p,{id:"weight",name:"weight",value:p.weight,onChange:H,placeholder:"e.g., 500g, 1.2kg",required:!0,disabled:k})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(w.J,{htmlFor:"stock",children:"Stock Quantity *"}),(0,r.jsx)(c.p,{id:"stock",name:"stock",type:"number",min:"0",value:p.stock,onChange:H,placeholder:"0",required:!0,disabled:k})]})]})]})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{children:"Product Images"}),(0,r.jsx)(o.BT,{children:T?"Current images are shown below. Upload new images to replace them.":"Upload high-quality images of your product (max 5MB each)"})]}),(0,r.jsxs)(o.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"border-2 border-dashed border-border rounded-lg p-6 text-center",children:[(0,r.jsx)("input",{ref:q,type:"file",multiple:!0,accept:"image/*",onChange:e=>{let s=Array.from(e.target.files||[]);if(0===s.length)return;let t=["image/jpeg","image/jpg","image/png","image/webp"];return s.filter(e=>!t.includes(e.type)).length>0?void N.oR.error("Please select only image files (JPEG, PNG, WebP)"):s.filter(e=>e.size>5242880).length>0?void N.oR.error("Please select images smaller than 5MB"):void(f(e=>[...e,...s]),s.forEach(e=>{let s=new FileReader;s.onload=e=>{y(s=>{var t;return[...s,null==(t=e.target)?void 0:t.result]})},s.readAsDataURL(e)}))},className:"hidden",disabled:k}),(0,r.jsx)(S,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-lg font-medium mb-2",children:"Upload Product Images"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"Drag and drop images here, or click to browse"}),(0,r.jsxs)(d.$,{type:"button",variant:"outline",onClick:()=>{var e;return null==(e=q.current)?void 0:e.click()},disabled:k,children:[(0,r.jsx)(F.A,{className:"h-4 w-4 mr-2"}),"Choose Images"]})]}),b.length>0&&(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:b.map((e,s)=>(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)(l.default,{src:e,alt:"Preview ".concat(s+1),width:200,height:128,className:"w-full h-32 object-cover rounded-lg border border-border"}),(0,r.jsx)(d.$,{type:"button",variant:"destructive",size:"icon",className:"absolute top-2 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity",onClick:()=>O(s),disabled:k,children:(0,r.jsx)(R.A,{className:"h-3 w-3"})})]},s))})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,r.jsx)(d.$,{type:"button",variant:"outline",onClick:h,disabled:k,children:"Cancel"}),(0,r.jsx)(d.$,{type:"submit",disabled:k,children:k?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(M.A,{className:"mr-2 h-4 w-4 animate-spin"}),T?"Updating...":"Creating..."]}):T?"Update Product":"Create Product"})]})]})]})};var $=t(4838);let q=e=>{var s,t;let a,{product:i,onEdit:c,onDelete:u}=e;return(0,r.jsxs)(o.Zp,{className:"overflow-hidden",children:[(0,r.jsxs)("div",{className:"aspect-square relative",children:[(null==(t=i.images)||null==(s=t[0])?void 0:s.url)?(0,r.jsx)(l.default,{src:i.images[0].url,alt:i.title,fill:!0,className:"object-cover"}):(0,r.jsx)("div",{className:"w-full h-full bg-muted flex items-center justify-center",children:(0,r.jsx)(j.A,{className:"h-12 w-12 text-muted-foreground"})}),(0,r.jsx)("div",{className:"absolute top-2 right-2",children:(0,r.jsxs)($.rI,{children:[(0,r.jsx)($.ty,{asChild:!0,children:(0,r.jsx)(d.$,{variant:"secondary",size:"icon",className:"h-8 w-8",children:(0,r.jsx)(v.A,{className:"h-4 w-4"})})}),(0,r.jsxs)($.SQ,{align:"end",children:[(0,r.jsxs)($._2,{onClick:c,children:[(0,r.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Edit"]}),(0,r.jsxs)($._2,{children:[(0,r.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"View Details"]}),(0,r.jsxs)($._2,{onClick:u,className:"text-destructive",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})})]}),(0,r.jsxs)(o.Wu,{className:"p-4",children:[(0,r.jsx)("h3",{className:"font-semibold text-lg mb-2 line-clamp-2",children:i.title}),(0,r.jsx)("p",{className:"text-muted-foreground text-sm mb-3 line-clamp-2",children:i.description}),(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsx)("span",{className:"text-2xl font-bold text-primary",children:(a=i.price,new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(a))}),(0,r.jsx)(n.E,{variant:i.stock>0?"default":"destructive",children:i.stock>0?"".concat(i.stock," in stock"):"Out of stock"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm text-muted-foreground",children:[(0,r.jsx)("span",{className:"capitalize",children:i.category}),(0,r.jsx)("span",{children:i.weight})]})]})]})},U=()=>{let[e,s]=(0,a.useState)([]),[t,l]=(0,a.useState)(!0),[n,v]=(0,a.useState)(""),[f,b]=(0,a.useState)(""),[y,w]=(0,a.useState)(""),[k,A]=(0,a.useState)(!1),[P,C]=(0,a.useState)(null),[S,R]=(0,a.useState)(1),[M,E]=(0,a.useState)(1),F=async()=>{try{l(!0);let e=await i.jU.getAllProducts();e.success&&e.data?(s(e.data.products),E(e.data.totalPages),R(e.data.currentPage)):v(e.message||"Failed to load products")}catch(r){var e,t;let s=(null==(t=r.response)||null==(e=t.data)?void 0:e.message)||"An error occurred while loading products";v(s),N.oR.error(s)}finally{l(!1)}};(0,a.useEffect)(()=>{F()},[]);let $=async e=>{if(confirm("Are you sure you want to delete this product?"))try{let s=await i.ZJ.deleteProduct(e);s.success?(N.oR.success("Product deleted successfully"),F()):N.oR.error(s.message||"Failed to delete product")}catch(r){var s,t;let e=(null==(t=r.response)||null==(s=t.data)?void 0:s.message)||"An error occurred while deleting product";N.oR.error(e)}},U=e=>{C(e),A(!0)},D=()=>{A(!1),C(null)},Z=e.filter(e=>{let s=e.title.toLowerCase().includes(f.toLowerCase())||e.description.toLowerCase().includes(f.toLowerCase()),t=!y||e.category===y;return s&&t}),z=Array.from(new Set(e.map(e=>e.category)));return k?(0,r.jsx)(J,{product:P,onClose:D,onSuccess:()=>{D(),F()}}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Product Management"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage your product inventory, add new products, and update existing ones."})]}),(0,r.jsxs)(d.$,{onClick:()=>A(!0),className:"flex items-center",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Add Product"]})]}),(0,r.jsx)(o.Zp,{children:(0,r.jsx)(o.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(c.p,{placeholder:"Search products...",value:f,onChange:e=>b(e.target.value),className:"pl-10"})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("select",{value:y,onChange:e=>w(e.target.value),className:"px-3 py-2 border border-border rounded-md bg-background text-foreground",children:[(0,r.jsx)("option",{value:"",children:"All Categories"}),z.map(e=>(0,r.jsx)("option",{value:e,children:e.charAt(0).toUpperCase()+e.slice(1)},e))]}),(0,r.jsx)(d.$,{variant:"outline",size:"icon",children:(0,r.jsx)(p.A,{className:"h-4 w-4"})})]})]})})}),n&&(0,r.jsxs)(u.Fc,{variant:"destructive",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{children:"Error loading products"}),(0,r.jsx)("p",{children:n})]})]}),t&&(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((e,s)=>(0,r.jsx)(o.Zp,{children:(0,r.jsxs)(o.Wu,{className:"p-4",children:[(0,r.jsx)(m.E,{className:"h-48 w-full mb-4"}),(0,r.jsx)(m.E,{className:"h-4 w-3/4 mb-2"}),(0,r.jsx)(m.E,{className:"h-4 w-1/2 mb-2"}),(0,r.jsx)(m.E,{className:"h-4 w-1/4"})]})},s))}),!t&&!n&&(0,r.jsx)(r.Fragment,{children:0===Z.length?(0,r.jsx)(o.Zp,{children:(0,r.jsxs)(o.Wu,{className:"p-8 text-center",children:[(0,r.jsx)(j.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No products found"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:f||y?"No products match your current filters.":"Get started by adding your first product."}),(0,r.jsxs)(d.$,{onClick:()=>A(!0),children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Add Product"]})]})}):(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Z.map(e=>(0,r.jsx)(q,{product:e,onEdit:()=>U(e),onDelete:()=>$(e._id)},e._id))})}),M>1&&(0,r.jsxs)("div",{className:"flex justify-center space-x-2",children:[(0,r.jsx)(d.$,{variant:"outline",onClick:()=>R(e=>Math.max(e-1,1)),disabled:1===S,children:"Previous"}),(0,r.jsxs)("span",{className:"flex items-center px-4 py-2 text-sm text-muted-foreground",children:["Page ",S," of ",M]}),(0,r.jsx)(d.$,{variant:"outline",onClick:()=>R(e=>Math.min(e+1,M)),disabled:S===M,children:"Next"})]})]})}},9869:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,9078,8543,6766,3888,9449,7389,5193,8441,1684,7358],()=>s(1785)),_N_E=e.O()}]);