(()=>{var e={};e.id=93,e.ids=[93],e.modules={1270:(e,r,t)=>{Promise.resolve().then(t.bind(t,7956))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7956:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminProtectedRoute.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminProtectedRoute.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16189:(e,r,t)=>{"use strict";var s=t(65773);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43983:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(37413),i=t(7956);function n(){return(0,s.jsx)(i.default,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Analytics & Reports"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"View detailed analytics, sales reports, and performance metrics."})]}),(0,s.jsxs)("div",{className:"bg-card p-8 rounded-lg border border-border text-center",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Analytics Dashboard Coming Soon"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Advanced analytics and reporting features will be available in the next update."})]})]})})}},48126:(e,r,t)=>{Promise.resolve().then(t.bind(t,73482))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58697:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>l});var s=t(65239),i=t(48088),n=t(88170),a=t.n(n),o=t(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l={children:["",{children:["admin",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,43983)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\analytics\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,99111)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\analytics\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/analytics/page",pathname:"/admin/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73482:(e,r,t)=>{"use strict";t.d(r,{default:()=>l});var s=t(60687);t(43210);var i=t(58873),n=t(91821),a=t(93613),o=t(99891);let d=(e,r)=>"admin"===r?"admin"===e.role||"superAdmin"===e.role:"superAdmin"===r&&"superAdmin"===e.role,l=({children:e,requiredRole:r,fallback:t})=>{let{admin:l,isAuthenticated:u,isLoading:c}=(0,i.b)();return c?t||(0,s.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})}):u&&l?r&&!d(l,r)?t||(0,s.jsxs)(n.Fc,{variant:"destructive",children:[(0,s.jsx)(o.A,{className:"h-4 w-4"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{children:"Insufficient Permissions"}),(0,s.jsxs)("p",{children:["You need ",r," privileges to access this page. Your current role is: ",l.role]})]})]}):(0,s.jsx)(s.Fragment,{children:e}):t||(0,s.jsxs)(n.Fc,{variant:"destructive",children:[(0,s.jsx)(a.A,{className:"h-4 w-4"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{children:"Access Denied"}),(0,s.jsx)("p",{children:"You must be logged in as an admin to access this page."})]})]})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,162,658,367,10],()=>t(58697));module.exports=s})();