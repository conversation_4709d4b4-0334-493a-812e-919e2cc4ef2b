(()=>{var e={};e.id=99,e.ids=[99],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7956:(e,s,r)=>{"use strict";r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminProtectedRoute.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminProtectedRoute.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16189:(e,s,r)=>{"use strict";var t=r(65773);r.o(t,"useParams")&&r.d(s,{useParams:function(){return t.useParams}}),r.o(t,"usePathname")&&r.d(s,{usePathname:function(){return t.usePathname}}),r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26068:(e,s,r)=>{Promise.resolve().then(r.bind(r,7956)),Promise.resolve().then(r.bind(r,53653))},27910:e=>{"use strict";e.exports=require("stream")},27985:(e,s,r)=>{"use strict";r.d(s,{default:()=>k});var t=r(60687),a=r(43210),i=r(58376),n=r(29523),l=r(89667),d=r(54300),c=r(44493),o=r(96834),m=r(91821),u=r(85726),x=r(96474),h=r(23026);let p=(0,r(62688).A)("crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]);var j=r(99891),f=r(99270),v=r(93613),g=r(88233),A=r(28559),N=r(12597),b=r(13861),y=r(41862),w=r(93853),P=r(58873);let C=({admin:e,currentAdminEmail:s,onDelete:r})=>{let a=e.email===s;return(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center",children:"superAdmin"===e.role?(0,t.jsx)(p,{className:"h-6 w-6 text-purple-600"}):(0,t.jsx)(j.A,{className:"h-6 w-6 text-blue-600"})}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg",children:e.fullName}),a&&(0,t.jsx)(o.E,{variant:"outline",children:"You"})]}),(0,t.jsx)("p",{className:"text-muted-foreground",children:e.email}),(0,t.jsx)(o.E,{variant:"superAdmin"===e.role?"default":"secondary",children:"superAdmin"===e.role?"Super Admin":"Admin"})]})]}),(0,t.jsx)("div",{className:"flex items-center space-x-2",children:!a&&(0,t.jsxs)(n.$,{variant:"destructive",size:"sm",onClick:r,children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-1"}),"Delete"]})})]})})})},R=({onClose:e,onSuccess:s})=>{let[r,o]=(0,a.useState)({fullName:"",email:"",password:"",role:"admin"}),[u,x]=(0,a.useState)(!1),[h,p]=(0,a.useState)(!1),[j,f]=(0,a.useState)(""),g=e=>{let{name:s,value:r}=e.target;o(e=>({...e,[s]:r})),j&&f("")},P=()=>r.fullName.trim()?r.email.trim()?!!r.password&&!(r.password.length<6)||(f("Password must be at least 6 characters long"),!1):(f("Email is required"),!1):(f("Full name is required"),!1),C=async e=>{if(e.preventDefault(),P()){p(!0),f("");try{let e=await i.ZJ.signUp(r);e.success?(w.oR.success("Admin account created successfully"),s()):f(e.message||"Failed to create admin account")}catch(s){let e=s.response?.data?.message||"An error occurred while creating admin account";f(e),w.oR.error(e)}finally{p(!1)}}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(n.$,{variant:"ghost",size:"icon",onClick:e,children:(0,t.jsx)(A.A,{className:"h-4 w-4"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Add New Admin"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Create a new admin account with appropriate permissions"})]})]}),(0,t.jsxs)(c.Zp,{className:"max-w-md",children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{children:"Admin Account Details"}),(0,t.jsx)(c.BT,{children:"Fill in the information to create a new admin account"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("form",{onSubmit:C,className:"space-y-4",children:[j&&(0,t.jsxs)(m.Fc,{variant:"destructive",children:[(0,t.jsx)(v.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{children:"Error"}),(0,t.jsx)("p",{children:j})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.J,{htmlFor:"fullName",children:"Full Name *"}),(0,t.jsx)(l.p,{id:"fullName",name:"fullName",value:r.fullName,onChange:g,placeholder:"Enter full name",required:!0,disabled:h})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.J,{htmlFor:"email",children:"Email *"}),(0,t.jsx)(l.p,{id:"email",name:"email",type:"email",value:r.email,onChange:g,placeholder:"Enter email address",required:!0,disabled:h})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.J,{htmlFor:"password",children:"Password *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(l.p,{id:"password",name:"password",type:u?"text":"password",value:r.password,onChange:g,placeholder:"Enter password (min 6 characters)",required:!0,disabled:h}),(0,t.jsx)(n.$,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>x(!u),disabled:h,children:u?(0,t.jsx)(N.A,{className:"h-4 w-4"}):(0,t.jsx)(b.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.J,{htmlFor:"role",children:"Role *"}),(0,t.jsxs)("select",{id:"role",name:"role",value:r.role,onChange:g,className:"w-full px-3 py-2 border border-border rounded-md bg-background text-foreground",required:!0,disabled:h,children:[(0,t.jsx)("option",{value:"admin",children:"Admin"}),(0,t.jsx)("option",{value:"superAdmin",children:"Super Admin"})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-4 pt-4",children:[(0,t.jsx)(n.$,{type:"button",variant:"outline",onClick:e,disabled:h,children:"Cancel"}),(0,t.jsx)(n.$,{type:"submit",disabled:h,children:h?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating..."]}):"Create Admin"})]})]})})]})]})},k=()=>{let{admin:e}=(0,P.b)(),[s,r]=(0,a.useState)([]),[d,o]=(0,a.useState)(!0),[g,A]=(0,a.useState)(""),[N,b]=(0,a.useState)(""),[y,k]=(0,a.useState)(!1),q=async()=>{try{o(!0);let e=await i.ZJ.getAllAdmins();e.success&&e.data?r(e.data.admins):A(e.message||"Failed to load admin users")}catch(s){let e=s.response?.data?.message||"An error occurred while loading admin users";A(e),w.oR.error(e)}finally{o(!1)}};(0,a.useEffect)(()=>{q()},[]);let E=async s=>{if(s===e?._id)return void w.oR.error("You cannot delete your own account");if(confirm("Are you sure you want to delete this admin account?"))try{let e=await i.ZJ.deleteAdmin(s);e.success?(w.oR.success("Admin account deleted successfully"),q()):w.oR.error(e.message||"Failed to delete admin account")}catch(s){let e=s.response?.data?.message||"An error occurred while deleting admin account";w.oR.error(e)}},F=s.filter(e=>{let s=N.toLowerCase();return e.fullName.toLowerCase().includes(s)||e.email.toLowerCase().includes(s)||e.role.toLowerCase().includes(s)}),_={total:s.length,superAdmins:s.filter(e=>"superAdmin"===e.role).length,regularAdmins:s.filter(e=>"admin"===e.role).length};return y?(0,t.jsx)(R,{onClose:()=>k(!1),onSuccess:()=>{k(!1),q()}}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Admin User Management"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Manage admin accounts, create new admin users, and control access permissions."})]}),(0,t.jsxs)(n.$,{onClick:()=>k(!0),className:"flex items-center",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Add Admin"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Admins"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:_.total})]}),(0,t.jsx)(h.A,{className:"h-8 w-8 text-muted-foreground"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Super Admins"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-purple-600",children:_.superAdmins})]}),(0,t.jsx)(p,{className:"h-8 w-8 text-purple-600"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Regular Admins"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:_.regularAdmins})]}),(0,t.jsx)(j.A,{className:"h-8 w-8 text-blue-600"})]})})})]}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"pt-6",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(l.p,{placeholder:"Search admins by name, email, or role...",value:N,onChange:e=>b(e.target.value),className:"pl-10"})]})})}),g&&(0,t.jsxs)(m.Fc,{variant:"destructive",children:[(0,t.jsx)(v.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{children:"Error loading admin users"}),(0,t.jsx)("p",{children:g})]})]}),d&&(0,t.jsx)("div",{className:"space-y-4",children:Array.from({length:3}).map((e,s)=>(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.E,{className:"h-4 w-32"}),(0,t.jsx)(u.E,{className:"h-4 w-48"})]}),(0,t.jsx)(u.E,{className:"h-8 w-20"})]})})},s))}),!d&&!g&&(0,t.jsx)(t.Fragment,{children:0===F.length?(0,t.jsx)(c.Zp,{children:(0,t.jsxs)(c.Wu,{className:"p-8 text-center",children:[(0,t.jsx)(h.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No admin users found"}),(0,t.jsx)("p",{className:"text-muted-foreground mb-4",children:N?"No admin users match your search criteria.":"No admin users have been created yet."}),(0,t.jsxs)(n.$,{onClick:()=>k(!0),children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Add First Admin"]})]})}):(0,t.jsx)("div",{className:"space-y-4",children:F.map(s=>(0,t.jsx)(C,{admin:s,currentAdminEmail:e?.email||"",onDelete:()=>E(s._id)},s._id))})})]})}},28276:(e,s,r)=>{Promise.resolve().then(r.bind(r,73482)),Promise.resolve().then(r.bind(r,27985))},28354:e=>{"use strict";e.exports=require("util")},28559:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29713:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>u,tree:()=>c});var t=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(s,d);let c={children:["",{children:["admin",{children:["admin-users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,85237)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\admin-users\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,99111)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\admin-users\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/admin-users/page",pathname:"/admin/admin-users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},33873:e=>{"use strict";e.exports=require("path")},53653:(e,s,r)=>{"use strict";r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminUserManagement.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminUserManagement.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73482:(e,s,r)=>{"use strict";r.d(s,{default:()=>c});var t=r(60687);r(43210);var a=r(58873),i=r(91821),n=r(93613),l=r(99891);let d=(e,s)=>"admin"===s?"admin"===e.role||"superAdmin"===e.role:"superAdmin"===s&&"superAdmin"===e.role,c=({children:e,requiredRole:s,fallback:r})=>{let{admin:c,isAuthenticated:o,isLoading:m}=(0,a.b)();return m?r||(0,t.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})}):o&&c?s&&!d(c,s)?r||(0,t.jsxs)(i.Fc,{variant:"destructive",children:[(0,t.jsx)(l.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{children:"Insufficient Permissions"}),(0,t.jsxs)("p",{children:["You need ",s," privileges to access this page. Your current role is: ",c.role]})]})]}):(0,t.jsx)(t.Fragment,{children:e}):r||(0,t.jsxs)(i.Fc,{variant:"destructive",children:[(0,t.jsx)(n.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{children:"Access Denied"}),(0,t.jsx)("p",{children:"You must be logged in as an admin to access this page."})]})]})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85237:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(37413),a=r(7956),i=r(53653);function n(){return(0,t.jsx)(a.default,{requiredRole:"superAdmin",children:(0,t.jsx)(i.default,{})})}},88233:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},94735:e=>{"use strict";e.exports=require("events")},96474:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,162,658,367,10],()=>r(29713));module.exports=t})();