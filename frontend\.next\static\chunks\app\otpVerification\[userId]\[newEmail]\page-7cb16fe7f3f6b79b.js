(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9213],{283:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,AuthProvider:()=>l});var a=r(5155),s=r(2115),n=r(5654);let i=(0,s.createContext)(void 0),o=()=>{let e=(0,s.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},l=e=>{let{children:t}=e,[r,o]=(0,s.useState)(null),[l,d]=(0,s.useState)(!0),c=!!r;(0,s.useEffect)(()=>{(async()=>{if(localStorage.getItem("token"))try{let e=await n.Dv.getUser();e.success&&e.data&&o(e.data)}catch(e){e.response&&(console.error("Error checking auth status:",e.response.data.message),localStorage.removeItem("token"))}d(!1)})()},[]);let u=async()=>{try{await n.Dv.logout()}catch(e){console.error("Error during logout:",e)}finally{localStorage.removeItem("token"),o(null)}},f=async()=>{try{let e=await n.Dv.getUser();e.success&&e.data&&o(e.data)}catch(e){console.error("Error refreshing user data:",e)}};return(0,a.jsx)(i.Provider,{value:{user:r,isLoading:l,isAuthenticated:c,login:(e,t)=>{localStorage.setItem("token",t),o(e)},logout:u,updateUser:e=>{r&&o({...r,...e})},refreshUser:f},children:t})}},285:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var a=r(5155);r(2115);var s=r(9708),n=r(2085),i=r(9434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:n,asChild:l=!1,...d}=e,c=l?s.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:r,size:n,className:t})),...d})}},968:(e,t,r)=>{"use strict";r.d(t,{b:()=>o});var a=r(2115),s=r(3655),n=r(5155),i=a.forwardRef((e,t)=>(0,n.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=i},1154:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1522:(e,t,r)=>{Promise.resolve().then(r.bind(r,5531))},2523:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var a=r(5155);r(2115);var s=r(9434);function n(e){let{className:t,type:r,...n}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},2657:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3655:(e,t,r)=>{"use strict";r.d(t,{hO:()=>l,sG:()=>o});var a=r(2115),s=r(7650),n=r(9708),i=r(5155),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,n.TL)(`Primitive.${t}`),s=a.forwardRef((e,a)=>{let{asChild:s,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(s?r:t,{...n,ref:a})});return s.displayName=`Primitive.${t}`,{...e,[t]:s}},{});function l(e,t){e&&s.flushSync(()=>e.dispatchEvent(t))}},5057:(e,t,r)=>{"use strict";r.d(t,{J:()=>i});var a=r(5155);r(2115);var s=r(968),n=r(9434);function i(e){let{className:t,...r}=e;return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},5531:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var a=r(5155),s=r(2115),n=r(6874),i=r.n(n),o=r(5695),l=r(8749),d=r(2657),c=r(1154),u=r(285),f=r(2523),p=r(5057),v=r(6695),m=r(5654),g=r(8543),h=r(283);let x=()=>{let e=(0,o.useRouter)(),t=(0,o.useParams)(),{isAuthenticated:r}=(0,h.A)(),[n,x]=(0,s.useState)(""),[b,y]=(0,s.useState)(!1),[w,j]=(0,s.useState)(!1),k=t.userId,N=t.newEmail,P=decodeURIComponent(N),R=async t=>{var a,s,i,o,l;if(t.preventDefault(),!k)return void g.oR.error("Please provide a valid user ID");if(6!==n.length)return void g.oR.error("Please enter a valid 6-digit OTP");j(!0);try{let t=await m.Dv.verifyOtp(k,n);if(r&&t.success)try{let e=await m.Dv.updateEmail({newEmail:P});e.success&&g.oR.success((null==(a=e.data)?void 0:a.message)||"Email updated successfully!")}catch(e){g.oR.error((null==(i=e.response)||null==(s=i.data)?void 0:s.message)||"Failed to update email. Please try again.")}(t.success||!N)&&(g.oR.success("OTP verification successfully."),e.push("/signin"))}catch(e){g.oR.error((null==(l=e.response)||null==(o=l.data)?void 0:o.message)||"An error occurred. Please try again.")}finally{j(!1)}},E=async()=>{try{(await m.Dv.resendOtp(k,P)).success&&g.oR.success("OTP resent successfull!")}catch(t){var e;g.oR.error((null==(e=t.response)?void 0:e.data.message)||"failed to resend otp.")}};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,a.jsxs)(v.Zp,{className:"w-full max-w-md shadow-lg rounded-2xl",children:[(0,a.jsxs)(v.aR,{className:"text-center",children:[(0,a.jsx)(v.ZB,{className:"text-2xl font-semibold",children:"OTP Verification"}),(0,a.jsx)(v.BT,{children:"Enter the 6-digit OTP sent to your email address."})]}),(0,a.jsx)(v.Wu,{children:(0,a.jsxs)("form",{onSubmit:R,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(p.J,{htmlFor:"otp",children:"One-Time Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(f.p,{id:"otp",type:b?"text":"password",value:n,onChange:e=>x(e.target.value),placeholder:"Enter 6-digit OTP",autoComplete:"off",maxLength:6,className:"pr-10"}),(0,a.jsx)("button",{type:"button",onClick:()=>y(e=>!e),className:"absolute top-1/2 right-2 -translate-y-1/2 text-muted-foreground",children:b?(0,a.jsx)(l.A,{size:18}):(0,a.jsx)(d.A,{size:18})})]})]}),(0,a.jsxs)(u.$,{type:"submit",className:"w-full",disabled:w,children:[w?(0,a.jsx)(c.A,{className:"w-4 h-4 animate-spin mr-2"}):null,w?"Verifying...":"Verify OTP"]})]})}),(0,a.jsxs)(v.wL,{className:"flex flex-col gap-2 text-sm text-center",children:[(0,a.jsxs)("p",{children:["Did not receive the OTP?"," ",(0,a.jsx)("button",{onClick:E,className:"text-primary underline",children:"Resend OTP"})]}),(0,a.jsxs)("p",{children:["Having trouble?"," ",(0,a.jsx)(i(),{href:"/contact",className:"text-primary underline",children:"Contact us"})]})]})]})})}},5695:(e,t,r)=>{"use strict";var a=r(8999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}})},6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>i,t:()=>n});var a=r(2115);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function n(...e){return t=>{let r=!1,a=e.map(e=>{let a=s(e,t);return r||"function"!=typeof a||(r=!0),a});if(r)return()=>{for(let t=0;t<a.length;t++){let r=a[t];"function"==typeof r?r():s(e[t],null)}}}}function i(...e){return a.useCallback(n(...e),e)}},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>c});var a=r(5155);r(2115);var s=r(9434);function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r})}},8749:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},9708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>o,TL:()=>i});var a=r(2115),s=r(6101),n=r(5155);function i(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...n}=e;if(a.isValidElement(r)){var i;let e,o,l=(i=r,(o=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(o=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,t){let r={...t};for(let a in t){let s=e[a],n=t[a];/^on[A-Z]/.test(a)?s&&n?r[a]=(...e)=>{let t=n(...e);return s(...e),t}:s&&(r[a]=s):"style"===a?r[a]={...s,...n}:"className"===a&&(r[a]=[s,n].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==a.Fragment&&(d.ref=t?(0,s.t)(t,l):l),a.cloneElement(r,d)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=a.forwardRef((e,r)=>{let{children:s,...i}=e,o=a.Children.toArray(s),l=o.find(d);if(l){let e=l.props.children,s=o.map(t=>t!==l?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...i,ref:r,children:a.isValidElement(e)?a.cloneElement(e,void 0,s):null})}return(0,n.jsx)(t,{...i,ref:r,children:s})});return r.displayName=`${e}.Slot`,r}var o=i("Slot"),l=Symbol("radix.slottable");function d(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,9078,8543,6874,7389,8441,1684,7358],()=>t(1522)),_N_E=e.O()}]);