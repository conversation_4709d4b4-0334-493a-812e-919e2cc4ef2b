(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1e3],{283:(e,s,a)=>{"use strict";a.d(s,{A:()=>n,AuthProvider:()=>d});var t=a(5155),r=a(2115),l=a(5654);let i=(0,r.createContext)(void 0),n=()=>{let e=(0,r.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},d=e=>{let{children:s}=e,[a,n]=(0,r.useState)(null),[d,c]=(0,r.useState)(!0),o=!!a;(0,r.useEffect)(()=>{(async()=>{if(localStorage.getItem("token"))try{let e=await l.Dv.getUser();e.success&&e.data&&n(e.data)}catch(e){e.response&&(console.error("Error checking auth status:",e.response.data.message),localStorage.removeItem("token"))}c(!1)})()},[]);let m=async()=>{try{await l.Dv.logout()}catch(e){console.error("Error during logout:",e)}finally{localStorage.removeItem("token"),n(null)}},u=async()=>{try{let e=await l.Dv.getUser();e.success&&e.data&&n(e.data)}catch(e){console.error("Error refreshing user data:",e)}};return(0,t.jsx)(i.Provider,{value:{user:a,isLoading:d,isAuthenticated:o,login:(e,s)=>{localStorage.setItem("token",s),n(e)},logout:m,updateUser:e=>{a&&n({...a,...e})},refreshUser:u},children:s})}},285:(e,s,a)=>{"use strict";a.d(s,{$:()=>d});var t=a(5155);a(2115);var r=a(9708),l=a(2085),i=a(9434);let n=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:s,variant:a,size:l,asChild:d=!1,...c}=e,o=d?r.DX:"button";return(0,t.jsx)(o,{"data-slot":"button",className:(0,i.cn)(n({variant:a,size:l,className:s})),...c})}},2357:(e,s,a)=>{"use strict";a.d(s,{default:()=>m});var t=a(5155),r=a(2115),l=a(285),i=a(6695),n=a(1243),d=a(3904),c=a(7340);class o extends r.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,s){console.error("Shop Error Boundary caught an error:",e,s)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,t.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center p-4",children:(0,t.jsx)(i.Zp,{className:"w-full max-w-lg",children:(0,t.jsxs)(i.Wu,{className:"flex flex-col items-center justify-center py-12 text-center space-y-6",children:[(0,t.jsx)("div",{className:"w-20 h-20 text-destructive",children:(0,t.jsx)(n.A,{className:"w-full h-full"})}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-foreground",children:"Something went wrong"}),(0,t.jsx)("p",{className:"text-muted-foreground max-w-md",children:"We encountered an unexpected error while loading the shop. This might be a temporary issue."}),!1]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,t.jsxs)(l.$,{onClick:this.handleRetry,className:"flex items-center gap-2",size:"lg",children:[(0,t.jsx)(d.A,{className:"w-4 h-4"}),"Try Again"]}),(0,t.jsxs)(l.$,{onClick:this.handleGoHome,variant:"outline",className:"flex items-center gap-2",size:"lg",children:[(0,t.jsx)(c.A,{className:"w-4 h-4"}),"Go Home"]})]})]})})}):this.props.children}constructor(e){super(e),this.handleRetry=()=>{this.setState({hasError:!1,error:void 0})},this.handleGoHome=()=>{window.location.href="/"},this.state={hasError:!1}}}let m=o},2523:(e,s,a)=>{"use strict";a.d(s,{p:()=>l});var t=a(5155);a(2115);var r=a(9434);function l(e){let{className:s,type:a,...l}=e;return(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...l})}},5057:(e,s,a)=>{"use strict";a.d(s,{J:()=>i});var t=a(5155);a(2115);var r=a(968),l=a(9434);function i(e){let{className:s,...a}=e;return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...a})}},5323:(e,s,a)=>{"use strict";a.d(s,{CartProvider:()=>o,_:()=>c});var t=a(5155),r=a(2115),l=a(5654),i=a(283),n=a(8543);let d=(0,r.createContext)(void 0),c=()=>{let e=(0,r.useContext)(d);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},o=e=>{let{children:s}=e,[a,c]=(0,r.useState)(null),[o,m]=(0,r.useState)(!1),[u,x]=(0,r.useState)(!1),{isAuthenticated:h,user:p}=(0,i.A)(),g=(null==a?void 0:a.length)||0;(0,r.useEffect)(()=>{h&&p?f():c(null)},[h,p,f]);let f=(0,r.useCallback)(async()=>{if(h)try{m(!0);let e=await l.CV.getCartItems();e.success&&e.data&&c(e.data)}catch(e){c(null)}finally{m(!1)}},[h]),v=async(e,s)=>{if(!h)return n.oR.error("Please sign in to add items to cart"),!1;try{x(!0);let a=await l.CV.addToCart({productId:e,quantity:s});if(a.success&&a.data)return c(a.data),n.oR.success("Item added to cart successfully"),!0;return!1}catch(e){var a;return n.oR.error((null==(a=e.response)?void 0:a.data.message)||"Failed to add item to cart"),!1}finally{x(!1)}},j=async(e,s)=>{if(!h||s<1)return!1;try{x(!0);let a=await l.CV.updateQuantity({productId:e,quantity:s});if(a.success&&a.data)return n.oR.success("Quantity updated successfully"),c(a.data),!0;return!1}catch(e){var a;return n.oR.error((null==(a=e.response)?void 0:a.data.message)||"Failed to update quantity"),!1}finally{x(!1)}},N=async e=>{if(!h)return!1;try{x(!0);let s=await l.CV.removeProduct(e);if(s.success&&s.data)return c(s.data),n.oR.success("Item removed from cart"),!0;return!1}catch(e){var s;return n.oR.error((null==(s=e.response)?void 0:s.data.message)||"Failed to remove item"),!1}finally{x(!1)}};return(0,t.jsx)(d.Provider,{value:{cart:a,isLoading:o,isUpdating:u,itemCount:g,addToCart:v,updateQuantity:j,removeFromCart:N,clearCart:()=>{c(null)},refreshCart:f,getCartCalculations:()=>{if(!a)return{subtotal:0,tax:0,shipping:0,total:0};let e=l.CV.calculateCartTotal(a),s=l.CV.calculateTax(e),t=l.CV.calculateShipping(e),r=e+s+t;return{subtotal:e,tax:s,shipping:t,total:r}}},children:s})}},5760:(e,s,a)=>{Promise.resolve().then(a.bind(a,7540))},6126:(e,s,a)=>{"use strict";a.d(s,{E:()=>d});var t=a(5155);a(2115);var r=a(9708),l=a(2085),i=a(9434);let n=(0,l.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:s,variant:a,asChild:l=!1,...d}=e,c=l?r.DX:"span";return(0,t.jsx)(c,{"data-slot":"badge",className:(0,i.cn)(n({variant:a}),s),...d})}},6695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>d,Wu:()=>c,ZB:()=>n,Zp:()=>l,aR:()=>i,wL:()=>o});var t=a(5155);a(2115);var r=a(9434);function l(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...a})}function i(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...a})}function n(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",s),...a})}function d(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",s),...a})}function c(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",s),...a})}function o(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",s),...a})}},7288:(e,s,a)=>{"use strict";a.d(s,{default:()=>l});var t=a(5155);a(2115);var r=a(6695);let l=()=>(0,t.jsx)("div",{className:"min-h-screen bg-background",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-12"}),(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-1"}),(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-12"}),(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-1"}),(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-20"}),(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-1"}),(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-32"})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 mb-16",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"aspect-square bg-muted rounded-lg animate-pulse"}),(0,t.jsx)("div",{className:"flex gap-2",children:Array.from({length:4}).map((e,s)=>(0,t.jsx)("div",{className:"w-20 h-20 bg-muted rounded-md animate-pulse"},s))})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-24"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"h-8 bg-muted rounded animate-pulse w-full"}),(0,t.jsx)("div",{className:"h-8 bg-muted rounded animate-pulse w-3/4"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("div",{className:"flex gap-1",children:Array.from({length:5}).map((e,s)=>(0,t.jsx)("div",{className:"w-5 h-5 bg-muted rounded animate-pulse"},s))}),(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-20"})]}),(0,t.jsx)("div",{className:"h-10 bg-muted rounded animate-pulse w-32"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-full"}),(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-full"}),(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-2/3"})]}),(0,t.jsx)("div",{className:"h-6 bg-muted rounded animate-pulse w-24"}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("div",{className:"h-10 bg-muted rounded animate-pulse w-24"}),(0,t.jsx)("div",{className:"h-10 bg-muted rounded animate-pulse w-40"})]}),(0,t.jsx)("div",{className:"flex gap-2",children:Array.from({length:4}).map((e,s)=>(0,t.jsx)("div",{className:"w-10 h-10 bg-muted rounded animate-pulse"},s))})]})]}),(0,t.jsx)("div",{className:"mb-16",children:(0,t.jsx)(r.Zp,{children:(0,t.jsxs)(r.Wu,{className:"p-6",children:[(0,t.jsx)("div",{className:"flex gap-6 mb-6",children:Array.from({length:3}).map((e,s)=>(0,t.jsx)("div",{className:"h-6 bg-muted rounded animate-pulse w-20"},s))}),(0,t.jsx)("div",{className:"space-y-4",children:Array.from({length:5}).map((e,s)=>(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-full"},s))})]})})}),(0,t.jsxs)("div",{className:"mb-16",children:[(0,t.jsx)("div",{className:"h-8 bg-muted rounded animate-pulse w-48 mb-6"}),(0,t.jsx)("div",{className:"space-y-4",children:Array.from({length:3}).map((e,s)=>(0,t.jsx)(r.Zp,{children:(0,t.jsx)(r.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-start gap-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-muted rounded-full animate-pulse"}),(0,t.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-32"}),(0,t.jsx)("div",{className:"flex gap-1",children:Array.from({length:5}).map((e,s)=>(0,t.jsx)("div",{className:"w-4 h-4 bg-muted rounded animate-pulse"},s))}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-full"}),(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-3/4"})]})]})]})})},s))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-8 bg-muted rounded animate-pulse w-48 mb-6"}),(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:Array.from({length:4}).map((e,s)=>(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)("div",{className:"aspect-square bg-muted animate-pulse"}),(0,t.jsxs)(r.Wu,{className:"p-4 space-y-2",children:[(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-full"}),(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-3/4"}),(0,t.jsx)("div",{className:"h-6 bg-muted rounded animate-pulse w-20"})]})]},s))})]})]})})},7540:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>en});var t=a(5155),r=a(2115),l=a(37),i=a(8543),n=a(6766),d=a(760),c=a(6408),o=a(285),m=a(6695),u=a(4481),x=a(1981),h=a(4416),p=a(2355),g=a(3052);let f=(0,r.memo)(e=>{let{images:s,productTitle:a}=e,[l,i]=(0,r.useState)(0),[f,v]=(0,r.useState)(!1),[j,N]=(0,r.useState)(!1),[b,w]=(0,r.useState)({x:0,y:0}),y=(0,r.useRef)(null),k=s[l],C=(0,r.useCallback)(e=>{i(e),N(!1)},[]),A=()=>{N(!j)},R=()=>{v(!0)},S=()=>{v(!1)},z=e=>{"prev"===e?i(e=>0===e?s.length-1:e-1):i(e=>e===s.length-1?0:e+1)};return s&&0!==s.length?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(m.Zp,{className:"relative overflow-hidden group",children:(0,t.jsxs)("div",{ref:y,className:"relative aspect-square cursor-zoom-in",onMouseMove:e=>{if(!j||!y.current)return;let s=y.current.getBoundingClientRect();w({x:(e.clientX-s.left)/s.width*100,y:(e.clientY-s.top)/s.height*100})},onMouseLeave:()=>N(!1),onClick:A,children:[(0,t.jsx)(n.default,{src:k.url,alt:"".concat(a," - Image ").concat(l+1),fill:!0,className:"object-cover transition-transform duration-300 ".concat(j?"scale-150":"scale-100"),style:j?{transformOrigin:"".concat(b.x,"% ").concat(b.y,"%")}:{},priority:0===l,quality:90,sizes:"(max-width: 768px) 100vw, 50vw"}),(0,t.jsx)("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300"}),(0,t.jsxs)("div",{className:"absolute top-4 right-4 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:[(0,t.jsx)(o.$,{size:"icon",variant:"secondary",className:"w-8 h-8 rounded-full bg-white/90 hover:bg-white shadow-md",onClick:e=>{e.stopPropagation(),A()},children:(0,t.jsx)(u.A,{className:"w-4 h-4"})}),(0,t.jsx)(o.$,{size:"icon",variant:"secondary",className:"w-8 h-8 rounded-full bg-white/90 hover:bg-white shadow-md",onClick:e=>{e.stopPropagation(),R()},children:(0,t.jsx)(x.A,{className:"w-4 h-4"})})]}),s.length>1&&(0,t.jsxs)("div",{className:"absolute bottom-4 left-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm",children:[l+1," / ",s.length]})]})}),s.length>1&&(0,t.jsx)("div",{className:"flex gap-2 overflow-x-auto pb-2",children:s.map((e,s)=>(0,t.jsx)("button",{onClick:()=>C(s),className:"relative flex-shrink-0 w-20 h-20 rounded-md overflow-hidden border-2 transition-all duration-200 ".concat(s===l?"border-primary shadow-md":"border-transparent hover:border-muted-foreground"),children:(0,t.jsx)(n.default,{src:e.url,alt:"".concat(a," - Thumbnail ").concat(s+1),fill:!0,className:"object-cover",sizes:"80px"})},s))}),(0,t.jsx)(d.N,{children:f&&(0,t.jsxs)(c.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-50 bg-black/90 flex items-center justify-center",onKeyDown:e=>{"Escape"===e.key?S():"ArrowLeft"===e.key?z("prev"):"ArrowRight"===e.key&&z("next")},tabIndex:-1,children:[(0,t.jsx)(o.$,{size:"icon",variant:"ghost",className:"absolute top-4 right-4 text-white hover:bg-white/20 z-10",onClick:S,children:(0,t.jsx)(h.A,{className:"w-6 h-6"})}),s.length>1&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(o.$,{size:"icon",variant:"ghost",className:"absolute left-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20 z-10",onClick:()=>z("prev"),children:(0,t.jsx)(p.A,{className:"w-6 h-6"})}),(0,t.jsx)(o.$,{size:"icon",variant:"ghost",className:"absolute right-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20 z-10",onClick:()=>z("next"),children:(0,t.jsx)(g.A,{className:"w-6 h-6"})})]}),(0,t.jsx)(c.P.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.8,opacity:0},className:"relative max-w-4xl max-h-[90vh] w-full h-full flex items-center justify-center p-4",children:(0,t.jsx)(n.default,{src:k.url,alt:"".concat(a," - Image ").concat(l+1),width:800,height:800,className:"max-w-full max-h-full object-contain",quality:95})},l),s.length>1&&(0,t.jsxs)("div",{className:"absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/70 text-white px-4 py-2 rounded-full",children:[l+1," of ",s.length]})]})})]}):(0,t.jsx)(m.Zp,{className:"aspect-square bg-muted flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-muted-foreground text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 mx-auto mb-2 opacity-50",children:(0,t.jsx)("svg",{fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"})})}),(0,t.jsx)("p",{children:"No image available"})]})})});var v=a(6126),j=a(2523),N=a(5057),b=a(5323),w=a(283),y=a(8564),k=a(7712),C=a(4616),A=a(7809),R=a(1976),S=a(488),z=a(8175),E=a(2894),P=a(1366),I=a(4575);let $=e=>{let{product:s,url:a}=e,r="Check out this amazing product: ".concat(s.title),l=encodeURIComponent(a),n=encodeURIComponent(r),d={facebook:"https://www.facebook.com/sharer/sharer.php?u=".concat(l),twitter:"https://twitter.com/intent/tweet?url=".concat(l,"&text=").concat(n),linkedin:"https://www.linkedin.com/sharing/share-offsite/?url=".concat(l),whatsapp:"https://wa.me/?text=".concat(n,"%20").concat(l)},c=e=>{let s=d[e];window.open(s,"_blank","width=600,height=400")},m=async()=>{try{await navigator.clipboard.writeText(a),i.oR.success("Link copied to clipboard!")}catch(e){console.error("Failed to copy link:",e),i.oR.error("Failed to copy link")}},u=async()=>{if(navigator.share)try{await navigator.share({title:s.title,text:r,url:a})}catch(e){console.error("Error sharing:",e)}else m()};return(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,t.jsx)(o.$,{variant:"outline",size:"icon",onClick:()=>c("facebook"),className:"w-10 h-10",title:"Share on Facebook",children:(0,t.jsx)(S.A,{className:"w-4 h-4"})}),(0,t.jsx)(o.$,{variant:"outline",size:"icon",onClick:()=>c("twitter"),className:"w-10 h-10",title:"Share on Twitter",children:(0,t.jsx)(z.A,{className:"w-4 h-4"})}),(0,t.jsx)(o.$,{variant:"outline",size:"icon",onClick:()=>c("linkedin"),className:"w-10 h-10",title:"Share on LinkedIn",children:(0,t.jsx)(E.A,{className:"w-4 h-4"})}),(0,t.jsx)(o.$,{variant:"outline",size:"icon",onClick:()=>c("whatsapp"),className:"w-10 h-10",title:"Share on WhatsApp",children:(0,t.jsx)(P.A,{className:"w-4 h-4"})}),(0,t.jsx)(o.$,{variant:"outline",size:"icon",onClick:u,className:"w-10 h-10",title:"Copy link",children:(0,t.jsx)(I.A,{className:"w-4 h-4"})})]})},_=e=>{let s,{product:a,reviews:l}=e,[n,d]=(0,r.useState)(1),[c,u]=(0,r.useState)(!1),{addToCart:x,isUpdating:h}=(0,b._)(),{isAuthenticated:p}=(0,w.A)(),g=e=>{e>=1&&e<=a.stock&&d(e)},f=async()=>{if(!p)return void i.oR.error("Please sign in to add items to cart");await x(a._id,n)&&d(1)},S=0===a.stock,z=a.stock>0&&a.stock<=5,E=a.averageRating||0,P=l.length;return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{children:(0,t.jsx)(v.E,{variant:"secondary",className:"text-xs sm:text-sm",children:a.category})}),(0,t.jsx)("div",{children:(0,t.jsx)("h1",{className:"text-2xl sm:text-3xl md:text-4xl font-bold text-foreground leading-tight",children:a.title})}),(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-2 sm:gap-4",children:[(0,t.jsx)("div",{className:"flex items-center gap-1",children:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,s=[],a=Math.floor(e),r=e%1!=0;for(let e=0;e<5;e++)e<a?s.push((0,t.jsx)(y.A,{className:"w-5 h-5 fill-yellow-400 text-yellow-400"},e)):e===a&&r?s.push((0,t.jsxs)("div",{className:"relative w-5 h-5",children:[(0,t.jsx)(y.A,{className:"w-5 h-5 text-gray-300 absolute"}),(0,t.jsx)("div",{className:"overflow-hidden w-1/2",children:(0,t.jsx)(y.A,{className:"w-5 h-5 fill-yellow-400 text-yellow-400"})})]},e)):s.push((0,t.jsx)(y.A,{className:"w-5 h-5 text-gray-300"},e));return s}(E)}),(0,t.jsxs)("span",{className:"text-sm text-muted-foreground",children:[E.toFixed(1)," (",P," review",1!==P?"s":"",")"]})]}),(0,t.jsx)("div",{className:"space-y-1",children:(0,t.jsx)("div",{className:"text-2xl sm:text-3xl font-bold text-primary",children:(s=a.price,new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(s))})}),(0,t.jsx)("div",{className:"prose prose-sm max-w-none",children:(0,t.jsx)("p",{className:"text-muted-foreground leading-relaxed",children:a.description})}),(0,t.jsx)("div",{children:S?(0,t.jsx)(v.E,{variant:"destructive",className:"text-xs sm:text-sm",children:"Out of Stock"}):z?(0,t.jsxs)(v.E,{className:"bg-orange-500 hover:bg-orange-600 text-xs sm:text-sm",children:["Only ",a.stock," left in stock"]}):(0,t.jsxs)(v.E,{className:"bg-green-500 hover:bg-green-600 text-xs sm:text-sm",children:["In Stock (",a.stock," available)"]})}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2 max-w-xs",children:[(0,t.jsx)(N.J,{htmlFor:"quantity",className:"text-sm font-medium",children:"Quantity"}),(0,t.jsxs)("div",{className:"flex items-center w-full border rounded-md overflow-hidden",children:[(0,t.jsx)(o.$,{variant:"ghost",size:"icon",className:"h-10 w-10",onClick:()=>g(n-1),disabled:n<=1,children:(0,t.jsx)(k.A,{className:"w-4 h-4"})}),(0,t.jsx)(j.p,{id:"quantity",type:"number",min:"1",max:a.stock,value:n,onChange:e=>g(parseInt(e.target.value)||1),className:"h-10 w-20 text-center border-0 focus-visible:ring-0"}),(0,t.jsx)(o.$,{variant:"ghost",size:"icon",className:"h-10 w-10",onClick:()=>g(n+1),disabled:n>=a.stock,children:(0,t.jsx)(C.A,{className:"w-4 h-4"})})]})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 w-full",children:[(0,t.jsxs)(o.$,{onClick:f,disabled:h||S,className:"w-full sm:flex-1 h-12 text-base",size:"lg",children:[(0,t.jsx)(A.A,{className:"w-5 h-5 mr-2"}),h?"Adding...":S?"Out of Stock":"Add to Cart"]}),(0,t.jsx)(o.$,{variant:"outline",onClick:()=>{if(!p)return void i.oR.error("Please sign in to add to wishlist");u(!c),i.oR.success(c?"Removed from wishlist":"Added to wishlist")},className:"h-12 px-6 w-full sm:w-auto",size:"lg",children:(0,t.jsx)(R.A,{className:"w-5 h-5 ".concat(c?"fill-red-500 text-red-500":"")})})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(N.J,{className:"text-sm font-medium",children:"Share this product"}),(0,t.jsx)($,{product:a,url:"".concat(window.location.origin,"/product/").concat(a._id)})]}),(0,t.jsx)(m.Zp,{className:"w-full",children:(0,t.jsx)(m.Wu,{className:"p-4 sm:p-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg",children:"Specifications"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between py-2 border-b",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Weight"}),(0,t.jsx)("span",{className:"font-medium",children:a.weight})]}),(0,t.jsxs)("div",{className:"flex justify-between py-2 border-b",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Category"}),(0,t.jsx)("span",{className:"font-medium",children:a.category})]}),(0,t.jsxs)("div",{className:"flex justify-between py-2 border-b",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Stock"}),(0,t.jsxs)("span",{className:"font-medium",children:[a.stock," units"]})]}),(0,t.jsxs)("div",{className:"flex justify-between py-2",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Product ID"}),(0,t.jsx)("span",{className:"font-medium text-xs",children:a._id})]})]})]})})})]})};var L=a(1284),D=a(7108),F=a(1497);let W=e=>{let{product:s,reviews:a,isLoadingReviews:l}=e,[i,n]=(0,r.useState)("description"),d=[{id:"description",label:"Description",icon:L.A,count:null},{id:"specifications",label:"Specifications",icon:D.A,count:null},{id:"reviews",label:"Reviews",icon:F.A,count:a.length}],c=e=>Array.from({length:5},(s,a)=>(0,t.jsx)(y.A,{className:"w-4 h-4 ".concat(a<e?"fill-yellow-400 text-yellow-400":"text-gray-300")},a)),u=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return(0,t.jsxs)(m.Zp,{children:[(0,t.jsx)("div",{className:"border-b",children:(0,t.jsx)("div",{className:"flex flex-wrap sm:flex-nowrap",children:d.map(e=>{let s=e.icon;return(0,t.jsxs)("button",{onClick:()=>n(e.id),className:"flex items-center gap-2 px-4 py-3 text-sm font-medium border-b-2 transition-colors w-full sm:w-auto ".concat(i===e.id?"border-primary text-primary bg-primary/5":"border-transparent text-muted-foreground hover:text-foreground hover:bg-muted/40"),children:[(0,t.jsx)(s,{className:"w-4 h-4"}),e.label,null!==e.count&&(0,t.jsx)(v.E,{variant:"secondary",className:"ml-1 text-xs",children:e.count})]},e.id)})})}),(0,t.jsx)(m.Wu,{className:"p-4 sm:p-6",children:(()=>{switch(i){case"description":return(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Product Description"}),(0,t.jsx)("p",{className:"text-muted-foreground leading-relaxed",children:s.description}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-6 mt-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"Key Features"}),(0,t.jsxs)("ul",{className:"space-y-1 text-sm text-muted-foreground",children:[(0,t.jsx)("li",{children:"• High-quality materials"}),(0,t.jsx)("li",{children:"• Durable construction"}),(0,t.jsx)("li",{children:"• Easy to use"}),(0,t.jsx)("li",{children:"• Great value for money"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"What's Included"}),(0,t.jsxs)("ul",{className:"space-y-1 text-sm text-muted-foreground",children:[(0,t.jsxs)("li",{children:["• 1x ",s.title]}),(0,t.jsx)("li",{children:"• User manual"}),(0,t.jsx)("li",{children:"• Warranty card"}),(0,t.jsx)("li",{children:"• Original packaging"})]})]})]})]});case"specifications":return(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Product Specifications"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between border-b py-2",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Category"}),(0,t.jsx)("span",{className:"font-medium",children:s.category})]}),(0,t.jsxs)("div",{className:"flex justify-between border-b py-2",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Weight"}),(0,t.jsx)("span",{className:"font-medium",children:s.weight})]}),(0,t.jsxs)("div",{className:"flex justify-between border-b py-2",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Stock"}),(0,t.jsxs)("span",{className:"font-medium",children:[s.stock," units"]})]}),(0,t.jsxs)("div",{className:"flex justify-between border-b py-2",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Product ID"}),(0,t.jsx)("span",{className:"font-medium text-xs",children:s._id})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between border-b py-2",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Price"}),(0,t.jsxs)("span",{className:"font-medium",children:["$",s.price]})]}),(0,t.jsxs)("div",{className:"flex justify-between border-b py-2",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Rating"}),(0,t.jsx)("span",{className:"font-medium",children:s.averageRating?"".concat(s.averageRating.toFixed(1),"/5"):"No ratings"})]}),(0,t.jsxs)("div",{className:"flex justify-between border-b py-2",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Created"}),(0,t.jsx)("span",{className:"font-medium",children:u(s.createdAt)})]}),(0,t.jsxs)("div",{className:"flex justify-between border-b py-2",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Updated"}),(0,t.jsx)("span",{className:"font-medium",children:u(s.updatedAt)})]})]})]})]});case"reviews":return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Customer Reviews"}),l?(0,t.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0].map((e,s)=>(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"h-4 w-32 bg-muted rounded animate-pulse"}),(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-full"}),(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-3/4"})]},s))}):0===a.length?(0,t.jsx)("p",{className:"text-center text-muted-foreground",children:"No reviews yet. Be the first to review this product!"}):(0,t.jsxs)("div",{className:"space-y-4",children:[a.slice(0,3).map(e=>{var s;return(0,t.jsx)("div",{className:"border-b pb-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full bg-muted text-sm flex items-center justify-center font-bold",children:null==e||null==(s=e.fullName)?void 0:s.charAt(0).toUpperCase()}),(0,t.jsxs)("div",{className:"flex-1 space-y-1",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("p",{className:"font-medium text-sm",children:e.fullName}),(0,t.jsx)("span",{className:"text-xs text-muted-foreground",children:u(e.createdAt)})]}),(0,t.jsx)("div",{className:"flex items-center gap-1",children:c(e.rating)}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.comment})]})]})},e._id)}),a.length>3&&(0,t.jsx)("div",{className:"text-center pt-4",children:(0,t.jsxs)(o.$,{variant:"outline",onClick:()=>{let e=document.getElementById("reviews-section");e&&e.scrollIntoView({behavior:"smooth"})},children:["View All ",a.length," Reviews"]})})]})]});default:return null}})()})]})};var B=a(6874),Z=a.n(B),U=a(2138);let V=e=>{let s,{product:a}=e,r=0===a.stock;return(0,t.jsx)(Z(),{href:"/product/".concat(a._id),children:(0,t.jsxs)(m.Zp,{className:"group hover:shadow-lg transition-all duration-300 cursor-pointer",children:[(0,t.jsxs)("div",{className:"relative aspect-square bg-muted overflow-hidden",children:[a.images&&a.images.length>0?(0,t.jsx)(n.default,{src:a.images[0].url,alt:a.title,fill:!0,className:"object-cover group-hover:scale-105 transition-transform duration-300"}):(0,t.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,t.jsx)("div",{className:"w-16 h-16 text-muted-foreground opacity-50",children:(0,t.jsx)("svg",{fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"})})})}),r&&(0,t.jsx)("div",{className:"absolute top-2 left-2",children:(0,t.jsx)(v.E,{variant:"destructive",className:"text-xs",children:"Out of Stock"})})]}),(0,t.jsxs)(m.Wu,{className:"p-4 space-y-2",children:[(0,t.jsx)("div",{className:"text-xs text-muted-foreground uppercase tracking-wide",children:a.category}),(0,t.jsx)("h3",{className:"font-medium text-sm leading-tight line-clamp-2 min-h-[2.5rem] group-hover:text-primary transition-colors",children:a.title}),a.averageRating&&(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)("div",{className:"flex items-center",children:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,s=[],a=Math.floor(e),r=e%1!=0;for(let e=0;e<5;e++)e<a?s.push((0,t.jsx)(y.A,{className:"w-3 h-3 fill-yellow-400 text-yellow-400"},e)):e===a&&r?s.push((0,t.jsxs)("div",{className:"relative w-3 h-3",children:[(0,t.jsx)(y.A,{className:"w-3 h-3 text-gray-300 absolute"}),(0,t.jsx)("div",{className:"overflow-hidden w-1/2",children:(0,t.jsx)(y.A,{className:"w-3 h-3 fill-yellow-400 text-yellow-400"})})]},e)):s.push((0,t.jsx)(y.A,{className:"w-3 h-3 text-gray-300"},e));return s}(a.averageRating)}),(0,t.jsxs)("span",{className:"text-xs text-muted-foreground",children:["(",a.averageRating.toFixed(1),")"]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"font-bold text-primary",children:(s=a.price,new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(s))}),a.stock>0&&a.stock<=5&&(0,t.jsx)(v.E,{className:"bg-orange-500 hover:bg-orange-600 text-xs",children:"Low Stock"})]})]})]})})},q=e=>{let{products:s,isLoading:a}=e;return a?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"h-8 bg-muted rounded animate-pulse w-48"}),(0,t.jsx)("div",{className:"h-10 bg-muted rounded animate-pulse w-32"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:Array.from({length:4}).map((e,s)=>(0,t.jsxs)(m.Zp,{children:[(0,t.jsx)("div",{className:"aspect-square bg-muted animate-pulse"}),(0,t.jsxs)(m.Wu,{className:"p-4 space-y-2",children:[(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-full"}),(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-3/4"}),(0,t.jsx)("div",{className:"h-6 bg-muted rounded animate-pulse w-20"})]})]},s))})]}):0===s.length?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold",children:"Related Products"}),(0,t.jsx)(m.Zp,{children:(0,t.jsxs)(m.Wu,{className:"flex flex-col items-center justify-center py-16 text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 mb-4 text-muted-foreground",children:(0,t.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",className:"w-full h-full",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H6a1 1 0 00-1 1v1m16 0H4"})})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-foreground mb-2",children:"No related products found"}),(0,t.jsx)("p",{className:"text-muted-foreground max-w-md mb-4",children:"We couldn't find any related products at the moment. Check out our full catalog instead."}),(0,t.jsx)(o.$,{asChild:!0,children:(0,t.jsxs)(Z(),{href:"/shop",children:["Browse All Products",(0,t.jsx)(U.A,{className:"w-4 h-4 ml-2"})]})})]})})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold",children:"Related Products"}),(0,t.jsx)(o.$,{variant:"outline",asChild:!0,children:(0,t.jsxs)(Z(),{href:"/shop",className:"flex items-center gap-2",children:["View All",(0,t.jsx)(U.A,{className:"w-4 h-4"})]})})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:s.map((e,s)=>(0,t.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*s},children:(0,t.jsx)(V,{product:e})},e._id))}),(0,t.jsxs)("div",{className:"text-center pt-8",children:[(0,t.jsx)("p",{className:"text-muted-foreground mb-4",children:"Looking for something else?"}),(0,t.jsx)(o.$,{asChild:!0,size:"lg",children:(0,t.jsxs)(Z(),{href:"/shop",children:["Explore All Products",(0,t.jsx)(U.A,{className:"w-4 h-4 ml-2"})]})})]})]})};var M=a(9409),H=a(1007),T=a(333),O=a(5580);let J=e=>{let{productId:s,onReviewSubmitted:a,onCancel:n}=e,[d,c]=(0,r.useState)(0),[u,x]=(0,r.useState)(0),[h,p]=(0,r.useState)(""),[g,f]=(0,r.useState)(!1),{user:v}=(0,w.A)(),j=async e=>{if(e.preventDefault(),0===d)return void i.oR.error("Please select a rating");if(h.trim().length<10)return void i.oR.error("Please write a review with at least 10 characters");try{f(!0);let e={rating:d,comment:h.trim()},t=await l.j.addReview(s,e);t.success&&t.data&&(a(t.data),i.oR.success("Review submitted successfully!"),c(0),p(""))}catch(e){e.response&&i.oR.error(e.response.data.message||"Failed to submit review")}finally{f(!1)}};return(0,t.jsxs)(m.Zp,{children:[(0,t.jsx)(m.aR,{children:(0,t.jsx)(m.ZB,{children:"Write a Review"})}),(0,t.jsx)(m.Wu,{children:(0,t.jsxs)("form",{onSubmit:j,className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(N.J,{className:"text-base font-medium",children:"Rating *"}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(()=>{let e=[];for(let s=1;s<=5;s++)e.push((0,t.jsx)("button",{type:"button",onClick:()=>c(s),onMouseEnter:()=>x(s),onMouseLeave:()=>x(0),className:"focus:outline-none",children:(0,t.jsx)(y.A,{className:"w-8 h-8 transition-colors ".concat(s<=(u||d)?"fill-yellow-400 text-yellow-400":"text-gray-300 hover:text-yellow-300")})},s));return e})(),d>0&&(0,t.jsxs)("span",{className:"ml-2 text-sm text-muted-foreground",children:[d," star",1!==d?"s":""]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(N.J,{htmlFor:"comment",className:"text-base font-medium",children:"Your Review *"}),(0,t.jsx)("textarea",{id:"comment",value:h,onChange:e=>p(e.target.value),placeholder:"Share your thoughts about this product...",className:"w-full min-h-[120px] px-3 py-2 border border-input rounded-md bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 resize-vertical",required:!0,minLength:10,maxLength:1e3}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground text-right",children:[h.length,"/1000 characters"]})]}),v&&(0,t.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Reviewing as: ",(0,t.jsx)("span",{className:"font-medium",children:v.fullName})]}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsx)(o.$,{type:"submit",disabled:g||0===d||h.trim().length<10,className:"flex-1",children:g?"Submitting...":"Submit Review"}),(0,t.jsx)(o.$,{type:"button",variant:"outline",onClick:n,disabled:g,children:"Cancel"})]})]})})]})},G=e=>{let{productId:s,reviews:a,isLoading:l,onReviewSubmitted:i}=e,[n,d]=(0,r.useState)(!1),[u,x]=(0,r.useState)("newest"),[h,p]=(0,r.useState)("all"),{isAuthenticated:g}=(0,w.A)(),f=(0,r.useMemo)(()=>{let e={1:0,2:0,3:0,4:0,5:0};a.forEach(s=>{e[s.rating]++});let s=a.length,t=s?a.reduce((e,s)=>e+s.rating,0)/s:0,r={1:s?e[1]/s*100:0,2:s?e[2]/s*100:0,3:s?e[3]/s*100:0,4:s?e[4]/s*100:0,5:s?e[5]/s*100:0};return{stats:e,total:s,average:t,percentages:r}},[a]),j=(0,r.useMemo)(()=>{let e=[...a];return"all"!==h&&(e=e.filter(e=>e.rating===Number(h))),e.sort((e,s)=>"newest"===u?new Date(s.createdAt).getTime()-new Date(e.createdAt).getTime():"oldest"===u?new Date(e.createdAt).getTime()-new Date(s.createdAt).getTime():"highest"===u?s.rating-e.rating:"lowest"===u?e.rating-s.rating:0),e},[a,u,h]),N=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"sm",a="sm"===s?"w-4 h-4":"w-5 h-5";return Array.from({length:5},(s,r)=>(0,t.jsx)(y.A,{className:"".concat(a," ").concat(r<e?"fill-yellow-400 text-yellow-400":"text-gray-300")},r))},b=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return l?(0,t.jsxs)(m.Zp,{children:[(0,t.jsx)(m.aR,{children:(0,t.jsx)(m.ZB,{children:"Customer Reviews"})}),(0,t.jsx)(m.Wu,{className:"space-y-4",children:Array.from({length:3}).map((e,s)=>(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-32"}),(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-full"}),(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-3/4"})]},s))})]}):(0,t.jsxs)(m.Zp,{children:[(0,t.jsx)(m.aR,{children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between gap-4",children:[(0,t.jsx)(m.ZB,{className:"text-2xl",children:"Customer Reviews"}),g&&(0,t.jsx)(o.$,{onClick:()=>d(!n),variant:n?"outline":"default",children:n?"Cancel":"Write a Review"})]})}),(0,t.jsxs)(m.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"text-center space-y-2",children:[(0,t.jsx)("div",{className:"text-4xl font-bold text-primary",children:f.average.toFixed(1)}),(0,t.jsx)("div",{className:"flex justify-center",children:N(Math.round(f.average),"md")}),(0,t.jsxs)("p",{className:"text-muted-foreground",children:["Based on ",f.total," review(s)"]})]}),(0,t.jsx)("div",{className:"space-y-2",children:[5,4,3,2,1].map(e=>(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"w-6 text-sm",children:e}),(0,t.jsx)(y.A,{className:"w-4 h-4 fill-yellow-400 text-yellow-400"}),(0,t.jsx)("div",{className:"flex-1 h-2 bg-muted rounded-full",children:(0,t.jsx)("div",{className:"h-2 bg-yellow-400 rounded-full",style:{width:"".concat(f.percentages[e],"%")}})}),(0,t.jsx)("span",{className:"w-6 text-sm text-muted-foreground",children:f.stats[e]})]},e))})]}),n&&g&&(0,t.jsx)(c.P.div,{initial:{opacity:0},animate:{opacity:1},children:(0,t.jsx)(J,{productId:s,onReviewSubmitted:i,onCancel:()=>d(!1)})}),a.length>0&&(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4",children:[(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsxs)(M.l6,{value:h,onValueChange:e=>p(e),children:[(0,t.jsx)(M.bq,{className:"w-32",children:(0,t.jsx)(M.yv,{placeholder:"Filter"})}),(0,t.jsxs)(M.gC,{children:[(0,t.jsx)(M.eb,{value:"all",children:"All Stars"}),(0,t.jsx)(M.eb,{value:"5",children:"5 Stars"}),(0,t.jsx)(M.eb,{value:"4",children:"4 Stars"}),(0,t.jsx)(M.eb,{value:"3",children:"3 Stars"}),(0,t.jsx)(M.eb,{value:"2",children:"2 Stars"}),(0,t.jsx)(M.eb,{value:"1",children:"1 Star"})]})]}),(0,t.jsxs)(M.l6,{value:u,onValueChange:e=>x(e),children:[(0,t.jsx)(M.bq,{className:"w-36",children:(0,t.jsx)(M.yv,{placeholder:"Sort"})}),(0,t.jsxs)(M.gC,{children:[(0,t.jsx)(M.eb,{value:"newest",children:"Newest"}),(0,t.jsx)(M.eb,{value:"oldest",children:"Oldest"}),(0,t.jsx)(M.eb,{value:"highest",children:"Highest Rated"}),(0,t.jsx)(M.eb,{value:"lowest",children:"Lowest Rated"}),(0,t.jsx)(M.eb,{value:"helpful",children:"Most Helpful"})]})]})]}),(0,t.jsxs)(v.E,{variant:"secondary",children:[j.length," review",1!==j.length?"s":""]})]}),(0,t.jsx)("div",{className:"space-y-6",children:0===j.length?(0,t.jsx)("p",{className:"text-center text-muted-foreground py-8",children:"all"===h?"No reviews yet. Be the first to write one!":"No ".concat(h,"-star reviews found.")}):j.map((e,s)=>(0,t.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.05*s},className:"border-b pb-6 last:border-0",children:(0,t.jsxs)("div",{className:"flex items-start gap-4",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-muted rounded-full flex items-center justify-center",children:(0,t.jsx)(H.A,{className:"w-5 h-5 text-muted-foreground"})}),(0,t.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,t.jsx)("div",{className:"flex justify-between",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:e.fullName}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[N(e.rating),(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:b(e.createdAt)})]})]})}),(0,t.jsx)("p",{className:"text-muted-foreground",children:e.comment}),(0,t.jsxs)("div",{className:"flex gap-4 pt-2",children:[(0,t.jsxs)(o.$,{variant:"ghost",size:"sm",children:[(0,t.jsx)(T.A,{className:"w-4 h-4 mr-1"})," Helpful"]}),(0,t.jsxs)(o.$,{variant:"ghost",size:"sm",children:[(0,t.jsx)(O.A,{className:"w-4 h-4 mr-1"})," Not Helpful"]})]})]})]})},e._id))})]})]})};var Q=a(9434);let X=r.forwardRef((e,s)=>{let{...a}=e;return(0,t.jsx)("nav",{ref:s,"aria-label":"breadcrumb",...a})});X.displayName="Breadcrumb";let Y=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("ol",{ref:s,className:(0,Q.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",a),...r})});Y.displayName="BreadcrumbList";let K=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("li",{ref:s,className:(0,Q.cn)("inline-flex items-center gap-1.5",a),...r})});K.displayName="BreadcrumbItem";let ee=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("a",{ref:s,className:(0,Q.cn)("transition-colors hover:text-foreground",a),...r})});ee.displayName="BreadcrumbLink";let es=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("span",{ref:s,role:"link","aria-disabled":"true","aria-current":"page",className:(0,Q.cn)("font-normal text-foreground",a),...r})});es.displayName="BreadcrumbPage";let ea=e=>{let{children:s,className:a,...r}=e;return(0,t.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,Q.cn)("[&>svg]:size-3.5",a),...r,children:null!=s?s:(0,t.jsx)(g.A,{})})};ea.displayName="BreadcrumbSeparator";let et=(0,r.memo)(e=>{let{product:s}=e,[a,n]=(0,r.useState)([]),[d,c]=(0,r.useState)([]),[o,m]=(0,r.useState)(!0),[u,x]=(0,r.useState)(!0);(0,r.useEffect)(()=>{s&&(async()=>{try{m(!0);let e=await l.j.getReviews(s._id);e.success&&e.data&&n(e.data.reviews)}catch(e){console.error("Error fetching reviews:",e),i.oR.error("Failed to load reviews")}finally{m(!1)}})()},[s]),(0,r.useEffect)(()=>{s&&(async()=>{try{x(!0);let e=await l.j.getProductsByCategory({category:s.category,limit:8});if(e.success&&e.data){let a=e.data.products.filter(e=>e._id!==s._id).slice(0,4);c(a)}}catch(e){console.error("Error fetching related products:",e)}finally{x(!1)}})()},[s]);let h=(0,r.useCallback)(e=>{n(s=>[e,...s]),i.oR.success("Review submitted successfully!")},[]);return s?(0,t.jsx)("div",{className:"min-h-screen bg-background",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsx)(X,{className:"mb-6 sm:mb-8",children:(0,t.jsxs)(Y,{className:"flex flex-wrap gap-2 text-sm",children:[(0,t.jsx)(K,{children:(0,t.jsx)(ee,{href:"/",children:"Home"})}),(0,t.jsx)(ea,{}),(0,t.jsx)(K,{children:(0,t.jsx)(ee,{href:"/shop",children:"Shop"})}),(0,t.jsx)(ea,{}),(0,t.jsx)(K,{children:(0,t.jsx)(ee,{href:"/shop?category=".concat(encodeURIComponent(s.category)),children:s.category})}),(0,t.jsx)(ea,{}),(0,t.jsx)(K,{children:(0,t.jsx)(es,{children:s.title})})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 xl:gap-12 mb-12 md:mb-16",children:[(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsx)(f,{images:s.images,productTitle:s.title})}),(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsx)(_,{product:s,reviews:a})})]}),(0,t.jsx)("div",{className:"mb-12 md:mb-16",children:(0,t.jsx)(W,{product:s,reviews:a,isLoadingReviews:o,onReviewSubmitted:h})}),(0,t.jsx)("div",{id:"reviews-section",className:"mb-12 md:mb-16",children:(0,t.jsx)(G,{productId:s._id,reviews:a,isLoading:o,onReviewSubmitted:h})}),(0,t.jsx)("div",{className:"mb-8 md:mb-16",children:(0,t.jsx)(q,{products:d,isLoading:u,currentProductId:s._id})})]})}):null});var er=a(7288),el=a(2357),ei=a(5695);function en(){let{id:e}=(0,ei.useParams)(),[s,a]=(0,r.useState)(null),[n,d]=(0,r.useState)(!0);return((0,r.useEffect)(()=>{(async()=>{try{d(!0);let s=await l.j.getProduct(e);s.success&&s.data&&a(s.data)}catch(e){console.error("Error fetching product:",e),i.oR.error("Failed to load product")}finally{d(!1)}})()},[e]),!s||n)?(0,t.jsx)(er.default,{}):(0,t.jsx)(el.default,{children:(0,t.jsx)(r.Suspense,{fallback:(0,t.jsx)(er.default,{}),children:(0,t.jsx)(et,{product:s})})})}},9409:(e,s,a)=>{"use strict";a.d(s,{bq:()=>u,eb:()=>g,gC:()=>p,l6:()=>o,yv:()=>m});var t=a(5155),r=a(2115),l=a(2918),i=a(6474),n=a(7863),d=a(5196),c=a(9434);let o=l.bL;l.YJ;let m=l.WT,u=r.forwardRef((e,s)=>{let{className:a,children:r,...n}=e;return(0,t.jsxs)(l.l9,{ref:s,className:(0,c.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...n,children:[r,(0,t.jsx)(l.In,{asChild:!0,children:(0,t.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=l.l9.displayName;let x=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.PP,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...r,children:(0,t.jsx)(n.A,{className:"h-4 w-4"})})});x.displayName=l.PP.displayName;let h=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.wn,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...r,children:(0,t.jsx)(i.A,{className:"h-4 w-4"})})});h.displayName=l.wn.displayName;let p=r.forwardRef((e,s)=>{let{className:a,children:r,position:i="popper",...n}=e;return(0,t.jsx)(l.ZL,{children:(0,t.jsxs)(l.UC,{ref:s,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:i,...n,children:[(0,t.jsx)(x,{}),(0,t.jsx)(l.LM,{className:(0,c.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,t.jsx)(h,{})]})})});p.displayName=l.UC.displayName,r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.JU,{ref:s,className:(0,c.cn)("px-2 py-1.5 text-sm font-semibold",a),...r})}).displayName=l.JU.displayName;let g=r.forwardRef((e,s)=>{let{className:a,children:r,...i}=e;return(0,t.jsxs)(l.q7,{ref:s,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...i,children:[(0,t.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(l.VF,{children:(0,t.jsx)(d.A,{className:"h-4 w-4"})})}),(0,t.jsx)(l.p4,{children:r})]})});g.displayName=l.q7.displayName,r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.wv,{ref:s,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",a),...r})}).displayName=l.wv.displayName}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,9078,8543,6874,6766,3888,6408,7172,1186,7389,8441,1684,7358],()=>s(5760)),_N_E=e.O()}]);