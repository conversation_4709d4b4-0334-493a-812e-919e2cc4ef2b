(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4895],{521:(e,s,a)=>{"use strict";a.d(s,{default:()=>i});var t=a(5155);a(2115);var r=a(6695);let i=()=>(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:Array.from({length:12}).map((e,s)=>(0,t.jsxs)(r.Zp,{className:"w-full h-full shadow-md border-0 bg-card overflow-hidden",children:[(0,t.jsx)("div",{className:"relative aspect-square bg-muted animate-pulse"}),(0,t.jsxs)(r.<PERSON>,{className:"p-4 space-y-3",children:[(0,t.jsx)("div",{className:"h-3 bg-muted rounded animate-pulse w-1/3"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-full"}),(0,t.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-3/4"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"flex gap-1",children:Array.from({length:5}).map((e,s)=>(0,t.jsx)("div",{className:"w-4 h-4 bg-muted rounded animate-pulse"},s))}),(0,t.jsx)("div",{className:"h-3 bg-muted rounded animate-pulse w-12"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"h-5 bg-muted rounded animate-pulse w-16"}),(0,t.jsx)("div",{className:"h-3 bg-muted rounded animate-pulse w-12"})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"h-3 bg-muted rounded animate-pulse w-full"}),(0,t.jsx)("div",{className:"h-3 bg-muted rounded animate-pulse w-2/3"})]})]})]},s))})},2523:(e,s,a)=>{"use strict";a.d(s,{p:()=>i});var t=a(5155);a(2115);var r=a(9434);function i(e){let{className:s,type:a,...i}=e;return(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...i})}},4838:(e,s,a)=>{"use strict";a.d(s,{SQ:()=>c,_2:()=>o,mB:()=>d,rI:()=>l,ty:()=>n});var t=a(5155);a(2115);var r=a(9449),i=a(9434);function l(e){let{...s}=e;return(0,t.jsx)(r.bL,{"data-slot":"dropdown-menu",...s})}function n(e){let{...s}=e;return(0,t.jsx)(r.l9,{"data-slot":"dropdown-menu-trigger",...s})}function c(e){let{className:s,sideOffset:a=4,...l}=e;return(0,t.jsx)(r.ZL,{children:(0,t.jsx)(r.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",s),...l})})}function o(e){let{className:s,inset:a,variant:l="default",...n}=e;return(0,t.jsx)(r.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":l,className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...n})}function d(e){let{className:s,...a}=e;return(0,t.jsx)(r.wv,{"data-slot":"dropdown-menu-separator",className:(0,i.cn)("bg-border -mx-1 my-1 h-px",s),...a})}},5057:(e,s,a)=>{"use strict";a.d(s,{J:()=>l});var t=a(5155);a(2115);var r=a(968),i=a(9434);function l(e){let{className:s,...a}=e;return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...a})}},5326:(e,s,a)=>{"use strict";a.d(s,{default:()=>_});var t=a(5155),r=a(2115),i=a(37),l=a(8543),n=a(4236),c=a(285),o=a(2355),d=a(5623),m=a(3052);let u=e=>{let{currentPage:s,totalPages:a,onPageChange:r}=e;if(a<=1)return null;let i=(()=>{let e=[],t=[];for(let t=Math.max(2,s-2);t<=Math.min(a-1,s+2);t++)e.push(t);return s-2>2?t.push(1,"..."):t.push(1),t.push(...e),s+2<a-1?t.push("...",a):a>1&&t.push(a),t})();return(0,t.jsxs)("nav",{className:"flex items-center justify-center space-x-1","aria-label":"Pagination",children:[(0,t.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>r(s-1),disabled:1===s,className:"flex items-center gap-1",children:[(0,t.jsx)(o.A,{className:"w-4 h-4"}),"Previous"]}),(0,t.jsx)("div",{className:"flex items-center space-x-1",children:i.map((e,a)=>{if("..."===e)return(0,t.jsx)("div",{className:"flex items-center justify-center w-8 h-8",children:(0,t.jsx)(d.A,{className:"w-4 h-4 text-muted-foreground"})},"dots-".concat(a));let i=e===s;return(0,t.jsx)(c.$,{variant:i?"default":"outline",size:"sm",onClick:()=>r(e),className:"w-8 h-8 p-0 ".concat(i?"bg-primary text-primary-foreground":"hover:bg-muted"),"aria-current":i?"page":void 0,children:e},e)})}),(0,t.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>r(s+1),disabled:s===a,className:"flex items-center gap-1",children:["Next",(0,t.jsx)(m.A,{className:"w-4 h-4"})]})]})};var x=a(6408);let h=(0,r.memo)(e=>{let{products:s,currentPage:a,totalPages:r,onPageChange:i,isLoading:l=!1}=e;return 0!==s.length||l?(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)(x.P.div,{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",initial:{opacity:0},animate:{opacity:1},transition:{duration:.3},children:s.map((e,s)=>(0,t.jsx)(x.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.05*s},children:(0,t.jsx)(n.K,{product:e})},e._id))}),r>1&&(0,t.jsx)("div",{className:"flex justify-center mt-12",children:(0,t.jsx)(u,{currentPage:a,totalPages:r,onPageChange:i})})]}):(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center py-16 text-center",children:[(0,t.jsx)("div",{className:"w-24 h-24 mb-6 text-muted-foreground",children:(0,t.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",className:"w-full h-full",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H6a1 1 0 00-1 1v1m16 0H4"})})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-2",children:"No products found"}),(0,t.jsx)("p",{className:"text-muted-foreground max-w-md",children:"We couldn't find any products matching your criteria. Try adjusting your filters or search terms."})]})});var p=a(6695),g=a(2523),f=a(5057),v=a(6126),j=a(4073),y=a(9434);let N=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsxs)(j.bL,{ref:s,className:(0,y.cn)("relative flex w-full touch-none select-none items-center",a),...r,children:[(0,t.jsx)(j.CC,{className:"relative h-1.5 w-full grow overflow-hidden rounded-full bg-primary/20",children:(0,t.jsx)(j.Q6,{className:"absolute h-full bg-primary"})}),(0,t.jsx)(j.zi,{className:"block h-4 w-4 rounded-full border border-primary/50 bg-background shadow transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"}),(0,t.jsx)(j.zi,{className:"block h-4 w-4 rounded-full border border-primary/50 bg-background shadow transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"})]})});N.displayName=j.bL.displayName;var b=a(6932),w=a(7863),P=a(6474),C=a(4416),k=a(760);let A=e=>{let{filters:s,categories:a,priceRange:i,onFilterChange:l,onClearFilters:n}=e,[o,d]=(0,r.useState)({categories:!0,price:!0,rating:!1}),[m,u]=(0,r.useState)([s.minPrice,s.maxPrice]),h=e=>{d(s=>({...s,[e]:!s[e]}))},j=e=>{u(e),l({minPrice:e[0],maxPrice:e[1]})},y=e=>{l({category:s.category===e?"":e})},A=()=>{let e=0;return s.category&&e++,(s.minPrice>i.min||s.maxPrice<i.max)&&e++,s.searchQuery&&e++,e},S=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0}).format(e);return(0,t.jsxs)(p.Zp,{className:"sticky top-4",children:[(0,t.jsx)(p.aR,{className:"pb-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(p.ZB,{className:"text-lg font-semibold flex items-center gap-2",children:[(0,t.jsx)(b.A,{className:"w-5 h-5"}),"Filters",A()>0&&(0,t.jsx)(v.E,{variant:"secondary",className:"ml-2",children:A()})]}),A()>0&&(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:n,className:"text-muted-foreground hover:text-foreground",children:"Clear All"})]})}),(0,t.jsxs)(p.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("button",{onClick:()=>h("categories"),className:"flex items-center justify-between w-full text-left",children:[(0,t.jsx)(f.J,{className:"text-sm font-medium",children:"Categories"}),o.categories?(0,t.jsx)(w.A,{className:"w-4 h-4"}):(0,t.jsx)(P.A,{className:"w-4 h-4"})]}),(0,t.jsx)(k.N,{children:o.categories&&(0,t.jsx)(x.P.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.2},className:"overflow-hidden",children:(0,t.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:a.map(e=>(0,t.jsx)("button",{onClick:()=>y(e),className:"w-full text-left px-3 py-2 rounded-md text-sm transition-colors ".concat(s.category===e?"bg-primary text-primary-foreground":"hover:bg-muted"),children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"capitalize",children:e}),s.category===e&&(0,t.jsx)(C.A,{className:"w-3 h-3"})]})},e))})})})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("button",{onClick:()=>h("price"),className:"flex items-center justify-between w-full text-left",children:[(0,t.jsx)(f.J,{className:"text-sm font-medium",children:"Price Range"}),o.price?(0,t.jsx)(w.A,{className:"w-4 h-4"}):(0,t.jsx)(P.A,{className:"w-4 h-4"})]}),(0,t.jsx)(k.N,{children:o.price&&(0,t.jsxs)(x.P.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.2},className:"overflow-hidden space-y-4",children:[(0,t.jsx)("div",{className:"px-2",children:(0,t.jsx)(N,{value:m,onValueChange:j,max:i.max,min:i.min,step:1,className:"w-full"})}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm text-muted-foreground",children:[(0,t.jsx)("span",{children:S(m[0])}),(0,t.jsx)("span",{children:S(m[1])})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(f.J,{htmlFor:"min-price",className:"text-xs",children:"Min"}),(0,t.jsx)(g.p,{id:"min-price",type:"number",value:m[0],onChange:e=>{let s=[parseInt(e.target.value)||0,m[1]];u(s),j(s)},className:"h-8 text-xs"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(f.J,{htmlFor:"max-price",className:"text-xs",children:"Max"}),(0,t.jsx)(g.p,{id:"max-price",type:"number",value:m[1],onChange:e=>{let s=parseInt(e.target.value)||i.max,a=[m[0],s];u(a),j(a)},className:"h-8 text-xs"})]})]})]})})]}),A()>0&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(f.J,{className:"text-sm font-medium",children:"Active Filters"}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[s.category&&(0,t.jsxs)(v.E,{variant:"secondary",className:"flex items-center gap-1 cursor-pointer",onClick:()=>l({category:""}),children:[s.category,(0,t.jsx)(C.A,{className:"w-3 h-3"})]}),(s.minPrice>i.min||s.maxPrice<i.max)&&(0,t.jsxs)(v.E,{variant:"secondary",className:"flex items-center gap-1 cursor-pointer",onClick:()=>l({minPrice:i.min,maxPrice:i.max}),children:[S(s.minPrice)," - ",S(s.maxPrice),(0,t.jsx)(C.A,{className:"w-3 h-3"})]}),s.searchQuery&&(0,t.jsxs)(v.E,{variant:"secondary",className:"flex items-center gap-1 cursor-pointer",onClick:()=>l({searchQuery:""}),children:['"                  “',s.searchQuery,'”"',(0,t.jsx)(C.A,{className:"w-3 h-3"})]})]})]})]})]})};var S=a(4838),Q=a(7924),O=a(257);let B=e=>{let{totalProducts:s,filters:a,onFilterChange:i,onClearFilters:l}=e,[n,o]=(0,r.useState)(a.searchQuery);(0,r.useEffect)(()=>{o(a.searchQuery)},[a.searchQuery]);let d=[{value:"createdAt-desc",label:"Newest First",sortBy:"createdAt",sortOrder:"desc"},{value:"createdAt-asc",label:"Oldest First",sortBy:"createdAt",sortOrder:"asc"},{value:"price-asc",label:"Price: Low to High",sortBy:"price",sortOrder:"asc"},{value:"price-desc",label:"Price: High to Low",sortBy:"price",sortOrder:"desc"},{value:"rating-desc",label:"Highest Rated",sortBy:"rating",sortOrder:"desc"},{value:"rating-asc",label:"Lowest Rated",sortBy:"rating",sortOrder:"asc"}],m=d.find(e=>e.sortBy===a.sortBy&&e.sortOrder===a.sortOrder),u=(e,s)=>{i({sortBy:e,sortOrder:s})},x=()=>{let e=0;return a.category&&e++,a.searchQuery&&e++,(a.minPrice>0||a.maxPrice<1e3)&&e++,e};return(0,t.jsxs)("div",{className:"space-y-6 mb-8",children:[(0,t.jsxs)("div",{className:"text-center space-y-2",children:[(0,t.jsx)("h1",{className:"text-3xl md:text-4xl font-bold text-foreground",children:"Shop Our Products"}),(0,t.jsx)("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Discover our extensive collection of premium products at unbeatable prices"})]}),(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 items-center justify-between",children:[(0,t.jsx)("form",{onSubmit:e=>{e.preventDefault(),i({searchQuery:n})},className:"relative flex-1 max-w-md",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(Q.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4"}),(0,t.jsx)(g.p,{type:"text",placeholder:"Search products...",value:n,onChange:e=>o(e.target.value),className:"pl-10 pr-10"}),n&&(0,t.jsx)("button",{type:"button",onClick:()=>{o(""),i({searchQuery:""})},className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground",children:(0,t.jsx)(C.A,{className:"w-4 h-4"})})]})}),(0,t.jsx)("div",{className:"flex items-center gap-4",children:(0,t.jsxs)(S.rI,{children:[(0,t.jsx)(S.ty,{asChild:!0,children:(0,t.jsxs)(c.$,{variant:"outline",className:"min-w-[180px] justify-between",children:[(0,t.jsxs)("span",{className:"flex items-center gap-2",children:[(0,t.jsx)(O.A,{className:"w-4 h-4"}),(null==m?void 0:m.label)||"Sort by"]}),(0,t.jsx)(P.A,{className:"w-4 h-4"})]})}),(0,t.jsx)(S.SQ,{align:"end",className:"w-[200px]",children:d.map(e=>(0,t.jsx)(S._2,{onClick:()=>u(e.sortBy,e.sortOrder),className:"cursor-pointer ".concat((null==m?void 0:m.value)===e.value?"bg-accent":""),children:e.label},e.value))})]})})]}),(0,t.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Showing ",s," product",1!==s?"s":""]}),x()>0&&(0,t.jsxs)(v.E,{variant:"secondary",children:[x()," filter",1!==x()?"s":""," applied"]})]}),x()>0&&(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:l,className:"text-muted-foreground hover:text-foreground",children:"Clear all filters"})]}),x()>0&&(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[a.category&&(0,t.jsxs)(v.E,{variant:"secondary",className:"flex items-center gap-1 cursor-pointer hover:bg-secondary/80",onClick:()=>i({category:""}),children:["Category: ",a.category,(0,t.jsx)(C.A,{className:"w-3 h-3"})]}),a.searchQuery&&(0,t.jsxs)(v.E,{variant:"secondary",className:"flex items-center gap-1 cursor-pointer hover:bg-secondary/80",onClick:()=>i({searchQuery:""}),children:["Search: “",a.searchQuery,"”",(0,t.jsx)(C.A,{className:"w-3 h-3"})]}),(a.minPrice>0||a.maxPrice<1e3)&&(0,t.jsxs)(v.E,{variant:"secondary",className:"flex items-center gap-1 cursor-pointer hover:bg-secondary/80",onClick:()=>i({minPrice:0,maxPrice:1e3}),children:["Price: $",a.minPrice," - $",a.maxPrice,(0,t.jsx)(C.A,{className:"w-3 h-3"})]})]})]})};var E=a(521),F=a(5339),z=a(3904);let L=e=>{let{message:s,onRetry:a}=e;return(0,t.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center p-4",children:(0,t.jsx)(p.Zp,{className:"w-full max-w-md",children:(0,t.jsxs)(p.Wu,{className:"flex flex-col items-center justify-center py-12 text-center space-y-6",children:[(0,t.jsx)("div",{className:"w-16 h-16 text-destructive",children:(0,t.jsx)(F.A,{className:"w-full h-full"})}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-foreground",children:"Oops! Something went wrong"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:s})]}),(0,t.jsxs)(c.$,{onClick:a,className:"flex items-center gap-2",size:"lg",children:[(0,t.jsx)(z.A,{className:"w-4 h-4"}),"Try Again"]})]})})})},_=()=>{let[e,s]=(0,r.useState)([]),[a,n]=(0,r.useState)([]),[c,o]=(0,r.useState)(!0),[d,m]=(0,r.useState)(null),[u,x]=(0,r.useState)([]),[p,g]=(0,r.useState)({min:0,max:1e3}),[f,v]=(0,r.useState)({category:"",minPrice:0,maxPrice:1e3,sortBy:"createdAt",sortOrder:"desc",searchQuery:""}),[j,y]=(0,r.useState)(1),[N,b]=(0,r.useState)(1),[w,P]=(0,r.useState)(0);(0,r.useEffect)(()=>{C()},[]),(0,r.useEffect)(()=>{k()},[e,f,k]);let C=async()=>{try{o(!0),m(null);let e=await i.j.getAllProducts();if(e.success&&e.data){s(e.data.products);let a=i.j.getCategories(e.data.products),t=i.j.getPriceRange(e.data.products);x(a),g(t),v(e=>({...e,maxPrice:t.max}))}else m("Failed to load products")}catch(e){console.error("Error fetching products:",e),m("Failed to load products. Please try again."),l.oR.error("Failed to load products")}finally{o(!1)}},k=()=>{let s=[...e];if(f.searchQuery){let e=f.searchQuery.toLowerCase();s=s.filter(s=>s.title.toLowerCase().includes(e)||s.description.toLowerCase().includes(e)||s.category.toLowerCase().includes(e))}f.category&&(s=s.filter(e=>e.category===f.category)),s=s.filter(e=>e.price>=f.minPrice&&e.price<=f.maxPrice),n(s=i.j.sortProducts(s,f.sortBy,f.sortOrder)),P(s.length),b(Math.ceil(s.length/12)),y(1)},S=(0,r.useMemo)(()=>{let e=(j-1)*12;return a.slice(e,e+12)},[a,j,12]),Q=(0,r.useCallback)(e=>{v(s=>({...s,...e}))},[]),O=(0,r.useCallback)(e=>{y(e),window.scrollTo({top:0,behavior:"smooth"})},[]),F=(0,r.useCallback)(()=>{v({category:"",minPrice:p.min,maxPrice:p.max,sortBy:"createdAt",sortOrder:"desc",searchQuery:""})},[p.min,p.max]);return d?(0,t.jsx)(L,{message:d,onRetry:C}):(0,t.jsx)("div",{className:"min-h-screen bg-background",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsx)(B,{totalProducts:w,filters:f,onFilterChange:Q,onClearFilters:F}),(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,t.jsx)("aside",{className:"lg:w-64 flex-shrink-0",children:(0,t.jsx)(A,{filters:f,categories:u,priceRange:p,onFilterChange:Q,onClearFilters:F})}),(0,t.jsx)("main",{className:"flex-1",children:c?(0,t.jsx)(E.default,{}):(0,t.jsx)(h,{products:S,currentPage:j,totalPages:N,onPageChange:O,isLoading:c})})]})]})})}},5396:(e,s,a)=>{Promise.resolve().then(a.bind(a,2357)),Promise.resolve().then(a.bind(a,521)),Promise.resolve().then(a.bind(a,5326))}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,9078,8543,6766,3888,6408,9449,533,7389,1678,8441,1684,7358],()=>s(5396)),_N_E=e.O()}]);