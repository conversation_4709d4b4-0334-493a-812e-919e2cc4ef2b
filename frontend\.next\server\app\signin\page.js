(()=>{var e={};e.id=217,e.ids=[217],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12597:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16189:(e,s,t)=>{"use strict";var r=t(65773);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19717:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let d={children:["",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,45029)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\signin\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\signin\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/signin/page",pathname:"/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},20769:(e,s,t)=>{"use strict";t.d(s,{A:()=>o});var r=t(60687);t(43210);var a=t(16189),i=t(63213),n=t(52027);function o({children:e,redirectTo:s="/signin",requireAuth:t=!0}){let{isAuthenticated:o,isLoading:l}=(0,i.A)();return((0,a.useRouter)(),l)?(0,r.jsx)(n.AV,{message:"Checking authentication..."}):t&&!o?(0,r.jsx)(n.AV,{message:"Redirecting to sign in..."}):!t&&o?(0,r.jsx)(n.AV,{message:"Redirecting..."}):(0,r.jsx)(r.Fragment,{children:e})}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39615:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>j});var r=t(60687),a=t(43210),i=t(85814),n=t.n(i),o=t(16189),l=t(28559),d=t(41550),c=t(64021),u=t(12597),p=t(13861),m=t(29523),x=t(89667),h=t(54300),f=t(44493),g=t(58376),v=t(93853),b=t(63213),y=t(20769);let j=function(){let e=(0,o.useRouter)(),{login:s}=(0,b.A)(),[t,i]=(0,a.useState)({email:"",password:""}),[j,w]=(0,a.useState)(!1),[N,A]=(0,a.useState)(!1),[P,k]=(0,a.useState)({}),q=()=>{let e={};return t.email?/\S+@\S+\.\S+/.test(t.email)||(e.email="Please enter a valid email"):e.email="Email is required",t.password?t.password.length<8&&(e.password="Password must be at least 8 characters"):e.password="Password is required",k(e),0===Object.keys(e).length},C=e=>{let{name:s,value:t}=e.target;i(e=>({...e,[s]:t})),P[s]&&k(e=>({...e,[s]:""}))},R=async r=>{if(r.preventDefault(),q()){A(!0);try{let r=await g.Dv.signIn(t),a=r.data?.user,i=r.data?.token,n=r.message;if(n.includes("Verification code sent successfully to your email. Please verify your email")){v.oR.success(n),e.push(`/otpVerification/${a?._id}`);return}s(a,i),v.oR.success("Sign in successfull! Welcome back."),e.push("/")}catch(e){e&&"object"==typeof e&&"response"in e&&e.response&&"object"==typeof e.response&&"data"in e.response&&e.response.data&&"object"==typeof e.response.data&&"message"in e.response.data?v.oR.error(e.response.data.message||"Something went wrong. Try again."):v.oR.error("Something went wrong. Try again.")}finally{A(!1)}}};return(0,r.jsx)(y.A,{requireAuth:!1,children:(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)(n(),{href:"/",className:"inline-flex items-center text-sm text-gray-600 hover:text-gray-900 transition-colors",children:[(0,r.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"Back to Home"]})}),(0,r.jsxs)(f.Zp,{className:"shadow-xl border-0",children:[(0,r.jsxs)(f.aR,{className:"text-center space-y-1",children:[(0,r.jsx)(f.ZB,{className:"text-2xl font-bold text-gray-900",children:"Welcome Back"}),(0,r.jsx)(f.BT,{className:"text-gray-600",children:"Sign in to your Mega Mall account"})]}),(0,r.jsxs)(f.Wu,{className:"space-y-6",children:[(0,r.jsxs)("form",{onSubmit:R,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(h.J,{htmlFor:"email",children:"Email Address"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(d.A,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,r.jsx)(x.p,{id:"email",name:"email",type:"email",placeholder:"Enter your email",value:t.email,onChange:C,className:`pl-10 ${P.email?"border-red-500 focus:ring-red-500":""}`,disabled:N})]}),P.email&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:P.email})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(h.J,{htmlFor:"password",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(c.A,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,r.jsx)(x.p,{id:"password",name:"password",type:j?"text":"password",placeholder:"Enter your password",value:t.password,onChange:C,className:`pl-10 pr-10 ${P.password?"border-red-500 focus:ring-red-500":""}`,disabled:N}),(0,r.jsx)("button",{type:"button",onClick:()=>w(!j),className:"absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600",disabled:N,children:j?(0,r.jsx)(u.A,{className:"w-4 h-4"}):(0,r.jsx)(p.A,{className:"w-4 h-4"})})]}),P.password&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:P.password})]}),(0,r.jsx)("div",{className:"text-right",children:(0,r.jsx)(n(),{href:"/forgot-password",className:"text-sm text-primary hover:underline",children:"Forgot your password?"})}),(0,r.jsx)(m.$,{type:"submit",className:"w-full h-11 text-base",disabled:N,children:N?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Signing In..."]}):"Sign In"})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("span",{className:"w-full border-t border-gray-300"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,r.jsx)("span",{className:"bg-white px-2 text-gray-500",children:"Or"})})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(m.$,{variant:"outline",className:"w-full h-11",disabled:!0,children:[(0,r.jsxs)("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",fill:"currentColor",children:[(0,r.jsx)("path",{d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,r.jsx)("path",{d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,r.jsx)("path",{d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,r.jsx)("path",{d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Continue with Google"]}),(0,r.jsxs)(m.$,{variant:"outline",className:"w-full h-11",disabled:!0,children:[(0,r.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})}),"Continue with Facebook"]})]}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Dont have an account?"," ",(0,r.jsx)(n(),{href:"/signup",className:"text-primary hover:underline",children:"Sign up here"})]})})]})]})]})})})}},44493:(e,s,t)=>{"use strict";t.d(s,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>n,wL:()=>c});var r=t(60687);t(43210);var a=t(4780);function i({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...s})}function n({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...s})}function o({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...s})}function l({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...s})}function d({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...s})}function c({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...s})}},45029:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\app\\\\signin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\signin\\page.tsx","default")},49612:(e,s,t)=>{Promise.resolve().then(t.bind(t,39615))},52027:(e,s,t)=>{"use strict";t.d(s,{AV:()=>i});var r=t(60687);t(43210);let a=({size:e="md",className:s=""})=>(0,r.jsx)("div",{className:`animate-spin rounded-full border-2 border-gray-300 border-t-primary ${{sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"}[e]} ${s}`}),i=({message:e="Loading..."})=>(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(a,{size:"lg",className:"mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 text-lg",children:e})]})})},54300:(e,s,t)=>{"use strict";t.d(s,{J:()=>l});var r=t(60687),a=t(43210),i=t(14163),n=a.forwardRef((e,s)=>(0,r.jsx)(i.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var o=t(4780);function l({className:e,...s}){return(0,r.jsx)(n,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...s})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62764:(e,s,t)=>{Promise.resolve().then(t.bind(t,45029))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64021:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,s,t)=>{"use strict";t.d(s,{p:()=>i});var r=t(60687);t(43210);var a=t(4780);function i({className:e,type:s,...t}){return(0,r.jsx)("input",{type:s,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,162,658,367],()=>t(19717));module.exports=r})();