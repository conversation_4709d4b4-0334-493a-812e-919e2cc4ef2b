(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3839],{646:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1243:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},1264:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},1586:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},1788:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},2346:(e,s,t)=>{"use strict";t.d(s,{w:()=>n});var a=t(5155),r=t(2115),l=t(7489),i=t(9434);let n=r.forwardRef((e,s)=>{let{className:t,orientation:r="horizontal",decorative:n=!0,...c}=e;return(0,a.jsx)(l.b,{ref:s,decorative:n,orientation:r,className:(0,i.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",t),...c})});n.displayName=l.b.displayName},3150:(e,s,t)=>{Promise.resolve().then(t.bind(t,6842)),Promise.resolve().then(t.bind(t,7558)),Promise.resolve().then(t.bind(t,2357))},3655:(e,s,t)=>{"use strict";t.d(s,{hO:()=>c,sG:()=>n});var a=t(2115),r=t(7650),l=t(9708),i=t(5155),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,s)=>{let t=(0,l.TL)(`Primitive.${s}`),r=a.forwardRef((e,a)=>{let{asChild:r,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(r?t:s,{...l,ref:a})});return r.displayName=`Primitive.${s}`,{...e,[s]:r}},{});function c(e,s){e&&r.flushSync(()=>e.dispatchEvent(s))}},3904:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},4516:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},5339:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5695:(e,s,t)=>{"use strict";var a=t(8999);t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"usePathname")&&t.d(s,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}})},6101:(e,s,t)=>{"use strict";t.d(s,{s:()=>i,t:()=>l});var a=t(2115);function r(e,s){if("function"==typeof e)return e(s);null!=e&&(e.current=s)}function l(...e){return s=>{let t=!1,a=e.map(e=>{let a=r(e,s);return t||"function"!=typeof a||(t=!0),a});if(t)return()=>{for(let s=0;s<a.length;s++){let t=a[s];"function"==typeof t?t():r(e[s],null)}}}}function i(...e){return a.useCallback(l(...e),e)}},7108:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},7340:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},7489:(e,s,t)=>{"use strict";t.d(s,{b:()=>d});var a=t(2115),r=t(3655),l=t(5155),i="horizontal",n=["horizontal","vertical"],c=a.forwardRef((e,s)=>{var t;let{decorative:a,orientation:c=i,...d}=e,x=(t=c,n.includes(t))?c:i;return(0,l.jsx)(r.sG.div,{"data-orientation":x,...a?{role:"none"}:{"aria-orientation":"vertical"===x?x:void 0,role:"separator"},...d,ref:s})});c.displayName="Separator";var d=c},7550:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7558:(e,s,t)=>{"use strict";t.d(s,{default:()=>A});var a=t(5155),r=t(2115),l=t(5695),i=t(6874),n=t.n(i),c=t(6695),d=t(285),x=t(6126),m=t(2346),h=t(5365),o=t(5654),u=t(5339),p=t(7550),y=t(646),j=t(1788);let f=(0,t(9946).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);var v=t(7108),g=t(9799),N=t(4516),b=t(9420),w=t(1264),k=t(1586);let A=e=>{let{orderId:s}=e,t=(0,l.useRouter)(),[i,A]=(0,r.useState)([]),[C,M]=(0,r.useState)(!0),[R,S]=(0,r.useState)(null);(0,r.useEffect)(()=>{let e=async()=>{try{M(!0);let e=await o.Qo.getOrders();if(e.success&&e.data){let t=e.data.find(e=>e._id===s);t?A([t]):S("Order not found")}else S("Failed to fetch order details")}catch(e){S("Failed to load order details")}finally{M(!1)}};s&&e()},[s]);let O=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});return C?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3 mb-4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-8"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"h-32 bg-gray-200 rounded"}),(0,a.jsx)("div",{className:"h-48 bg-gray-200 rounded"}),(0,a.jsx)("div",{className:"h-32 bg-gray-200 rounded"})]})]})})}):R||!i.length?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)(h.Fc,{variant:"destructive",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsx)(h.TN,{children:R||"Order not found"})]}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsxs)(d.$,{onClick:()=>t.push("/orders"),variant:"outline",children:[(0,a.jsx)(p.A,{className:"w-4 h-4 mr-2"}),"Back to Orders"]})})]})}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)(y.A,{className:"w-8 h-8 text-green-500 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Order Confirmed!"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Thank you for your order. We'll send you updates via email."})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Order Number"}),(0,a.jsx)("p",{className:"text-lg font-semibold",children:i[0]._id})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)(d.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(j.A,{className:"w-4 h-4 mr-2"}),"Download Receipt"]}),(0,a.jsxs)(d.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(f,{className:"w-4 h-4 mr-2"}),"Share"]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsxs)(c.ZB,{className:"flex items-center",children:[(0,a.jsx)(v.A,{className:"w-5 h-5 mr-2"}),"Order Status"]})}),(0,a.jsxs)(c.Wu,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)(x.E,{className:(e=>{switch(e){case"placed":return"bg-blue-100 text-blue-800";case"processing":return"bg-yellow-100 text-yellow-800";case"shipped":return"bg-purple-100 text-purple-800";case"delivered":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(i[0].status),children:i[0].status}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:O(i[0].createdAt)})]}),(0,a.jsx)("div",{className:"space-y-3",children:i.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full mr-3 ".concat(e.status===i[0].status?"bg-blue-500":"bg-green-500")}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"font-medium",children:["Order Status: ",e.status]}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:O(e.createdAt)})]})})]},s))}),i[0].estimatedDelivery&&(0,a.jsx)("div",{className:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(g.A,{className:"w-4 h-4 text-blue-600 mr-2"}),(0,a.jsxs)("span",{className:"text-sm text-blue-800",children:["Estimated delivery: ",O(i[0].estimatedDelivery)]})]})})]})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{children:"Order Items"})}),(0,a.jsx)(c.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:i[0].orderItems.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center space-x-4 pb-4 border-b last:border-b-0",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gray-200 rounded-md flex items-center justify-center",children:(0,a.jsx)(v.A,{className:"w-6 h-6 text-gray-400"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"font-medium",children:e.product.title}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Quantity: ",e.quantity]}),(0,a.jsxs)("p",{className:"text-sm font-medium",children:["$",e.product.price," each"]})]}),(0,a.jsx)("div",{className:"text-right",children:(0,a.jsxs)("p",{className:"font-semibold",children:["$",e.totalPrice]})})]},s))})})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsxs)(c.ZB,{className:"flex items-center",children:[(0,a.jsx)(N.A,{className:"w-5 h-5 mr-2"}),"Shipping Address"]})}),(0,a.jsx)(c.Wu,{children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"font-medium",children:i[0].shippingDetails[0].fullName}),(0,a.jsx)("p",{className:"text-gray-600",children:i[0].shippingDetails[0].address}),(0,a.jsxs)("p",{className:"text-gray-600",children:[i[0].shippingDetails[0].city,", ",i[0].shippingDetails[0].state," ",i[0].shippingDetails[0].postalCode]}),(0,a.jsx)("p",{className:"text-gray-600",children:i[0].shippingDetails[0].country}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mt-3 pt-3 border-t",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(b.A,{className:"w-4 h-4 mr-2 text-gray-400"}),(0,a.jsx)("span",{className:"text-sm",children:i[0].shippingDetails[0].phone})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(w.A,{className:"w-4 h-4 mr-2 text-gray-400"}),(0,a.jsx)("span",{className:"text-sm",children:i[0].user.email})]})]})]})})]})]}),(0,a.jsxs)("div",{className:"lg:col-span-1",children:[(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{children:"Order Summary"})}),(0,a.jsxs)(c.Wu,{children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Subtotal"}),(0,a.jsxs)("span",{children:["$",i[0].totalRevenue.toFixed(2)]})]}),i[0].codCharges>0&&(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"COD Fee"}),(0,a.jsxs)("span",{children:["$",i[0].codCharges.toFixed(2)]})]}),(0,a.jsx)(m.w,{}),(0,a.jsxs)("div",{className:"flex justify-between font-semibold",children:[(0,a.jsx)("span",{children:"Total"}),(0,a.jsxs)("span",{children:["$",i[0].totalRevenue.toFixed(2)]})]})]}),(0,a.jsx)("div",{className:"mt-4 p-3 bg-gray-50 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(k.A,{className:"w-4 h-4 mr-2 text-gray-600"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:i[0].paymentMethod.toUpperCase()})]})})]})]}),(0,a.jsxs)("div",{className:"mt-6 space-y-3",children:[(0,a.jsx)(d.$,{asChild:!0,className:"w-full",children:(0,a.jsx)(n(),{href:"/orders",children:"View All Orders"})}),(0,a.jsx)(d.$,{asChild:!0,variant:"outline",className:"w-full",children:(0,a.jsx)(n(),{href:"/shop",children:"Continue Shopping"})})]}),(0,a.jsx)(c.Zp,{className:"mt-6",children:(0,a.jsxs)(c.Wu,{className:"pt-6",children:[(0,a.jsx)("h4",{className:"font-medium mb-2",children:"Need Help?"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"Contact our customer support team for any questions about your order."}),(0,a.jsx)(d.$,{variant:"outline",size:"sm",className:"w-full",children:"Contact Support"})]})})]})]})]})})}},9420:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},9708:(e,s,t)=>{"use strict";t.d(s,{DX:()=>n,TL:()=>i});var a=t(2115),r=t(6101),l=t(5155);function i(e){let s=function(e){let s=a.forwardRef((e,s)=>{let{children:t,...l}=e;if(a.isValidElement(t)){var i;let e,n,c=(i=t,(n=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(n=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,s){let t={...s};for(let a in s){let r=e[a],l=s[a];/^on[A-Z]/.test(a)?r&&l?t[a]=(...e)=>{let s=l(...e);return r(...e),s}:r&&(t[a]=r):"style"===a?t[a]={...r,...l}:"className"===a&&(t[a]=[r,l].filter(Boolean).join(" "))}return{...e,...t}}(l,t.props);return t.type!==a.Fragment&&(d.ref=s?(0,r.t)(s,c):c),a.cloneElement(t,d)}return a.Children.count(t)>1?a.Children.only(null):null});return s.displayName=`${e}.SlotClone`,s}(e),t=a.forwardRef((e,t)=>{let{children:r,...i}=e,n=a.Children.toArray(r),c=n.find(d);if(c){let e=c.props.children,r=n.map(s=>s!==c?s:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,l.jsx)(s,{...i,ref:t,children:a.isValidElement(e)?a.cloneElement(e,void 0,r):null})}return(0,l.jsx)(s,{...i,ref:t,children:r})});return t.displayName=`${e}.Slot`,t}var n=i("Slot"),c=Symbol("radix.slottable");function d(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===c}},9799:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,9078,6874,7389,7455,8441,1684,7358],()=>s(3150)),_N_E=e.O()}]);