(()=>{var e={};e.id=543,e.ids=[543],e.modules={1270:(e,r,s)=>{Promise.resolve().then(s.bind(s,7956))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7956:(e,r,s)=>{"use strict";s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminProtectedRoute.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminProtectedRoute.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16189:(e,r,s)=>{"use strict";var t=s(65773);s.o(t,"useParams")&&s.d(r,{useParams:function(){return t.useParams}}),s.o(t,"usePathname")&&s.d(r,{usePathname:function(){return t.usePathname}}),s.o(t,"useRouter")&&s.d(r,{useRouter:function(){return t.useRouter}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},42997:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>l,routeModule:()=>p,tree:()=>u});var t=s(65239),i=s(48088),n=s(88170),o=s.n(n),a=s(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);s.d(r,d);let u={children:["",{children:["admin",{children:["messages",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,98883)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\messages\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\messages\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/messages/page",pathname:"/admin/messages",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},48126:(e,r,s)=>{Promise.resolve().then(s.bind(s,73482))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73482:(e,r,s)=>{"use strict";s.d(r,{default:()=>u});var t=s(60687);s(43210);var i=s(58873),n=s(91821),o=s(93613),a=s(99891);let d=(e,r)=>"admin"===r?"admin"===e.role||"superAdmin"===e.role:"superAdmin"===r&&"superAdmin"===e.role,u=({children:e,requiredRole:r,fallback:s})=>{let{admin:u,isAuthenticated:l,isLoading:c}=(0,i.b)();return c?s||(0,t.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})}):l&&u?r&&!d(u,r)?s||(0,t.jsxs)(n.Fc,{variant:"destructive",children:[(0,t.jsx)(a.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{children:"Insufficient Permissions"}),(0,t.jsxs)("p",{children:["You need ",r," privileges to access this page. Your current role is: ",u.role]})]})]}):(0,t.jsx)(t.Fragment,{children:e}):s||(0,t.jsxs)(n.Fc,{variant:"destructive",children:[(0,t.jsx)(o.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{children:"Access Denied"}),(0,t.jsx)("p",{children:"You must be logged in as an admin to access this page."})]})]})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},98883:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>n});var t=s(37413),i=s(7956);function n(){return(0,t.jsx)(i.default,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Customer Messages"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Manage customer support messages and communications."})]}),(0,t.jsxs)("div",{className:"bg-card p-8 rounded-lg border border-border text-center",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Message Center Coming Soon"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Customer support messaging system will be implemented in the next phase."})]})]})})}}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,162,658,367,10],()=>s(42997));module.exports=t})();