"use strict";exports.id=589,exports.ids=[589],exports.modules={9732:(e,t,a)=>{a.d(t,{K:()=>p});var s=a(60687),r=a(43210),o=a(44493),i=a(29523),l=a(96834),n=a(64398),d=a(13861),c=a(67760),h=a(28561),u=a(26001),A=a(30474),x=a(28253),m=a(93853),g=a(16189);let p=({product:e,badge:t})=>{let a,[p,f]=(0,r.useState)(1),[v,w]=(0,r.useState)(!1),[b,y]=(0,r.useState)(!0),{addToCart:j,isUpdating:N}=(0,x._)(),k=(0,g.useRouter)(),C=async()=>{await j(e._id,p)&&f(1)},E=0===e.stock,B=e.stock>0&&e.stock<=5;return(0,s.jsx)(u.P.div,{className:"group relative",onHoverStart:()=>w(!0),onHoverEnd:()=>w(!1),whileHover:{y:-4},transition:{type:"spring",stiffness:300,damping:30},children:(0,s.jsxs)(o.Zp,{className:"w-full h-full shadow-md hover:shadow-xl transition-all duration-300 border-0 bg-card overflow-hidden",children:[(0,s.jsxs)("div",{className:"relative aspect-square bg-muted overflow-hidden",children:[e.images&&e.images.length>0&&(0,s.jsx)(A.default,{src:e.images[0].url,alt:e.title,fill:!0,className:`object-cover transition-all duration-300 ${v?"scale-110":"scale-100"} ${b?"blur-sm":"blur-0"}`,onLoad:()=>y(!1),onError:()=>y(!1),sizes:"(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw",priority:!1,quality:85,placeholder:"blur",blurDataURL:"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="}),b&&(0,s.jsx)("div",{className:"absolute inset-0 bg-muted animate-pulse"}),(0,s.jsxs)("div",{className:"absolute top-3 left-3 flex flex-col gap-2",children:[t&&(0,s.jsx)(l.E,{className:`text-xs font-medium ${"Sale"===t?"bg-red-500 hover:bg-red-600":"New Arrival"===t?"bg-green-500 hover:bg-green-600":"bg-blue-500 hover:bg-blue-600"}`,children:t}),E&&(0,s.jsx)(l.E,{variant:"destructive",className:"text-xs",children:"Out of Stock"}),B&&!E&&(0,s.jsx)(l.E,{className:"bg-orange-500 hover:bg-orange-600 text-xs",children:"Low Stock"})]}),(0,s.jsxs)(u.P.div,{className:"absolute top-3 right-3 flex flex-col gap-2",initial:{opacity:0,x:20},animate:{opacity:+!!v,x:20*!v},transition:{duration:.2},children:[(0,s.jsx)(i.$,{size:"icon",variant:"secondary",className:"w-8 h-8 rounded-full bg-white/90 hover:bg-white shadow-md",onClick:()=>{setShowQuickView(!0),m.oR.info("Quick view feature coming soon!")},children:(0,s.jsx)(d.A,{className:"w-4 h-4"})}),(0,s.jsx)(i.$,{size:"icon",variant:"secondary",className:"w-8 h-8 rounded-full bg-white/90 hover:bg-white shadow-md",children:(0,s.jsx)(c.A,{className:"w-4 h-4"})})]}),(0,s.jsxs)(u.P.div,{className:"absolute bottom-3 left-3 right-3",initial:{opacity:0,y:20},animate:{opacity:+!!v,y:20*!v},transition:{duration:.2,delay:.1},children:[(0,s.jsxs)(i.$,{onClick:C,disabled:N||E,className:"w-full bg-primary/90 hover:bg-primary text-primary-foreground shadow-lg",size:"sm",children:[(0,s.jsx)(h.A,{className:"w-4 h-4 mr-2"}),N?"Adding...":E?"Out of Stock":"Quick Add"]}),(0,s.jsx)(i.$,{className:"w-full bg-accent/90 hover:bg-accent text-accent-foreground shadow-lg",size:"sm",onClick:()=>k.push(`/product/${e._id}`),children:"View Product"})]})]}),(0,s.jsxs)(o.Wu,{className:"p-4 space-y-3",children:[(0,s.jsx)("div",{className:"text-xs text-muted-foreground uppercase tracking-wide",children:e.category}),(0,s.jsx)("h3",{className:"font-semibold text-sm leading-tight line-clamp-2 min-h-[2.5rem]",children:e.title}),e.averageRating&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"flex items-center",children:((e=0)=>{let t=[],a=Math.floor(e),r=e%1!=0;for(let e=0;e<5;e++)e<a?t.push((0,s.jsx)(n.A,{className:"w-4 h-4 fill-yellow-400 text-yellow-400"},e)):e===a&&r?t.push((0,s.jsxs)("div",{className:"relative w-4 h-4",children:[(0,s.jsx)(n.A,{className:"w-4 h-4 text-gray-300 absolute"}),(0,s.jsx)("div",{className:"overflow-hidden w-1/2",children:(0,s.jsx)(n.A,{className:"w-4 h-4 fill-yellow-400 text-yellow-400"})})]},e)):t.push((0,s.jsx)(n.A,{className:"w-4 h-4 text-gray-300"},e));return t})(e.averageRating)}),(0,s.jsxs)("span",{className:"text-xs text-muted-foreground",children:["(",e.averageRating.toFixed(1),")"]}),e.reviews&&(0,s.jsxs)("span",{className:"text-xs text-muted-foreground",children:[e.reviews.length," review",1!==e.reviews.length?"s":""]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{className:"flex items-center gap-2",children:(0,s.jsx)("span",{className:"text-lg font-bold text-primary",children:(a=e.price,new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(a))})}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:e.stock>0?`${e.stock} left`:"Out of stock"})]}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground line-clamp-2",children:e.description})]})]})})}},13861:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},17215:(e,t,a)=>{a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Github-Repositories\\\\mega-mall\\\\frontend\\\\src\\\\components\\\\shop\\\\ErrorBoundary.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\shop\\ErrorBoundary.tsx","default")},32192:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(62688).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},43649:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},44493:(e,t,a)=>{a.d(t,{BT:()=>n,Wu:()=>d,ZB:()=>l,Zp:()=>o,aR:()=>i,wL:()=>c});var s=a(60687);a(43210);var r=a(4780);function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...t})}function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},64398:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},66981:(e,t,a)=>{a.d(t,{default:()=>h});var s=a(60687),r=a(43210),o=a(29523),i=a(44493),l=a(43649),n=a(78122),d=a(32192);class c extends r.Component{constructor(e){super(e),this.handleRetry=()=>{this.setState({hasError:!1,error:void 0})},this.handleGoHome=()=>{window.location.href="/"},this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("Shop Error Boundary caught an error:",e,t)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,s.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center p-4",children:(0,s.jsx)(i.Zp,{className:"w-full max-w-lg",children:(0,s.jsxs)(i.Wu,{className:"flex flex-col items-center justify-center py-12 text-center space-y-6",children:[(0,s.jsx)("div",{className:"w-20 h-20 text-destructive",children:(0,s.jsx)(l.A,{className:"w-full h-full"})}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-foreground",children:"Something went wrong"}),(0,s.jsx)("p",{className:"text-muted-foreground max-w-md",children:"We encountered an unexpected error while loading the shop. This might be a temporary issue."}),!1]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,s.jsxs)(o.$,{onClick:this.handleRetry,className:"flex items-center gap-2",size:"lg",children:[(0,s.jsx)(n.A,{className:"w-4 h-4"}),"Try Again"]}),(0,s.jsxs)(o.$,{onClick:this.handleGoHome,variant:"outline",className:"flex items-center gap-2",size:"lg",children:[(0,s.jsx)(d.A,{className:"w-4 h-4"}),"Go Home"]})]})]})})}):this.props.children}}let h=c},67760:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(62688).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},70440:(e,t,a)=>{a.r(t),a.d(t,{default:()=>r});var s=a(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},78122:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},96834:(e,t,a)=>{a.d(t,{E:()=>n});var s=a(60687);a(43210);var r=a(8730),o=a(24224),i=a(4780);let l=(0,o.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function n({className:e,variant:t,asChild:a=!1,...o}){let n=a?r.DX:"span";return(0,s.jsx)(n,{"data-slot":"badge",className:(0,i.cn)(l({variant:t}),e),...o})}}};