[{"C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\admin-users\\page.tsx": "2", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\analytics\\page.tsx": "3", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\customers\\page.tsx": "4", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\layout.tsx": "5", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\messages\\page.tsx": "6", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\orders\\page.tsx": "7", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\page.tsx": "8", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\products\\page.tsx": "9", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\settings\\page.tsx": "10", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\cart\\page.tsx": "11", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\checkout\\page.tsx": "12", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\layout.tsx": "13", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\order-confirmation\\[orderId]\\page.tsx": "14", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\orders\\page.tsx": "15", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\otpVerification\\[userId]\\[newEmail]\\page.tsx": "16", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\page.tsx": "17", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\product\\[id]\\error.tsx": "18", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\product\\[id]\\loading.tsx": "19", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\product\\[id]\\not-found.tsx": "20", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\product\\[id]\\page.tsx": "21", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\profile\\page.tsx": "22", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\shop\\page.tsx": "23", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\signin\\page.tsx": "24", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\signup\\page.tsx": "25", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminDashboard.tsx": "26", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminHeader.tsx": "27", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminLayoutWrapper.tsx": "28", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminLoginPage.tsx": "29", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminProtectedRoute.tsx": "30", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminSidebar.tsx": "31", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminUserManagement.tsx": "32", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\CustomerManagement.tsx": "33", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\DataTable.tsx": "34", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\ErrorBoundary.tsx": "35", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\index.ts": "36", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\Loading.tsx": "37", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\OrderDetails.tsx": "38", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\OrderManagement.tsx": "39", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\ProductForm.tsx": "40", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\ProductManagement.tsx": "41", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\SimpleChart.tsx": "42", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\StatsCard.tsx": "43", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\cart\\CartErrorBoundary.tsx": "44", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\cart\\CartItem.tsx": "45", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\cart\\CartSummary.tsx": "46", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\cart\\EmptyCart.tsx": "47", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\cart\\index.ts": "48", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\checkout\\CheckoutPage.tsx": "49", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\checkout\\CheckoutSkeleton.tsx": "50", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\checkout\\CustomerInfoForm.tsx": "51", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\checkout\\OrderConfirmationPage.tsx": "52", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\checkout\\OrderHistoryPage.tsx": "53", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\checkout\\OrderSummary.tsx": "54", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\Footer.tsx": "55", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\home\\CategoriesShowcase.tsx": "56", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\home\\FeaturedProducts.tsx": "57", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\home\\HeroSection.tsx": "58", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\home\\HomePage.tsx": "59", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\home\\HomePageSkeleton.tsx": "60", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\home\\KeyFeatures.tsx": "61", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\home\\NewsletterSection.tsx": "62", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\home\\TestimonialsSection.tsx": "63", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\NavBar.tsx": "64", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\product\\ProductCard.tsx": "65", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\product\\ProductDetailsPage.tsx": "66", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\product\\ProductDetailsSkeleton.tsx": "67", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\product\\ProductImageGallery.tsx": "68", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\product\\ProductInfo.tsx": "69", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\product\\ProductTabs.tsx": "70", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\product\\RelatedProducts.tsx": "71", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\product\\ReviewForm.tsx": "72", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\product\\ReviewsSection.tsx": "73", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\product\\SocialShare.tsx": "74", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\profile\\AccountSettings.tsx": "75", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\profile\\OrderHistory.tsx": "76", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\profile\\PersonalInfoForm.tsx": "77", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\profile\\ProfilePictureUpload.tsx": "78", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\profile\\SecuritySettingsForm.tsx": "79", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ProtectedRoute.tsx": "80", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\shop\\EnhancedProductCard.tsx": "81", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\shop\\ErrorBoundary.tsx": "82", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\shop\\ErrorState.tsx": "83", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\shop\\LoadingGrid.tsx": "84", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\shop\\Pagination.tsx": "85", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\shop\\ProductGrid.tsx": "86", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\shop\\ShopFilters.tsx": "87", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\shop\\ShopHeader.tsx": "88", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\shop\\ShopPage.tsx": "89", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\alert.tsx": "90", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\badge.tsx": "91", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\breadcrumb.tsx": "92", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\button.tsx": "93", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\card.tsx": "94", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\dropdown-menu.tsx": "95", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\form.tsx": "96", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\input.tsx": "97", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\label.tsx": "98", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\loading.tsx": "99", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\radio-group.tsx": "100", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\select.tsx": "101", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\separator.tsx": "102", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\sheet.tsx": "103", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\skeleton.tsx": "104", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\slider.tsx": "105", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\textarea.tsx": "106", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\contexts\\admin\\AuthContext.tsx": "107", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\contexts\\AuthContext.tsx": "108", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\contexts\\CartContext.tsx": "109", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\examples\\apiUsage.ts": "110", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\lib\\axious.ts": "111", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\lib\\utils.ts": "112", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\services\\Api\\adminService.ts": "113", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\services\\Api\\cartService.ts": "114", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\services\\Api\\index.ts": "115", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\services\\Api\\messageService.ts": "116", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\services\\Api\\orderService.ts": "117", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\services\\Api\\productService.ts": "118", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\services\\Api\\userService.ts": "119", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\types\\api.ts": "120", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\types\\order.ts": "121", "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\utils\\orderUtils.ts": "122"}, {"size": 3563, "mtime": 1752426076140, "results": "123", "hashOfConfig": "124"}, {"size": 319, "mtime": 1752249723590, "results": "125", "hashOfConfig": "124"}, {"size": 846, "mtime": 1752249965644, "results": "126", "hashOfConfig": "124"}, {"size": 294, "mtime": 1752249624972, "results": "127", "hashOfConfig": "124"}, {"size": 1343, "mtime": 1752248906673, "results": "128", "hashOfConfig": "124"}, {"size": 819, "mtime": 1752249975102, "results": "129", "hashOfConfig": "124"}, {"size": 282, "mtime": 1752249419949, "results": "130", "hashOfConfig": "124"}, {"size": 137, "mtime": 1752248991298, "results": "131", "hashOfConfig": "124"}, {"size": 290, "mtime": 1752249266006, "results": "132", "hashOfConfig": "124"}, {"size": 840, "mtime": 1752249983647, "results": "133", "hashOfConfig": "124"}, {"size": 3988, "mtime": 1751865815613, "results": "134", "hashOfConfig": "124"}, {"size": 1265, "mtime": 1751955542139, "results": "135", "hashOfConfig": "124"}, {"size": 1404, "mtime": 1751562927327, "results": "136", "hashOfConfig": "124"}, {"size": 1065, "mtime": 1751955553606, "results": "137", "hashOfConfig": "124"}, {"size": 986, "mtime": 1751955563767, "results": "138", "hashOfConfig": "124"}, {"size": 5312, "mtime": 1751440446427, "results": "139", "hashOfConfig": "124"}, {"size": 1922, "mtime": 1751866130916, "results": "140", "hashOfConfig": "124"}, {"size": 2883, "mtime": 1751693966894, "results": "141", "hashOfConfig": "124"}, {"size": 165, "mtime": 1751693974487, "results": "142", "hashOfConfig": "124"}, {"size": 1998, "mtime": 1752426091700, "results": "143", "hashOfConfig": "124"}, {"size": 4171, "mtime": 1752426319534, "results": "144", "hashOfConfig": "124"}, {"size": 8635, "mtime": 1751263514711, "results": "145", "hashOfConfig": "124"}, {"size": 2908, "mtime": 1751610830842, "results": "146", "hashOfConfig": "124"}, {"size": 9574, "mtime": 1751304819154, "results": "147", "hashOfConfig": "124"}, {"size": 16019, "mtime": 1751439761389, "results": "148", "hashOfConfig": "124"}, {"size": 12359, "mtime": 1752427383890, "results": "149", "hashOfConfig": "124"}, {"size": 2922, "mtime": 1752248961425, "results": "150", "hashOfConfig": "124"}, {"size": 2093, "mtime": 1752248925426, "results": "151", "hashOfConfig": "124"}, {"size": 5278, "mtime": 1752426931029, "results": "152", "hashOfConfig": "124"}, {"size": 2138, "mtime": 1752249091843, "results": "153", "hashOfConfig": "124"}, {"size": 4408, "mtime": 1752248945551, "results": "154", "hashOfConfig": "124"}, {"size": 16338, "mtime": 1752427195441, "results": "155", "hashOfConfig": "124"}, {"size": 21065, "mtime": 1752427406097, "results": "156", "hashOfConfig": "124"}, {"size": 15876, "mtime": 1752427001370, "results": "157", "hashOfConfig": "124"}, {"size": 5147, "mtime": 1752249897875, "results": "158", "hashOfConfig": "124"}, {"size": 1188, "mtime": 1752390384598, "results": "159", "hashOfConfig": "124"}, {"size": 5977, "mtime": 1752249927472, "results": "160", "hashOfConfig": "124"}, {"size": 15803, "mtime": 1752427428008, "results": "161", "hashOfConfig": "124"}, {"size": 15449, "mtime": 1752427243155, "results": "162", "hashOfConfig": "124"}, {"size": 22010, "mtime": 1752427449263, "results": "163", "hashOfConfig": "124"}, {"size": 11608, "mtime": 1752427468293, "results": "164", "hashOfConfig": "124"}, {"size": 5925, "mtime": 1752249202527, "results": "165", "hashOfConfig": "124"}, {"size": 4017, "mtime": 1752249873527, "results": "166", "hashOfConfig": "124"}, {"size": 2873, "mtime": 1751563203172, "results": "167", "hashOfConfig": "124"}, {"size": 7273, "mtime": 1751651690686, "results": "168", "hashOfConfig": "124"}, {"size": 6370, "mtime": 1751995434509, "results": "169", "hashOfConfig": "124"}, {"size": 6407, "mtime": 1752426104806, "results": "170", "hashOfConfig": "124"}, {"size": 226, "mtime": 1751563257261, "results": "171", "hashOfConfig": "124"}, {"size": 12057, "mtime": 1752427098785, "results": "172", "hashOfConfig": "124"}, {"size": 5526, "mtime": 1751955861172, "results": "173", "hashOfConfig": "124"}, {"size": 12871, "mtime": 1752427312806, "results": "174", "hashOfConfig": "124"}, {"size": 12416, "mtime": 1752426693515, "results": "175", "hashOfConfig": "124"}, {"size": 12053, "mtime": 1752426730003, "results": "176", "hashOfConfig": "124"}, {"size": 6462, "mtime": 1751997519046, "results": "177", "hashOfConfig": "124"}, {"size": 9284, "mtime": 1752043366208, "results": "178", "hashOfConfig": "124"}, {"size": 9768, "mtime": 1752427171430, "results": "179", "hashOfConfig": "124"}, {"size": 7721, "mtime": 1752426166563, "results": "180", "hashOfConfig": "124"}, {"size": 8970, "mtime": 1752043497567, "results": "181", "hashOfConfig": "124"}, {"size": 4330, "mtime": 1752426334022, "results": "182", "hashOfConfig": "124"}, {"size": 5913, "mtime": 1751866182627, "results": "183", "hashOfConfig": "124"}, {"size": 7048, "mtime": 1752426180636, "results": "184", "hashOfConfig": "124"}, {"size": 7470, "mtime": 1752426744966, "results": "185", "hashOfConfig": "124"}, {"size": 10013, "mtime": 1752427487281, "results": "186", "hashOfConfig": "124"}, {"size": 7906, "mtime": 1751563174894, "results": "187", "hashOfConfig": "124"}, {"size": 2951, "mtime": 1752426351055, "results": "188", "hashOfConfig": "124"}, {"size": 4955, "mtime": 1751700907278, "results": "189", "hashOfConfig": "124"}, {"size": 6667, "mtime": 1751693500483, "results": "190", "hashOfConfig": "124"}, {"size": 8592, "mtime": 1751693906151, "results": "191", "hashOfConfig": "124"}, {"size": 7726, "mtime": 1752426367261, "results": "192", "hashOfConfig": "124"}, {"size": 9010, "mtime": 1752426281285, "results": "193", "hashOfConfig": "124"}, {"size": 8062, "mtime": 1752427507210, "results": "194", "hashOfConfig": "124"}, {"size": 5174, "mtime": 1752426399689, "results": "195", "hashOfConfig": "124"}, {"size": 9485, "mtime": 1751782621029, "results": "196", "hashOfConfig": "124"}, {"size": 2982, "mtime": 1751693611650, "results": "197", "hashOfConfig": "124"}, {"size": 16087, "mtime": 1751438972470, "results": "198", "hashOfConfig": "124"}, {"size": 13240, "mtime": 1751442286562, "results": "199", "hashOfConfig": "124"}, {"size": 10356, "mtime": 1751446848281, "results": "200", "hashOfConfig": "124"}, {"size": 7416, "mtime": 1751304941689, "results": "201", "hashOfConfig": "124"}, {"size": 11305, "mtime": 1751350505888, "results": "202", "hashOfConfig": "124"}, {"size": 1194, "mtime": 1751083668072, "results": "203", "hashOfConfig": "124"}, {"size": 8804, "mtime": 1752426414991, "results": "204", "hashOfConfig": "124"}, {"size": 3389, "mtime": 1751610662990, "results": "205", "hashOfConfig": "124"}, {"size": 1408, "mtime": 1751610638950, "results": "206", "hashOfConfig": "124"}, {"size": 1995, "mtime": 1751610629123, "results": "207", "hashOfConfig": "124"}, {"size": 2969, "mtime": 1751610616309, "results": "208", "hashOfConfig": "124"}, {"size": 2582, "mtime": 1752426233266, "results": "209", "hashOfConfig": "124"}, {"size": 9554, "mtime": 1752426248916, "results": "210", "hashOfConfig": "124"}, {"size": 7315, "mtime": 1752426264304, "results": "211", "hashOfConfig": "124"}, {"size": 5961, "mtime": 1752427354387, "results": "212", "hashOfConfig": "124"}, {"size": 1584, "mtime": 1751956039109, "results": "213", "hashOfConfig": "124"}, {"size": 1631, "mtime": 1751561614629, "results": "214", "hashOfConfig": "124"}, {"size": 2181, "mtime": 1751693472454, "results": "215", "hashOfConfig": "124"}, {"size": 2123, "mtime": 1750782049687, "results": "216", "hashOfConfig": "124"}, {"size": 1989, "mtime": 1751083388097, "results": "217", "hashOfConfig": "124"}, {"size": 8284, "mtime": 1750782050082, "results": "218", "hashOfConfig": "124"}, {"size": 3759, "mtime": 1751083388242, "results": "219", "hashOfConfig": "124"}, {"size": 967, "mtime": 1751083387262, "results": "220", "hashOfConfig": "124"}, {"size": 611, "mtime": 1751083388044, "results": "221", "hashOfConfig": "124"}, {"size": 911, "mtime": 1751083658627, "results": "222", "hashOfConfig": "124"}, {"size": 2295, "mtime": 1751956485171, "results": "223", "hashOfConfig": "124"}, {"size": 5632, "mtime": 1751693690995, "results": "224", "hashOfConfig": "124"}, {"size": 770, "mtime": 1751955614025, "results": "225", "hashOfConfig": "124"}, {"size": 4090, "mtime": 1750782049889, "results": "226", "hashOfConfig": "124"}, {"size": 261, "mtime": 1751956045361, "results": "227", "hashOfConfig": "124"}, {"size": 1305, "mtime": 1751610549670, "results": "228", "hashOfConfig": "124"}, {"size": 716, "mtime": 1752427278743, "results": "229", "hashOfConfig": "124"}, {"size": 2443, "mtime": 1752336909880, "results": "230", "hashOfConfig": "124"}, {"size": 2809, "mtime": 1752254250471, "results": "231", "hashOfConfig": "124"}, {"size": 5221, "mtime": 1752427332386, "results": "232", "hashOfConfig": "124"}, {"size": 6271, "mtime": 1751071014000, "results": "233", "hashOfConfig": "124"}, {"size": 1574, "mtime": 1752249157844, "results": "234", "hashOfConfig": "124"}, {"size": 166, "mtime": 1750781328403, "results": "235", "hashOfConfig": "124"}, {"size": 4444, "mtime": 1752426788934, "results": "236", "hashOfConfig": "124"}, {"size": 3676, "mtime": 1752426808542, "results": "237", "hashOfConfig": "124"}, {"size": 471, "mtime": 1751389264750, "results": "238", "hashOfConfig": "124"}, {"size": 3535, "mtime": 1751388999827, "results": "239", "hashOfConfig": "124"}, {"size": 3948, "mtime": 1752387987458, "results": "240", "hashOfConfig": "124"}, {"size": 4388, "mtime": 1751700434359, "results": "241", "hashOfConfig": "124"}, {"size": 3456, "mtime": 1751446126983, "results": "242", "hashOfConfig": "124"}, {"size": 6599, "mtime": 1752388638442, "results": "243", "hashOfConfig": "124"}, {"size": 2626, "mtime": 1751955529611, "results": "244", "hashOfConfig": "124"}, {"size": 7550, "mtime": 1751956417692, "results": "245", "hashOfConfig": "124"}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1rbw9o1", {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\admin-users\\page.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\analytics\\page.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\customers\\page.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\layout.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\messages\\page.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\orders\\page.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\page.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\products\\page.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\admin\\settings\\page.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\cart\\page.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\checkout\\page.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\order-confirmation\\[orderId]\\page.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\orders\\page.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\otpVerification\\[userId]\\[newEmail]\\page.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\product\\[id]\\error.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\product\\[id]\\loading.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\product\\[id]\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\product\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\profile\\page.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\shop\\page.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\signin\\page.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\app\\signup\\page.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminDashboard.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminHeader.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminLayoutWrapper.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminLoginPage.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminSidebar.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\AdminUserManagement.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\CustomerManagement.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\DataTable.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\index.ts", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\Loading.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\OrderDetails.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\OrderManagement.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\ProductForm.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\ProductManagement.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\SimpleChart.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\admin\\StatsCard.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\cart\\CartErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\cart\\CartItem.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\cart\\CartSummary.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\cart\\EmptyCart.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\cart\\index.ts", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\checkout\\CheckoutPage.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\checkout\\CheckoutSkeleton.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\checkout\\CustomerInfoForm.tsx", ["612"], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\checkout\\OrderConfirmationPage.tsx", ["613", "614", "615"], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\checkout\\OrderHistoryPage.tsx", ["616", "617"], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\checkout\\OrderSummary.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\home\\CategoriesShowcase.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\home\\FeaturedProducts.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\home\\HeroSection.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\home\\HomePage.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\home\\HomePageSkeleton.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\home\\KeyFeatures.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\home\\NewsletterSection.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\home\\TestimonialsSection.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\NavBar.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\product\\ProductCard.tsx", ["618"], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\product\\ProductDetailsPage.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\product\\ProductDetailsSkeleton.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\product\\ProductImageGallery.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\product\\ProductInfo.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\product\\ProductTabs.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\product\\RelatedProducts.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\product\\ReviewForm.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\product\\ReviewsSection.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\product\\SocialShare.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\profile\\AccountSettings.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\profile\\OrderHistory.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\profile\\PersonalInfoForm.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\profile\\ProfilePictureUpload.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\profile\\SecuritySettingsForm.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\shop\\EnhancedProductCard.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\shop\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\shop\\ErrorState.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\shop\\LoadingGrid.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\shop\\Pagination.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\shop\\ProductGrid.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\shop\\ShopFilters.tsx", ["619", "620"], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\shop\\ShopHeader.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\shop\\ShopPage.tsx", ["621"], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\breadcrumb.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\loading.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\radio-group.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\sheet.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\skeleton.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\slider.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\contexts\\admin\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\contexts\\CartContext.tsx", ["622"], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\examples\\apiUsage.ts", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\lib\\axious.ts", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\services\\Api\\adminService.ts", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\services\\Api\\cartService.ts", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\services\\Api\\index.ts", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\services\\Api\\messageService.ts", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\services\\Api\\orderService.ts", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\services\\Api\\productService.ts", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\services\\Api\\userService.ts", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\types\\api.ts", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\types\\order.ts", [], [], "C:\\Users\\<USER>\\Github-Repositories\\mega-mall\\frontend\\src\\utils\\orderUtils.ts", [], [], {"ruleId": "623", "severity": 1, "message": "624", "line": 103, "column": 9, "nodeType": "625", "endLine": 117, "endColumn": 4, "suggestions": "626"}, {"ruleId": "627", "severity": 2, "message": "628", "line": 20, "column": 3, "nodeType": null, "messageId": "629", "endLine": 20, "endColumn": 11}, {"ruleId": "630", "severity": 2, "message": "631", "line": 151, "column": 18, "nodeType": "632", "messageId": "633", "endLine": 151, "endColumn": 26}, {"ruleId": "630", "severity": 2, "message": "634", "line": 155, "column": 18, "nodeType": "632", "messageId": "633", "endLine": 155, "endColumn": 24}, {"ruleId": "627", "severity": 2, "message": "635", "line": 4, "column": 10, "nodeType": null, "messageId": "629", "endLine": 4, "endColumn": 19}, {"ruleId": "630", "severity": 2, "message": "636", "line": 310, "column": 24, "nodeType": "632", "messageId": "633", "endLine": 310, "endColumn": 32}, {"ruleId": "627", "severity": 2, "message": "635", "line": 13, "column": 10, "nodeType": null, "messageId": "629", "endLine": 13, "endColumn": 19}, {"ruleId": "637", "severity": 2, "message": "638", "line": 262, "column": 19, "nodeType": "639", "messageId": "640", "suggestions": "641"}, {"ruleId": "637", "severity": 2, "message": "638", "line": 262, "column": 73, "nodeType": "639", "messageId": "640", "suggestions": "642"}, {"ruleId": "623", "severity": 1, "message": "643", "line": 86, "column": 9, "nodeType": "625", "endLine": 116, "endColumn": 4}, {"ruleId": "623", "severity": 1, "message": "644", "line": 60, "column": 9, "nodeType": "625", "endLine": 78, "endColumn": 4, "suggestions": "645"}, "react-hooks/exhaustive-deps", "The 'validateForm' function makes the dependencies of useEffect Hook (at line 140) change on every render. To fix this, wrap the definition of 'validateForm' in its own useCallback() Hook.", "VariableDeclarator", ["646"], "@typescript-eslint/no-unused-vars", "'Calendar' is defined but never used.", "unusedVar", "react/jsx-no-undef", "'Download' is not defined.", "JSXIdentifier", "undefined", "'Share2' is not defined.", "'useRouter' is defined but never used.", "'Calendar' is not defined.", "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["647", "648", "649", "650"], ["651", "652", "653", "654"], "The 'applyFiltersAndSort' function makes the dependencies of useEffect Hook (at line 52) change on every render. Move it inside the useEffect callback. Alternatively, wrap the definition of 'applyFiltersAndSort' in its own useCallback() Hook.", "The 'refreshCart' function makes the dependencies of useEffect Hook (at line 58) change on every render. To fix this, wrap the definition of 'refreshCart' in its own useCallback() Hook.", ["655"], {"desc": "656", "fix": "657"}, {"messageId": "658", "data": "659", "fix": "660", "desc": "661"}, {"messageId": "658", "data": "662", "fix": "663", "desc": "664"}, {"messageId": "658", "data": "665", "fix": "666", "desc": "667"}, {"messageId": "658", "data": "668", "fix": "669", "desc": "670"}, {"messageId": "658", "data": "671", "fix": "672", "desc": "661"}, {"messageId": "658", "data": "673", "fix": "674", "desc": "664"}, {"messageId": "658", "data": "675", "fix": "676", "desc": "667"}, {"messageId": "658", "data": "677", "fix": "678", "desc": "670"}, {"desc": "679", "fix": "680"}, "Wrap the definition of 'validateForm' in its own useCallback() Hook.", {"range": "681", "text": "682"}, "replaceWithAlt", {"alt": "683"}, {"range": "684", "text": "685"}, "Replace with `&quot;`.", {"alt": "686"}, {"range": "687", "text": "688"}, "Replace with `&ldquo;`.", {"alt": "689"}, {"range": "690", "text": "691"}, "Replace with `&#34;`.", {"alt": "692"}, {"range": "693", "text": "694"}, "Replace with `&rdquo;`.", {"alt": "683"}, {"range": "695", "text": "696"}, {"alt": "686"}, {"range": "697", "text": "698"}, {"alt": "689"}, {"range": "699", "text": "700"}, {"alt": "692"}, {"range": "701", "text": "702"}, "Wrap the definition of 'refreshCart' in its own useCallback() Hook.", {"range": "703", "text": "704"}, [3113, 3480], "useCallback((): boolean => {\n    const newErrors: FormErrors = {};\n    let isValid = true;\n\n    Object.keys(formData).forEach((key) => {\n      const error = validateField(key, formData[key as keyof ShippingAddress]);\n      if (error) {\n        newErrors[key as keyof FormErrors] = error;\n        isValid = false;\n      }\n    });\n\n    setErrors(newErrors);\n    return isValid;\n  })", "&quot;", [9276, 9321], "\n                  &quot;                  &ldquo;", "&ldquo;", [9276, 9321], "\n                  &ldquo;                  &ldquo;", "&#34;", [9276, 9321], "\n                  &#34;                  &ldquo;", "&rdquo;", [9276, 9321], "\n                  &rdquo;                  &ldquo;", [9342, 9369], "&rdquo;&quot;\n                  ", [9342, 9369], "&rdquo;&ldquo;\n                  ", [9342, 9369], "&rdquo;&#34;\n                  ", [9342, 9369], "&rdquo;&rdquo;\n                  ", [1755, 2312], "useCallback(async () => {\n    if (!isAuthenticated) return;\n\n    try {\n      setIsLoading(true);\n      const response = await cartService.getCartItems();\n      if (response.success && response.data) {\n        setCart(response.data);\n      }\n    } catch {\n      // const axiosError = error as AxiosError<ApiResponse>;\n      // console.error('Error fetching cart:', axiosError);\n      // Don't show toast for initial cart fetch failures\n      // Set empty cart on error to prevent undefined issues\n      setCart(null);\n    } finally {\n      setIsLoading(false);\n    }\n  })"]