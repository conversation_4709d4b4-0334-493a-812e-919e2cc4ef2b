(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4099],{285:(e,s,r)=>{"use strict";r.d(s,{$:()=>d});var t=r(5155);r(2115);var a=r(9708),i=r(2085),n=r(9434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:s,variant:r,size:i,asChild:d=!1,...c}=e,o=d?a.DX:"button";return(0,t.jsx)(o,{"data-slot":"button",className:(0,n.cn)(l({variant:r,size:i,className:s})),...c})}},968:(e,s,r)=>{"use strict";r.d(s,{b:()=>l});var t=r(2115),a=r(3655),i=r(5155),n=t.forwardRef((e,s)=>(0,i.jsx)(a.sG.label,{...e,ref:s,onMouseDown:s=>{var r;s.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var l=n},1154:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1242:(e,s,r)=>{"use strict";r.d(s,{default:()=>S});var t=r(5155),a=r(2115),i=r(5654),n=r(285),l=r(2523),d=r(5057),c=r(6695),o=r(6126),u=r(5365),m=r(8856),x=r(4616),h=r(2318);let p=(0,r(9946).A)("crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]);var f=r(5525),v=r(7924),g=r(5339),j=r(2525),y=r(7550),b=r(8749),N=r(2657),w=r(1154),A=r(8543),k=r(3779);let C=e=>{let{admin:s,currentAdminEmail:r,onDelete:a}=e,i=s.email===r;return(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center",children:"superAdmin"===s.role?(0,t.jsx)(p,{className:"h-6 w-6 text-purple-600"}):(0,t.jsx)(f.A,{className:"h-6 w-6 text-blue-600"})}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg",children:s.fullName}),i&&(0,t.jsx)(o.E,{variant:"outline",children:"You"})]}),(0,t.jsx)("p",{className:"text-muted-foreground",children:s.email}),(0,t.jsx)(o.E,{variant:"superAdmin"===s.role?"default":"secondary",children:"superAdmin"===s.role?"Super Admin":"Admin"})]})]}),(0,t.jsx)("div",{className:"flex items-center space-x-2",children:!i&&(0,t.jsxs)(n.$,{variant:"destructive",size:"sm",onClick:a,children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-1"}),"Delete"]})})]})})})},E=e=>{let{onClose:s,onSuccess:r}=e,[o,m]=(0,a.useState)({fullName:"",email:"",password:"",role:"admin"}),[x,h]=(0,a.useState)(!1),[p,f]=(0,a.useState)(!1),[v,j]=(0,a.useState)(""),k=e=>{let{name:s,value:r}=e.target;m(e=>({...e,[s]:r})),v&&j("")},C=()=>o.fullName.trim()?o.email.trim()?!!o.password&&!(o.password.length<6)||(j("Password must be at least 6 characters long"),!1):(j("Email is required"),!1):(j("Full name is required"),!1),E=async e=>{if(e.preventDefault(),C()){f(!0),j("");try{let e=await i.ZJ.signUp(o);e.success?(A.oR.success("Admin account created successfully"),r()):j(e.message||"Failed to create admin account")}catch(r){var s,t;let e=(null==(t=r.response)||null==(s=t.data)?void 0:s.message)||"An error occurred while creating admin account";j(e),A.oR.error(e)}finally{f(!1)}}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(n.$,{variant:"ghost",size:"icon",onClick:s,children:(0,t.jsx)(y.A,{className:"h-4 w-4"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Add New Admin"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Create a new admin account with appropriate permissions"})]})]}),(0,t.jsxs)(c.Zp,{className:"max-w-md",children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{children:"Admin Account Details"}),(0,t.jsx)(c.BT,{children:"Fill in the information to create a new admin account"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("form",{onSubmit:E,className:"space-y-4",children:[v&&(0,t.jsxs)(u.Fc,{variant:"destructive",children:[(0,t.jsx)(g.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{children:"Error"}),(0,t.jsx)("p",{children:v})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.J,{htmlFor:"fullName",children:"Full Name *"}),(0,t.jsx)(l.p,{id:"fullName",name:"fullName",value:o.fullName,onChange:k,placeholder:"Enter full name",required:!0,disabled:p})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.J,{htmlFor:"email",children:"Email *"}),(0,t.jsx)(l.p,{id:"email",name:"email",type:"email",value:o.email,onChange:k,placeholder:"Enter email address",required:!0,disabled:p})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.J,{htmlFor:"password",children:"Password *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(l.p,{id:"password",name:"password",type:x?"text":"password",value:o.password,onChange:k,placeholder:"Enter password (min 6 characters)",required:!0,disabled:p}),(0,t.jsx)(n.$,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>h(!x),disabled:p,children:x?(0,t.jsx)(b.A,{className:"h-4 w-4"}):(0,t.jsx)(N.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.J,{htmlFor:"role",children:"Role *"}),(0,t.jsxs)("select",{id:"role",name:"role",value:o.role,onChange:k,className:"w-full px-3 py-2 border border-border rounded-md bg-background text-foreground",required:!0,disabled:p,children:[(0,t.jsx)("option",{value:"admin",children:"Admin"}),(0,t.jsx)("option",{value:"superAdmin",children:"Super Admin"})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-4 pt-4",children:[(0,t.jsx)(n.$,{type:"button",variant:"outline",onClick:s,disabled:p,children:"Cancel"}),(0,t.jsx)(n.$,{type:"submit",disabled:p,children:p?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(w.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating..."]}):"Create Admin"})]})]})})]})]})},S=()=>{let{admin:e}=(0,k.b)(),[s,r]=(0,a.useState)([]),[d,o]=(0,a.useState)(!0),[j,y]=(0,a.useState)(""),[b,N]=(0,a.useState)(""),[w,S]=(0,a.useState)(!1),F=async()=>{try{o(!0);let e=await i.ZJ.getAllAdmins();e.success&&e.data?r(e.data.admins):y(e.message||"Failed to load admin users")}catch(t){var e,s;let r=(null==(s=t.response)||null==(e=s.data)?void 0:e.message)||"An error occurred while loading admin users";y(r),A.oR.error(r)}finally{o(!1)}};(0,a.useEffect)(()=>{F()},[]);let _=async s=>{if(s===(null==e?void 0:e._id))return void A.oR.error("You cannot delete your own account");if(confirm("Are you sure you want to delete this admin account?"))try{let e=await i.ZJ.deleteAdmin(s);e.success?(A.oR.success("Admin account deleted successfully"),F()):A.oR.error(e.message||"Failed to delete admin account")}catch(s){var r,t;let e=(null==(t=s.response)||null==(r=t.data)?void 0:r.message)||"An error occurred while deleting admin account";A.oR.error(e)}},R=s.filter(e=>{let s=b.toLowerCase();return e.fullName.toLowerCase().includes(s)||e.email.toLowerCase().includes(s)||e.role.toLowerCase().includes(s)}),M={total:s.length,superAdmins:s.filter(e=>"superAdmin"===e.role).length,regularAdmins:s.filter(e=>"admin"===e.role).length};return w?(0,t.jsx)(E,{onClose:()=>S(!1),onSuccess:()=>{S(!1),F()}}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Admin User Management"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Manage admin accounts, create new admin users, and control access permissions."})]}),(0,t.jsxs)(n.$,{onClick:()=>S(!0),className:"flex items-center",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Add Admin"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Admins"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:M.total})]}),(0,t.jsx)(h.A,{className:"h-8 w-8 text-muted-foreground"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Super Admins"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-purple-600",children:M.superAdmins})]}),(0,t.jsx)(p,{className:"h-8 w-8 text-purple-600"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Regular Admins"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:M.regularAdmins})]}),(0,t.jsx)(f.A,{className:"h-8 w-8 text-blue-600"})]})})})]}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"pt-6",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(l.p,{placeholder:"Search admins by name, email, or role...",value:b,onChange:e=>N(e.target.value),className:"pl-10"})]})})}),j&&(0,t.jsxs)(u.Fc,{variant:"destructive",children:[(0,t.jsx)(g.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{children:"Error loading admin users"}),(0,t.jsx)("p",{children:j})]})]}),d&&(0,t.jsx)("div",{className:"space-y-4",children:Array.from({length:3}).map((e,s)=>(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.E,{className:"h-4 w-32"}),(0,t.jsx)(m.E,{className:"h-4 w-48"})]}),(0,t.jsx)(m.E,{className:"h-8 w-20"})]})})},s))}),!d&&!j&&(0,t.jsx)(t.Fragment,{children:0===R.length?(0,t.jsx)(c.Zp,{children:(0,t.jsxs)(c.Wu,{className:"p-8 text-center",children:[(0,t.jsx)(h.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No admin users found"}),(0,t.jsx)("p",{className:"text-muted-foreground mb-4",children:b?"No admin users match your search criteria.":"No admin users have been created yet."}),(0,t.jsxs)(n.$,{onClick:()=>S(!0),children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Add First Admin"]})]})}):(0,t.jsx)("div",{className:"space-y-4",children:R.map(s=>(0,t.jsx)(C,{admin:s,currentAdminEmail:(null==e?void 0:e.email)||"",onDelete:()=>_(s._id)},s._id))})})]})}},2318:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},2523:(e,s,r)=>{"use strict";r.d(s,{p:()=>i});var t=r(5155);r(2115);var a=r(9434);function i(e){let{className:s,type:r,...i}=e;return(0,t.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...i})}},2525:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2657:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3655:(e,s,r)=>{"use strict";r.d(s,{hO:()=>d,sG:()=>l});var t=r(2115),a=r(7650),i=r(9708),n=r(5155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,s)=>{let r=(0,i.TL)(`Primitive.${s}`),a=t.forwardRef((e,t)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(a?r:s,{...i,ref:t})});return a.displayName=`Primitive.${s}`,{...e,[s]:a}},{});function d(e,s){e&&a.flushSync(()=>e.dispatchEvent(s))}},3756:(e,s,r)=>{Promise.resolve().then(r.bind(r,4368)),Promise.resolve().then(r.bind(r,1242))},3779:(e,s,r)=>{"use strict";r.d(s,{AuthProvider:()=>d,b:()=>l});var t=r(5155),a=r(2115),i=r(5654);let n=(0,a.createContext)(void 0),l=()=>{let e=(0,a.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},d=e=>{let{children:s}=e,[r,l]=(0,a.useState)(null),[d,c]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(async()=>{if(localStorage.getItem("access_token"))try{let e=await i.ZJ.getAdmin();e.success&&e.data&&l(e.data)}catch(e){e.response&&(console.error("Error checking auth status:",e.response.data.message),localStorage.removeItem("access_token"))}c(!1)})()},[]);let o=async()=>{try{await i.ZJ.logout()}catch(e){console.error("Error during logout:",e)}finally{localStorage.removeItem("access_token"),l(null)}};return(0,t.jsx)(n.Provider,{value:{admin:r,isLoading:d,isAuthenticated:!!r,login:(e,s)=>{localStorage.setItem("access_token",s),l(e)},logout:o},children:s})}},4368:(e,s,r)=>{"use strict";r.d(s,{default:()=>c});var t=r(5155);r(2115);var a=r(3779),i=r(5365),n=r(5339),l=r(5525);let d=(e,s)=>"admin"===s?"admin"===e.role||"superAdmin"===e.role:"superAdmin"===s&&"superAdmin"===e.role,c=e=>{let{children:s,requiredRole:r,fallback:c}=e,{admin:o,isAuthenticated:u,isLoading:m}=(0,a.b)();return m?c||(0,t.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})}):u&&o?r&&!d(o,r)?c||(0,t.jsxs)(i.Fc,{variant:"destructive",children:[(0,t.jsx)(l.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{children:"Insufficient Permissions"}),(0,t.jsxs)("p",{children:["You need ",r," privileges to access this page. Your current role is: ",o.role]})]})]}):(0,t.jsx)(t.Fragment,{children:s}):c||(0,t.jsxs)(i.Fc,{variant:"destructive",children:[(0,t.jsx)(n.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{children:"Access Denied"}),(0,t.jsx)("p",{children:"You must be logged in as an admin to access this page."})]})]})}},4616:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5057:(e,s,r)=>{"use strict";r.d(s,{J:()=>n});var t=r(5155);r(2115);var a=r(968),i=r(9434);function n(e){let{className:s,...r}=e;return(0,t.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...r})}},5339:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5365:(e,s,r)=>{"use strict";r.d(s,{Fc:()=>d,TN:()=>c});var t=r(5155),a=r(2115),i=r(2085),n=r(9434);let l=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=a.forwardRef((e,s)=>{let{className:r,variant:a,...i}=e;return(0,t.jsx)("div",{ref:s,role:"alert",className:(0,n.cn)(l({variant:a}),r),...i})});d.displayName="Alert",a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("h5",{ref:s,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",r),...a})}).displayName="AlertTitle";let c=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",r),...a})});c.displayName="AlertDescription"},5525:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},6101:(e,s,r)=>{"use strict";r.d(s,{s:()=>n,t:()=>i});var t=r(2115);function a(e,s){if("function"==typeof e)return e(s);null!=e&&(e.current=s)}function i(...e){return s=>{let r=!1,t=e.map(e=>{let t=a(e,s);return r||"function"!=typeof t||(r=!0),t});if(r)return()=>{for(let s=0;s<t.length;s++){let r=t[s];"function"==typeof r?r():a(e[s],null)}}}}function n(...e){return t.useCallback(i(...e),e)}},6126:(e,s,r)=>{"use strict";r.d(s,{E:()=>d});var t=r(5155);r(2115);var a=r(9708),i=r(2085),n=r(9434);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:s,variant:r,asChild:i=!1,...d}=e,c=i?a.DX:"span";return(0,t.jsx)(c,{"data-slot":"badge",className:(0,n.cn)(l({variant:r}),s),...d})}},6695:(e,s,r)=>{"use strict";r.d(s,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>n,wL:()=>o});var t=r(5155);r(2115);var a=r(9434);function i(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...r})}function n(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...r})}function l(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",s),...r})}function d(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",s),...r})}function c(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",s),...r})}function o(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",s),...r})}},7550:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7924:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8749:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},8856:(e,s,r)=>{"use strict";r.d(s,{E:()=>i});var t=r(5155),a=r(9434);function i(e){let{className:s,...r}=e;return(0,t.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",s),...r})}},9708:(e,s,r)=>{"use strict";r.d(s,{DX:()=>l,TL:()=>n});var t=r(2115),a=r(6101),i=r(5155);function n(e){let s=function(e){let s=t.forwardRef((e,s)=>{let{children:r,...i}=e;if(t.isValidElement(r)){var n;let e,l,d=(n=r,(l=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(l=(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref),c=function(e,s){let r={...s};for(let t in s){let a=e[t],i=s[t];/^on[A-Z]/.test(t)?a&&i?r[t]=(...e)=>{let s=i(...e);return a(...e),s}:a&&(r[t]=a):"style"===t?r[t]={...a,...i}:"className"===t&&(r[t]=[a,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==t.Fragment&&(c.ref=s?(0,a.t)(s,d):d),t.cloneElement(r,c)}return t.Children.count(r)>1?t.Children.only(null):null});return s.displayName=`${e}.SlotClone`,s}(e),r=t.forwardRef((e,r)=>{let{children:a,...n}=e,l=t.Children.toArray(a),d=l.find(c);if(d){let e=d.props.children,a=l.map(s=>s!==d?s:t.Children.count(e)>1?t.Children.only(null):t.isValidElement(e)?e.props.children:null);return(0,i.jsx)(s,{...n,ref:r,children:t.isValidElement(e)?t.cloneElement(e,void 0,a):null})}return(0,i.jsx)(s,{...n,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var l=n("Slot"),d=Symbol("radix.slottable");function c(e){return t.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,9078,8543,7389,8441,1684,7358],()=>s(3756)),_N_E=e.O()}]);