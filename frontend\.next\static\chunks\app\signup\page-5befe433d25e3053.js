(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[879],{1007:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},5110:(e,s,a)=>{Promise.resolve().then(a.bind(a,9349))},5196:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},9349:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>b});var r=a(5155),t=a(2115),l=a(6874),c=a.n(l),i=a(5695),d=a(7550),n=a(1007),o=a(1264),m=a(2919),x=a(8749),h=a(2657),u=a(5196),p=a(285),f=a(2523),N=a(5057),y=a(6695),j=a(5654),w=a(8543),v=a(9053),g=a(9362);function b(){var e;let s,a=(0,i.useRouter)(),[l,b]=(0,t.useState)({fullName:"",email:"",password:"",confirmPassword:""}),[P,A]=(0,t.useState)(!1),[C,k]=(0,t.useState)(!1),[S,F]=(0,t.useState)(!1),[M,z]=(0,t.useState)({}),[E,H]=(0,t.useState)(!1),_=()=>{let e={};return l.fullName.trim()?l.fullName.trim().length<2&&(e.fullName="Full name must be at least 2 characters"):e.fullName="Full name is required",l.email?/\S+@\S+\.\S+/.test(l.email)||(e.email="Please enter a valid email"):e.email="Email is required",l.password?l.password.length<8?e.password="Password must be at least 8 characters":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(l.password)||(e.password="Password must contain at least one uppercase letter, one lowercase letter, and one number"):e.password="Password is required",l.confirmPassword?l.password!==l.confirmPassword&&(e.confirmPassword="Passwords do not match"):e.confirmPassword="Please confirm your password",E||(e.terms="You must accept the terms and conditions"),z(e),0===Object.keys(e).length},B=e=>{let{name:s,value:a}=e.target;b(e=>({...e,[s]:a})),M[s]&&z(e=>({...e,[s]:""}))},J=async e=>{var s,r,t,c;if(e.preventDefault(),_()){F(!0);try{let e=await j.Dv.signUp(l);if(e.success){let t=null==(s=e.data)?void 0:s.user._id,l=null==(r=e.data)?void 0:r.user.email;w.oR.success(e.message||"Account created successfully! Please check your email for verification."),a.push("/otpVerification/".concat(t,"/").concat(l))}else w.oR.error(e.message||"Sign up failed. Please try again.")}catch(e){e instanceof g.pe&&w.oR.error((null==(c=e.response)||null==(t=c.data)?void 0:t.message)||"An error occurred. Please try again.")}finally{F(!1)}}},R=(e=l.password,s=0,e.length>=8&&s++,/[a-z]/.test(e)&&s++,/[A-Z]/.test(e)&&s++,/\d/.test(e)&&s++,/[^A-Za-z0-9]/.test(e)&&s++,s),V=["bg-red-500","bg-red-400","bg-yellow-500","bg-blue-500","bg-green-500"];return(0,r.jsx)(v.A,{requireAuth:!1,children:(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-50 to-pink-100 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)(c(),{href:"/",className:"inline-flex items-center text-sm text-gray-600 hover:text-gray-900 transition-colors",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"Back to Home"]})}),(0,r.jsxs)(y.Zp,{className:"shadow-xl border-0",children:[(0,r.jsxs)(y.aR,{className:"space-y-1 text-center",children:[(0,r.jsx)(y.ZB,{className:"text-2xl font-bold text-gray-900",children:"Create Account"}),(0,r.jsx)(y.BT,{className:"text-gray-600",children:"Join Mega Mall and start shopping today"})]}),(0,r.jsxs)(y.Wu,{className:"space-y-6",children:[(0,r.jsxs)("form",{onSubmit:J,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(N.J,{htmlFor:"fullName",className:"text-sm font-medium text-gray-700",children:"Full Name"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(n.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,r.jsx)(f.p,{id:"fullName",name:"fullName",type:"text",placeholder:"Enter your full name",value:l.fullName,onChange:B,className:"pl-10 ".concat(M.fullName?"border-red-500 focus-visible:ring-red-500":""),disabled:S})]}),M.fullName&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:M.fullName})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(N.J,{htmlFor:"email",className:"text-sm font-medium text-gray-700",children:"Email Address"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(o.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,r.jsx)(f.p,{id:"email",name:"email",type:"email",placeholder:"Enter your email",value:l.email,onChange:B,className:"pl-10 ".concat(M.email?"border-red-500 focus-visible:ring-red-500":""),disabled:S})]}),M.email&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:M.email})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(N.J,{htmlFor:"password",className:"text-sm font-medium text-gray-700",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,r.jsx)(f.p,{id:"password",name:"password",type:P?"text":"password",placeholder:"Create a strong password",value:l.password,onChange:B,className:"pl-10 pr-10 ".concat(M.password?"border-red-500 focus-visible:ring-red-500":""),disabled:S}),(0,r.jsx)("button",{type:"button",onClick:()=>A(!P),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",disabled:S,children:P?(0,r.jsx)(x.A,{className:"w-4 h-4"}):(0,r.jsx)(h.A,{className:"w-4 h-4"})})]}),l.password&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"flex space-x-1",children:[1,2,3,4,5].map(e=>(0,r.jsx)("div",{className:"h-1 flex-1 rounded-full ".concat(e<=R?V[R-1]:"bg-gray-200")},e))}),(0,r.jsxs)("p",{className:"text-xs text-gray-600",children:["Password strength: ",["Very Weak","Weak","Fair","Good","Strong"][R-1]||"Very Weak"]})]}),M.password&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:M.password})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(N.J,{htmlFor:"confirmPassword",className:"text-sm font-medium text-gray-700",children:"Confirm Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,r.jsx)(f.p,{id:"confirmPassword",name:"confirmPassword",type:C?"text":"password",placeholder:"Confirm your password",value:l.confirmPassword,onChange:B,className:"pl-10 pr-10 ".concat(M.confirmPassword?"border-red-500 focus-visible:ring-red-500":""),disabled:S}),(0,r.jsx)("button",{type:"button",onClick:()=>k(!C),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",disabled:S,children:C?(0,r.jsx)(x.A,{className:"w-4 h-4"}):(0,r.jsx)(h.A,{className:"w-4 h-4"})}),l.confirmPassword&&l.password===l.confirmPassword&&(0,r.jsx)(u.A,{className:"absolute right-10 top-1/2 transform -translate-y-1/2 text-green-500 w-4 h-4"})]}),M.confirmPassword&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:M.confirmPassword})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,r.jsx)("input",{type:"checkbox",id:"terms",checked:E,onChange:e=>{H(e.target.checked),M.terms&&z(e=>({...e,terms:""}))},className:"mt-1 h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded",disabled:S}),(0,r.jsxs)("label",{htmlFor:"terms",className:"text-sm text-gray-600",children:["I agree to the"," ",(0,r.jsx)(c(),{href:"/terms",className:"text-primary hover:text-primary/80 underline",children:"Terms of Service"})," ","and"," ",(0,r.jsx)(c(),{href:"/privacy",className:"text-primary hover:text-primary/80 underline",children:"Privacy Policy"})]})]}),M.terms&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:M.terms})]}),(0,r.jsx)(p.$,{type:"submit",className:"w-full h-11 text-base font-medium",disabled:S,children:S?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Creating Account..."]}):"Create Account"})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("span",{className:"w-full border-t border-gray-300"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,r.jsx)("span",{className:"bg-white px-2 text-gray-500",children:"Or"})})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(p.$,{variant:"outline",className:"w-full h-11",disabled:S,children:[(0,r.jsxs)("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,r.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,r.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,r.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Continue with Google"]}),(0,r.jsxs)(p.$,{variant:"outline",className:"w-full h-11",disabled:S,children:[(0,r.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})}),"Continue with Facebook"]})]}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",(0,r.jsx)(c(),{href:"/signin",className:"font-medium text-primary hover:text-primary/80 transition-colors",children:"Sign in here"})]})})]})]})]})})})}},9362:(e,s,a)=>{"use strict";a.d(s,{pe:()=>t});let{Axios:r,AxiosError:t,CanceledError:l,isCancel:c,CancelToken:i,VERSION:d,all:n,Cancel:o,isAxiosError:m,spread:x,toFormData:h,AxiosHeaders:u,HttpStatusCode:p,formToJSON:f,getAdapter:N,mergeConfig:y}=a(3464).A}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,9078,8543,6874,7389,2949,8441,1684,7358],()=>s(5110)),_N_E=e.O()}]);