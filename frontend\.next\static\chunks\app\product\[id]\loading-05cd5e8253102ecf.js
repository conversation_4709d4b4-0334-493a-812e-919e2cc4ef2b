(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8351],{6695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>n,Wu:()=>t,ZB:()=>r,Zp:()=>m,aR:()=>i,wL:()=>c});var d=a(5155);a(2115);var l=a(9434);function m(e){let{className:s,...a}=e;return(0,d.jsx)("div",{"data-slot":"card",className:(0,l.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...a})}function i(e){let{className:s,...a}=e;return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,l.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...a})}function r(e){let{className:s,...a}=e;return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,l.cn)("leading-none font-semibold",s),...a})}function n(e){let{className:s,...a}=e;return(0,d.jsx)("div",{"data-slot":"card-description",className:(0,l.cn)("text-muted-foreground text-sm",s),...a})}function t(e){let{className:s,...a}=e;return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,l.cn)("px-6",s),...a})}function c(e){let{className:s,...a}=e;return(0,d.jsx)("div",{"data-slot":"card-footer",className:(0,l.cn)("flex items-center px-6 [.border-t]:pt-6",s),...a})}},6924:(e,s,a)=>{Promise.resolve().then(a.bind(a,7288))},7288:(e,s,a)=>{"use strict";a.d(s,{default:()=>m});var d=a(5155);a(2115);var l=a(6695);let m=()=>(0,d.jsx)("div",{className:"min-h-screen bg-background",children:(0,d.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-12"}),(0,d.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-1"}),(0,d.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-12"}),(0,d.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-1"}),(0,d.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-20"}),(0,d.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-1"}),(0,d.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-32"})]})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 mb-16",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("div",{className:"aspect-square bg-muted rounded-lg animate-pulse"}),(0,d.jsx)("div",{className:"flex gap-2",children:Array.from({length:4}).map((e,s)=>(0,d.jsx)("div",{className:"w-20 h-20 bg-muted rounded-md animate-pulse"},s))})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-24"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("div",{className:"h-8 bg-muted rounded animate-pulse w-full"}),(0,d.jsx)("div",{className:"h-8 bg-muted rounded animate-pulse w-3/4"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsx)("div",{className:"flex gap-1",children:Array.from({length:5}).map((e,s)=>(0,d.jsx)("div",{className:"w-5 h-5 bg-muted rounded animate-pulse"},s))}),(0,d.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-20"})]}),(0,d.jsx)("div",{className:"h-10 bg-muted rounded animate-pulse w-32"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-full"}),(0,d.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-full"}),(0,d.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-2/3"})]}),(0,d.jsx)("div",{className:"h-6 bg-muted rounded animate-pulse w-24"}),(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsx)("div",{className:"h-10 bg-muted rounded animate-pulse w-24"}),(0,d.jsx)("div",{className:"h-10 bg-muted rounded animate-pulse w-40"})]}),(0,d.jsx)("div",{className:"flex gap-2",children:Array.from({length:4}).map((e,s)=>(0,d.jsx)("div",{className:"w-10 h-10 bg-muted rounded animate-pulse"},s))})]})]}),(0,d.jsx)("div",{className:"mb-16",children:(0,d.jsx)(l.Zp,{children:(0,d.jsxs)(l.Wu,{className:"p-6",children:[(0,d.jsx)("div",{className:"flex gap-6 mb-6",children:Array.from({length:3}).map((e,s)=>(0,d.jsx)("div",{className:"h-6 bg-muted rounded animate-pulse w-20"},s))}),(0,d.jsx)("div",{className:"space-y-4",children:Array.from({length:5}).map((e,s)=>(0,d.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-full"},s))})]})})}),(0,d.jsxs)("div",{className:"mb-16",children:[(0,d.jsx)("div",{className:"h-8 bg-muted rounded animate-pulse w-48 mb-6"}),(0,d.jsx)("div",{className:"space-y-4",children:Array.from({length:3}).map((e,s)=>(0,d.jsx)(l.Zp,{children:(0,d.jsx)(l.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-start gap-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-muted rounded-full animate-pulse"}),(0,d.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,d.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-32"}),(0,d.jsx)("div",{className:"flex gap-1",children:Array.from({length:5}).map((e,s)=>(0,d.jsx)("div",{className:"w-4 h-4 bg-muted rounded animate-pulse"},s))}),(0,d.jsxs)("div",{className:"space-y-1",children:[(0,d.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-full"}),(0,d.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-3/4"})]})]})]})})},s))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"h-8 bg-muted rounded animate-pulse w-48 mb-6"}),(0,d.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:Array.from({length:4}).map((e,s)=>(0,d.jsxs)(l.Zp,{children:[(0,d.jsx)("div",{className:"aspect-square bg-muted animate-pulse"}),(0,d.jsxs)(l.Wu,{className:"p-4 space-y-2",children:[(0,d.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-full"}),(0,d.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-3/4"}),(0,d.jsx)("div",{className:"h-6 bg-muted rounded animate-pulse w-20"})]})]},s))})]})]})})},9434:(e,s,a)=>{"use strict";a.d(s,{cn:()=>m});var d=a(2596),l=a(9688);function m(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,l.QP)((0,d.$)(s))}}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,8441,1684,7358],()=>s(6924)),_N_E=e.O()}]);